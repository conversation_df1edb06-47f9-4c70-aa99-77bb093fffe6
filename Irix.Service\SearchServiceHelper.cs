﻿using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Service.Contract;
using Irix.ServiceAdapters;
using Logger.Contract;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Repricer.Cache;
using RePricer.Util;
using System.Diagnostics;
using System.Text;
using System.Text.RegularExpressions;
using commonConstants = RePricer.Constants.CommonConstant;
using constants = RePricer.Constants.ServiceConstants;
using UtilCommonConstants = Repricer.Util.Constant;

namespace Irix.Service
{
    public class SearchServiceHelper : ISearchServicerHelper
    {
        private readonly IIrixAdapter _irixAdapter;
        private readonly IReservationPersistence _reservationPersistance;
        private readonly IMasterService _masterService;
        private readonly IClientServices _clientServices;
        private readonly IClientPersistance _clientPersistance;
        private readonly IMemoryCache _memoryCache;

        private readonly IEmailService _emailService;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly ILogger _log;
        private readonly int _timeout = 30;
        private readonly string _className = nameof(SearchServiceHelper);
        private bool _isMock = false;

        public SearchServiceHelper(IIrixAdapter irixAdapter, IReservationPersistence reservationPersistence, IMasterService masterService, ILogger log, IClientServices clientService, IEmailService emailService, IExchangeRateService exchangeRateService, IMemoryCache memoryCache
            , IClientPersistance clientPersistence
            )
        {
            _irixAdapter = irixAdapter;
            _reservationPersistance = reservationPersistence;
            _masterService = masterService;
            _log = log;
            _clientServices = clientService;
            _clientPersistance = clientPersistence;
            _emailService = emailService;
            _exchangeRateService = exchangeRateService;
            _memoryCache = memoryCache;
        }

        public List<SearchRoom_Package> GetMainRoom(List<OfferInfo> offerInfoList, List<PrebookCriteria> offerInfoPrebookCriteria, int reservationId, int repricerId, bool isRoomBoard = true, bool isRoomInfo = true)
        {
            try
            {
                var mainRoomInfoList = new List<SearchRoom_Package>();

                if (offerInfoList != null && offerInfoList.Count > 0)
                {
                    foreach (var offerInfo in offerInfoList)
                    {
                        var watchroomselection = Stopwatch.StartNew();

                        var matchedRoomInfoList = new List<SearchRoom_Package>();

                        if (!GetReservationEmailStatus(reservationId, offerInfo.id, repricerId))
                        {
                            var roominfofromoffersList = new List<SearchRoom>();
                            if (offerInfoPrebookCriteria != null)
                            {
                                foreach (var prebookcriterias in offerInfoPrebookCriteria)
                                {
                                    if (prebookcriterias != null)
                                    {
                                        var rooms = offerInfo?.rooms
                                                              ?.Where
                                                               (
                                                                                            room =>

                                                                                            room != null &&
                                                                                            room.name?.ToLower() == prebookcriterias.RoomName?.ToLower()
                                                                                            && (
                                                                                                ((room?.board?.ToLower()?.Trim() ?? string.Empty)?
                                                                                                .Replace(System.Environment.NewLine, " ")?
                                                                                                .Replace("\n", " ")
                                                                                                )
                                                                                                ==
                                                                                                ((prebookcriterias?.RoomBoard?.ToLower()?.Trim() ?? string.Empty)?
                                                                                                .Replace(System.Environment.NewLine, " ")?
                                                                                                .Replace("\n", " "))
                                                                                                )
                                                                                            && (
                                                                                                    ((room?.info?.ToLower()?.Trim() ?? string.Empty)?
                                                                                                    .Replace(System.Environment.NewLine, " ")?
                                                                                                    .Replace("\n", " "))
                                                                                                    ==
                                                                                                     ((prebookcriterias?.RoomInfo?.ToLower()?.Trim() ?? string.Empty)?
                                                                                                    .Replace(System.Environment.NewLine, " ")?
                                                                                                    .Replace("\n", " "))
                                                                                                )
                                                                                        )
                                                                                        ?.ToList();

                                        if (string.IsNullOrEmpty(prebookcriterias.RoomBoard))
                                        {
                                            rooms = rooms?.Where(room => room?.board == "")?.ToList();
                                        }

                                        if (prebookcriterias.RoomInfo == null || prebookcriterias.RoomInfo == "")
                                        {
                                            rooms = rooms?.Where(room => room?.info == "")?.ToList();
                                        }

                                        if (rooms != null && rooms.Count > 0)
                                        {
                                            foreach (var room in rooms)
                                            {
                                                var roomInfo = new SearchRoom_Package
                                                {
                                                    RoomIndex = room.index,
                                                    RoomName = room.name,
                                                    RoomBoard = room.board,
                                                    RoomInfo = room.info,
                                                    PassengerCount = prebookcriterias.passengerCount,
                                                    ChildAges = ExtractIntegers(prebookcriterias.ChildAges),
                                                    SearchRoomPrice = room.price?.components?.supplier?.value,
                                                    SearchRoomCurrency = room.price?.components?.supplier?.currency,
                                                    NonRefundable = room.nonRefundable
                                                };

                                                mainRoomInfoList.Add(roomInfo);
                                            }
                                        }
                                        return mainRoomInfoList;
                                    }
                                }
                            }
                        }
                    }
                }

                return mainRoomInfoList;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "GetMainRoom",
                    MethodName = "SearchServiceHelper"
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public List<SearchRoom_Package> ExtractRoomInfo(List<OfferInfo> offerInfoList, List<PrebookCriteria> prebookcriteria, int ReservationId, int Repriceruserid, bool isRoomBoard = true, bool isRoomInfo = true)
        {
            var mainRoomInfoList = new List<SearchRoom_Package>();

            try
            {
                if (offerInfoList == null || offerInfoList.Count == 0)
                {
                    return mainRoomInfoList; // Return empty list if no offer info
                }

                foreach (var offerInfo in offerInfoList)
                {
                    try
                    {
                        if (!GetReservationEmailStatus(ReservationId, offerInfo.id, Repriceruserid))
                        {
                            if (prebookcriteria != null)
                            {
                                foreach (var prebookcriterias in prebookcriteria)
                                {
                                    try
                                    {
                                        if (prebookcriterias != null)
                                        {
                                            var rooms = offerInfo?.rooms
                                                ?.Where(room =>
                                                    room != null &&
                                                    room.name == prebookcriterias.RoomName
                                                    && (!isRoomBoard || string.IsNullOrEmpty(room.board) || string.IsNullOrEmpty(prebookcriterias.RoomBoard) || room.board.Contains(prebookcriterias.RoomBoard))
                                                    && (!isRoomInfo || string.IsNullOrEmpty(room.info) || string.IsNullOrEmpty(prebookcriterias.RoomInfo) || room.info.Contains(prebookcriterias.RoomInfo)))
                                                ?.ToList();

                                            if (string.IsNullOrEmpty(prebookcriterias.RoomBoard))
                                            {
                                                rooms = rooms?.Where(room => room?.board == "")?.ToList();
                                            }

                                            if (prebookcriterias.RoomInfo == null || prebookcriterias.RoomInfo == "")
                                            {
                                                rooms = rooms?.Where(room => room?.info == "")?.ToList();
                                            }

                                            if (rooms != null && rooms.Count > 0)
                                            {
                                                foreach (var room in rooms)
                                                {
                                                    var roomInfo = new SearchRoom_Package
                                                    {
                                                        RoomIndex = room.index,
                                                        RoomName = room.name,
                                                        RoomBoard = room.board,
                                                        RoomInfo = room.info,
                                                        PassengerCount = prebookcriterias.passengerCount,
                                                        ChildAges = ExtractIntegers(prebookcriterias.ChildAges),
                                                        SearchRoomPrice = room.price?.components?.supplier?.value,
                                                        SearchRoomCurrency = room.price?.components?.supplier?.currency,
                                                        NonRefundable = room.nonRefundable
                                                    };

                                                    mainRoomInfoList.Add(roomInfo);
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        var irixErrorEntity = new IrixErrorEntity
                                        {
                                            ClassName = "GetMainRoom",
                                            MethodName = "SearchServiceHelper"
                                        };
                                        _log.Error(irixErrorEntity, ex);
                                        throw;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = "GetMainRoom",
                            MethodName = "SearchServiceHelper"
                        };
                        _log.Error(irixErrorEntity, ex);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "GetMainRoom",
                    MethodName = "SearchServiceHelper"
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return mainRoomInfoList;
        }

        public List<SearchRoom_Package> ProcessRoomInfo(List<SearchRoom_Package> mainRoomInfoList, List<OfferInfo> offerInfoList, List<PrebookCriteria> prebookcriteria, int ReservationId)
        {
            var matchedRoomInfoList = FindMatchedRooms(mainRoomInfoList, offerInfoList);

            return matchedRoomInfoList;
        }

        public List<SearchRoom_Package> FindMatchedRooms(List<SearchRoom_Package> mainRoomInfoList, List<OfferInfo> offerInfoList)
        {
            var matchedRoomInfoList = new List<SearchRoom_Package>();

            if (mainRoomInfoList.Count > 0)
            {
                var packageInfo = offerInfoList?.SelectMany(offer => offer?.packages)
                                    .Where(p => p != null && p.Price?.components?.supplier != null)
                                    .OrderBy(p => p.Price.components.supplier.value)
                                    .FirstOrDefault();

                if (packageInfo != null)
                {
                    foreach (var package in packageInfo.PackageRooms)
                    {
                        bool packageTokenFound = false;

                        foreach (var roomlist in mainRoomInfoList)
                        {
                            if (roomlist.PassengerCount == package.occupancy.adults && roomlist.ChildAges.SequenceEqual(package.occupancy.childrenAges))
                            {
                                foreach (var room in package.roomReferences)
                                {
                                    if (room.roomCode.ToString() == roomlist.RoomIndex)
                                    {
                                        matchedRoomInfoList.Add(roomlist);
                                        packageTokenFound = true;
                                        break;
                                    }
                                }

                                if (packageTokenFound)
                                {
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            return matchedRoomInfoList;
        }

        public RoomResult GetRoomResult(PreBookCriteriaResult prebookcriteriaresult, List<SearchRoom_Package> matchedRoomInfoList, ReservationMainModel resm, SystemInfo system)
        {
            var watch = Stopwatch.StartNew();

            RoomResult roomsdetail = new RoomResult();  // Initialize the roomsdetail object

            try
            {
                roomsdetail.ReservationRoomName = string.Join("## ", prebookcriteriaresult.PreBookCriteriaList
                    .Where(pc => !string.IsNullOrEmpty(pc?.RoomName))
                    .Select(pc => pc.RoomName));
                roomsdetail.ReservationRoomBoard = string.Join("## ", prebookcriteriaresult.PreBookCriteriaList
                    .Where(pc => !string.IsNullOrEmpty(pc?.RoomBoard))
                    .Select(pc => pc.RoomBoard));
                roomsdetail.ReservationRoomInfo = string.Join("## ", prebookcriteriaresult.PreBookCriteriaList
                    .Where(pc => !string.IsNullOrEmpty(pc?.RoomInfo))
                    .Select(pc => pc.RoomInfo));

                roomsdetail.PreBookRoomName = string.Join("## ", matchedRoomInfoList
                                            .Where(room => !string.IsNullOrEmpty(room?.RoomName))
                                            .Select(room => room.RoomName));

                roomsdetail.PreBookRoomBoard = string.Join("## ", matchedRoomInfoList
                                              .Where(room => !string.IsNullOrEmpty(room?.RoomBoard))
                                              .Select(room => room.RoomBoard));
                roomsdetail.PreBookRoomInfo = string.Join("## ", matchedRoomInfoList
                                             .Where(room => !string.IsNullOrEmpty(room?.RoomInfo))
                                             .Select(room => room.RoomInfo));

                roomsdetail.PreBookRoomIndex = string.Join("## ", matchedRoomInfoList
                                             .Where(room => !string.IsNullOrEmpty(room?.RoomIndex))
                                             .Select(room => room.RoomIndex));

                roomsdetail.ReservationPassengerCount = prebookcriteriaresult.PreBookCriteriaList
                                                        .Where(pc => pc != null)
                                                        .Sum(pc => pc.passengerCount);

                roomsdetail.PreBookPassengerCount = matchedRoomInfoList
                                                        .Where(pc => pc != null)
                                                        .Sum(pc => pc.PassengerCount);

                roomsdetail.ReservationchildAges = resm.ChildrenAges;

                roomsdetail.PrebookChildAges = ConcatenateChildAges(matchedRoomInfoList
                                                                  .SelectMany(list => list.ChildAges)
                                                                  .Select(age => age.ToString())
                                                                  .ToList());

                roomsdetail.ReservationPrice = prebookcriteriaresult.IssueNet;

                roomsdetail.BookingDate = resm.BookingDate;
                roomsdetail.ReservationProviders = resm?.supplierName ?? string.Empty;
                roomsdetail.PrebookProviders = system?.Code ?? string.Empty;
                if (string.IsNullOrWhiteSpace(roomsdetail?.ReservationProviders)
                    || string.IsNullOrWhiteSpace(roomsdetail?.PrebookProviders)
                    )
                {
                    var msg = $"{prebookcriteriaresult.RepricerId.ToString("0000")}_{prebookcriteriaresult.ReservationId.ToString("0000000")} Supplier not found OriginalReservationProviders {roomsdetail?.ReservationProviders}, PrebookReservationProviders {roomsdetail?.PrebookProviders}";

                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(GetRoomResult),
                        RePricerId = prebookcriteriaresult.RepricerId,
                        ReservationId = prebookcriteriaresult.ReservationId,
                        Params = SerializeDeSerializeHelper.Serialize(new { message = msg, prebookcriteriaresult })
                    };
                    _log.Info(msg, irixErrorEntity, false, true, ConsoleColor.Red);

                    throw new Exception(msg);
                }

                roomsdetail.ReservationGiataMappingId = string.Join(", ", prebookcriteriaresult.PreBookCriteriaList
                   .Where(pc => !string.IsNullOrEmpty(pc?.giataroommappingdetail?.MappingId.ToString()))
                   .Select(pc => pc?.giataroommappingdetail?.MappingId.ToString()));

                roomsdetail.SearchGiataMappingId = string.Join(", ", matchedRoomInfoList
                                               .Where(room => !string.IsNullOrEmpty(room?.GiataMappingRoomDetail?.MappingId.ToString()))
                                               .Select(room => room?.GiataMappingRoomDetail?.MappingId.ToString()));

                roomsdetail.GiataGroupName = string.Join("## ", matchedRoomInfoList
                                             .Where(room => !string.IsNullOrEmpty(room?.GiataGroupName))
                                             .Select(room => room.GiataGroupName));

                roomsdetail.PrebookRoomBoardGroup = string.Join("## ", matchedRoomInfoList
                                             .Where(room => !string.IsNullOrEmpty(room?.RoomBoardGroupName))
                                             .Select(room => room.RoomBoardGroupName));

                roomsdetail.ReservationRoomBoardGroup = string.Join("## ", prebookcriteriaresult.PreBookCriteriaList
                                            .Where(pc => !string.IsNullOrEmpty(pc?.RoomBoardGroupName))
                                            .Select(room => room.RoomBoardGroupName));

                try
                {
                    roomsdetail.ResservationGiataPropertyName = string.Join("## ", prebookcriteriaresult.PreBookCriteriaList
                               .Where(pc => !string.IsNullOrEmpty(pc?.giataroommappingdetail?.PropertyName.ToString()))
                               .Select(pc => pc?.giataroommappingdetail?.PropertyName.ToString()));

                    roomsdetail.PreBookGiataPropertyName = string.Join("## ", matchedRoomInfoList
                                                   .Where(room => !string.IsNullOrEmpty(room?.GiataMappingRoomDetail?.PropertyName.ToString()))
                                                   .Select(room => room?.GiataMappingRoomDetail?.PropertyName.ToString()));
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = nameof(SearchServiceHelper),
                        MethodName = nameof(GetRoomResult),
                        Params = $"RePricerId: {prebookcriteriaresult.RepricerId}, ReservationId: {resm.ReservationId}"
                    };
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(SearchServiceHelper),
                    MethodName = nameof(GetRoomResult),
                    Params = $"RePricerId: {prebookcriteriaresult.RepricerId}, ReservationId: {resm.ReservationId}"
                };

                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            _log.Info($"SearchService|GetRoomResult|{elapsedTimeInSeconds} in {watch.Elapsed}");
            return roomsdetail;
        }

        public string ConcatenateChildAges(List<string> ages)
        {
            if (ages.Count == 0)
            {
                return "[]";
            }
            else
            {
                return "[" + string.Join(", ", ages) + "]";
            }
        }

        public string ConcatenateSupplier(List<string> stringlist)
        {
            return string.Join("## ", stringlist);
        }

        public CancellationPolicyResult CancellationCheck(int repricerUserId, List<CancellationPolicyReservation> cancellationPolicyByReservationId, CancellationPolicy prebookCancellationPolicy, PreBookCriteriaResult originalReservationDetails, decimal prebookPrice, ReservationMainModel reservationMainModel)
        {
            var watch = Stopwatch.StartNew();

            var cancellationPolicyResult = new CancellationPolicyResult();
            DateTime cpStartDatePlusBuffer;
            bool isCPDateInBufferDateRange = false;
            bool isCPDateInBufferDateRangeRES = false;
            bool isCPDateInBufferDateRangePRE = false;
            bool isCPDateMatch = false;
            bool isCPDateLoosen = false;
            bool isCPAmountLoosen = false;
            bool isCPInFuture = false;
            bool isCPInFutureRES = false;
            bool isCPInFuturePRE = false;
            bool isCPDaysDiffInLimit = false;
            bool isCPTypeMatched = false;
            int cpDaysGain = 0;
            int daysLimitCancellationPolicyEdgeCase = 0;
            var nowdate = DateTime.UtcNow.Date;
            var prebookCPType = string.Empty;
            var reservationCPType = string.Empty;

            if (_isMock)
            {
                var repricerId = repricerUserId;
                var reservationId = originalReservationDetails.ReservationId;
                var startTime = DateTime.UtcNow;

                #region GetBookingAction date and set it as now date for processing

                try
                {
                    var reservationRequestReport = new RepricerReportRequest
                    {
                        RepricerId = repricerId,
                        ReportType = "Optimized"
                    };
                    if (reservationId > 0)
                    {
                        reservationRequestReport = new RepricerReportRequest
                        {
                            RepricerId = repricerId,
                            ReservationId = reservationId.ToString()
                        };
                    }
                    var activeTabDashboard = _masterService.GetReservationReport(reservationRequestReport)?.Data?.FirstOrDefault();
                    if (activeTabDashboard != null && !string.IsNullOrEmpty(activeTabDashboard.OptimizationDate))
                    {
                        var optimizationDate = activeTabDashboard.OptimizationDate;
                        nowdate = DateTime.ParseExact(optimizationDate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture).Date;
                    }
                }
                catch (Exception ex)
                {
                    var logInfoEntry = new
                    {
                        RepricerId = repricerId,
                        Message = "Entered into CancellationCheck MOCK block",
                        Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                        isMultiSupplier = true,
                        OffersFound = 0,
                        reservationId,
                        Params = new
                        {
                            repricerId,
                            reservationId,
                            isUpdateDB = false,
                            isMultiSupplier = false,
                            isOptimizeTriggeredManually = false,
                            totalItems = 0,
                            currentItem = 0,
                        },
                        isPrebookCreated = false,
                        isOptimized = false,
                        Method = nameof(CancellationCheck),
                        StartTime = startTime.ToString("yyyy-MMM-dd HH:mm:ss"),

                        EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                        EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                    };

                    var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = "OPTIMIZE_TRIGGERED",
                        MethodName = nameof(CancellationCheck),
                        RePricerId = repricerId,
                        ReservationId = reservationId,
                        Params = msg
                    };
                    _log.Info(msg, irixErrorEntity, true);
                }

                #endregion GetBookingAction date and set it as now date for processing
            }

            try
            {
                var clientDetails = _clientPersistance.LoadRePricerDetail(repricerUserId)?.GetAwaiter().GetResult();
                var reservationCP = cancellationPolicyByReservationId?.FirstOrDefault();

                var prebookCP = //prebookCancellationPolicy?.policies?.FirstOrDefault(x => x.source?.ToLower()?.Trim() == "supplier") ??
                                prebookCancellationPolicy?.policies?.FirstOrDefault();

                prebookCPType = prebookCP?.type?.ToLower();
                reservationCPType = reservationCP?.CancellationType?.ToLower();
                daysLimitCancellationPolicyEdgeCase = (clientDetails?.ExtraClientDetail?.DaysLimitCancellationPolicyEdgeCase ?? 5) * -1;

                bool isUseResellerCPHourDifference = clientDetails?.ExtraClientDetail?.IsUseResellerCPHourDifference ?? false;
                var resellerCPHourDifference = clientDetails?.ExtraClientDetail?.ResellerCPHourDifference ?? 0.0M;
                var cpTimeAllowedBufferPercentage = clientDetails?.ExtraClientDetail?.CPTimeAllowedBufferPercentage ?? 0.0M;
                var isCheckProfitAfterCancellation = clientDetails?.ExtraClientDetail.IsCheckProfitAfterCancellation ?? false;
                var cpAmountAllowedBufferPercentage = clientDetails?.ExtraClientDetail.CPAmountAllowedBufferPercentage ?? 0.0M;
                var isCPAmountCheck = cpAmountAllowedBufferPercentage > 0 ? true : false;

                if (clientDetails == null
                    || reservationCP == null
                    || prebookCP == null
                    || reservationCP?.MainCancellationDate == null
                    || prebookCP?.date == null
                )
                {
                    var message = string.Format(commonConstants.DetailsNotAvailable, repricerUserId);
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        RePricerId = repricerUserId,
                        ReservationId = originalReservationDetails?.ReservationId ?? 0,
                        ClassName = "SearchServiceHelper",
                        MethodName = "CancellationCheck",
                        Params = SerializeDeSerializeHelper.Serialize(new
                        {
                            RePricerId = repricerUserId,
                            ReservationId = originalReservationDetails?.ReservationId ?? 0,
                            Message = message,
                            reservationCP = cancellationPolicyByReservationId,
                            prebookCP = prebookCancellationPolicy,
                        })
                    };

                    _log.Info(message, irixErrorEntity, true, true, ConsoleColor.Red);
                    return null;
                }
                else
                {
                    cpStartDatePlusBuffer = nowdate.AddDays(clientDetails.ExtraClientDetail.TravelDaysMinSearchInDays).Date;
                    cancellationPolicyResult.IsCancellationPolicyMatched = false;
                    cancellationPolicyResult.CancellationPolicyDate = reservationCP.CancellationDate;

                    if (cancellationPolicyByReservationId?.Count >= 0)
                    {
                        decimal prebookCpChargeInOriginalCurrency = prebookCPType != "standard"
                            && prebookCP != null
                            ? prebookPrice
                            : prebookCP?.charge?.value ?? 0.0M;

                        var prebookCpChargeCurrency = prebookCPType != "standard"
                            && prebookCP != null
                            ? originalReservationDetails?.Currency
                            : prebookCP?.charge?.currency ?? commonConstants.EUR_DefaultTo;

                        decimal reservationcharge = reservationCPType != "standard"
                            && reservationCP != null
                            ? originalReservationDetails?.IssueNet ?? 0.0M
                            : reservationCP?.CancellationCharge ?? 0.0M;

                        var reservationchargeCurrency =
                            reservationCPType != "standard"
                            && reservationCP != null
                            && prebookCpChargeInOriginalCurrency > 0
                            ? originalReservationDetails?.Currency ?? commonConstants.EUR_DefaultTo
                            : string.IsNullOrEmpty(reservationCP?.Currency) ? commonConstants.EUR_DefaultTo : reservationCP.Currency;

                        cancellationPolicyResult.Currency = reservationchargeCurrency;

                        var prebookcancellationfactor = RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerUserId, prebookCpChargeCurrency, reservationchargeCurrency));
                        var prebookConvertedCPCharges = RoundToDecimalPlaces(((decimal)prebookCpChargeInOriginalCurrency * prebookcancellationfactor));
                        var cpGainAmount = reservationcharge - prebookConvertedCPCharges;
                        var bufferedCPAmount = 0.0M;
                        try
                        {
                            if (cpAmountAllowedBufferPercentage > 0)
                            {
                                bufferedCPAmount = reservationcharge + (reservationcharge * cpAmountAllowedBufferPercentage / 100);
                                isCPAmountLoosen = bufferedCPAmount - prebookConvertedCPCharges >= 0
                                    && reservationcharge > 0
                                    && prebookConvertedCPCharges > 0
                                    ? true : false;
                            }
                            else
                            {
                                isCPAmountLoosen = reservationcharge - prebookConvertedCPCharges >= 0
                                        && reservationcharge > 0
                                        && prebookConvertedCPCharges > 0
                                        ? true : false;
                            }

                            isCPInFuture = reservationCP?.MainCancellationDate.Date > nowdate
                                && prebookCP?.date?.Date > nowdate;

                            isCPInFutureRES = reservationCP?.MainCancellationDate.Date > nowdate;
                            isCPInFuturePRE = prebookCP?.date?.Date > nowdate;

                            isCPDateMatch = prebookCP?.date?.Date == reservationCP.MainCancellationDate.Date
                                && isCPInFuture
                                ? true : false;

                            isCPDateInBufferDateRange = reservationCP?.MainCancellationDate.Date > cpStartDatePlusBuffer.Date
                                && prebookCP?.date?.Date > cpStartDatePlusBuffer.Date
                                && isCPInFuture
                                ? true : false;

                            isCPDateInBufferDateRangeRES = reservationCP?.MainCancellationDate.Date > cpStartDatePlusBuffer.Date
                                && isCPInFutureRES
                                ? true : false;

                            isCPDateInBufferDateRangePRE = prebookCP?.date?.Date > cpStartDatePlusBuffer.Date
                                && isCPInFuturePRE
                                ? true : false;

                            cpDaysGain = (prebookCP?.date?.Date - reservationCP.MainCancellationDate.Date).Value.Days;

                            DateTime reserCPDate_withoutTimezone = reservationCP.MainCancellationDate;
                            DateTime preCPDate_withoutTimezone = prebookCP?.date?.DateTime ?? DateTime.UtcNow;
                            DateTime preCPDate_withoutTimezoneWithBuffer = prebookCP?.date?.DateTime ?? DateTime.UtcNow;
                            try
                            {
                                var preCPdate = prebookCP?.date?.ToString(constants.datetypeDB);
                                var resCPdate = reservationCP.MainCancellationDate.Date.ToString(constants.datetypeDB);
                                preCPDate_withoutTimezone = DateTime.ParseExact(preCPdate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                                reserCPDate_withoutTimezone = DateTime.ParseExact(resCPdate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                                prebookCP.date = preCPDate_withoutTimezone;
                                reservationCP.MainCancellationDate = reserCPDate_withoutTimezone;

                                var reservationCPBySource = reservationMainModel.CancellationPoliciesBySource.FirstOrDefault(x => x?.Source?.ToLower()?.Trim() == "manual");

                                if (isUseResellerCPHourDifference
                                    && reservationCPBySource != null
                                //&& (prebookCP?.source?.ToLower()?.Trim() == "supplier" || string.IsNullOrEmpty(prebookCP?.source?.ToLower()?.Trim()))
                                //&& reservationCPBySource?.Source?.ToLower()?.Trim() == "supplier"
                                )
                                {
                                    if (resellerCPHourDifference > 0)
                                    {
                                        preCPDate_withoutTimezoneWithBuffer = preCPDate_withoutTimezone.AddHours(System.Convert.ToDouble(resellerCPHourDifference));
                                    }
                                    else if (cpTimeAllowedBufferPercentage > 0)
                                    {
                                        var diffInHours = System.Convert.ToDecimal((reserCPDate_withoutTimezone - preCPDate_withoutTimezone).TotalHours);
                                        var calculatedCPHourDifference = (diffInHours * cpTimeAllowedBufferPercentage) / 100;
                                        resellerCPHourDifference = calculatedCPHourDifference;
                                    }
                                }
                            }
                            catch
                            {
                            }

                            if (isUseResellerCPHourDifference)
                            {
                                isCPDateLoosen = (
                                                    preCPDate_withoutTimezoneWithBuffer.Date >= reserCPDate_withoutTimezone.Date
                                                    && preCPDate_withoutTimezoneWithBuffer >= reserCPDate_withoutTimezone
                                                 )
                                                 && isCPInFuture
                                                 ? true : false;
                            }
                            else
                            {
                                isCPDateLoosen = (
                                                    preCPDate_withoutTimezone.Date >= reserCPDate_withoutTimezone.Date
                                                 )
                                                 && isCPInFuture
                                                 ? true : false;
                            }

                            //-2 >= -10 true
                            isCPDaysDiffInLimit = cpDaysGain >= daysLimitCancellationPolicyEdgeCase;

                            isCPTypeMatched = (
                                                reservationCP?.CancellationType?.ToLower() == prebookCP?.type?.ToLower()
                                                || (reservationCP?.CancellationType?.ToLower() == "limit"
                                                        && prebookCP?.type?.ToLower() == "standard"
                                                    )
                                              )
                                                && reservationCP != null
                                                && reservationCP?.CancellationType != null
                                                && prebookCP != null
                                                && string.IsNullOrEmpty(prebookCPType) == false;

                            if (isCPTypeMatched == false)
                            {
                                cancellationPolicyResult.CancellationPolicyRemark = $"CPType Not Matched : OriginalCPType : {reservationCP?.CancellationType?.ToLower()}, PrebookCPType {prebookCPType}";
                            }
                        }
                        catch (Exception)
                        {
                            cpDaysGain = -100;
                        }

                        #region New Block for calculation with tightWithBuffer

                        if (isCPDateInBufferDateRange)
                        {
                            if (reservationcharge == prebookConvertedCPCharges && isCPDateMatch && isCPTypeMatched)
                            {
                                cancellationPolicyResult.IsCancellationPolicyMatched = true;
                                cancellationPolicyResult.cPStatus = CPBucketStatus.loose.ToString();
                                cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                                cancellationPolicyResult.daysgain = cpDaysGain;
                                cancellationPolicyResult.CancellationType = prebookCPType;
                                cancellationPolicyResult.CancellationCharge = isCheckProfitAfterCancellation ? prebookConvertedCPCharges : 0;
                            }
                            // - (CP amount check is disabled) OR (CP amount check is enabled AND amount is loosened)
                            else if ((!isCPAmountCheck || isCPAmountLoosen) && isCPDateLoosen && isCPTypeMatched)
                            {
                                cancellationPolicyResult.IsCancellationPolicyMatched = false;
                                cancellationPolicyResult.cPStatus = CPBucketStatus.loose.ToString();
                                cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                                cancellationPolicyResult.daysgain = cpDaysGain;
                                cancellationPolicyResult.CancellationType = prebookCPType;
                                cancellationPolicyResult.CancellationCharge = isCheckProfitAfterCancellation ? prebookConvertedCPCharges : 0;

                                if ((!isCPAmountCheck || isCPAmountLoosen))
                                {
                                    cancellationPolicyResult.CancellationPolicyRemark = SerializeDeSerializeHelper.Serialize(new
                                    {
                                        isCPAmountCheck,
                                        isCPAmountLoosen,
                                        reservationCPcharge = reservationcharge,
                                        prebookCPCharges = prebookConvertedCPCharges,
                                        message = "CP amount check was true."
                                    });
                                }
                                if (reservationCP?.CancellationType?.ToLower() == "limit"
                                                       && prebookCP?.type?.ToLower() == "standard"
                                )
                                {
                                    cancellationPolicyResult.CancellationPolicyRemark = SerializeDeSerializeHelper.Serialize(new
                                    {
                                        isCPAmountCheck,
                                        isCPAmountLoosen,
                                        reservationCPcharge = reservationcharge,
                                        prebookCPCharges = prebookConvertedCPCharges,
                                        message = $"CPType less restricted : OriginalCPType : {reservationCP?.CancellationType?.ToLower()}, PrebookCPType {prebookCPType}"
                                    });
                                }
                            }
                            else if (/*isCPAmountLoosen &&*/ isCPDateLoosen == false && isCPDaysDiffInLimit)
                            {
                                cancellationPolicyResult.IsCancellationPolicyMatched = false;
                                cancellationPolicyResult.cPStatus = CPBucketStatus.tightWithBuffer.ToString();
                                cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                                cancellationPolicyResult.daysgain = cpDaysGain;
                                cancellationPolicyResult.CancellationType = prebookCPType;
                                cancellationPolicyResult.CancellationCharge = isCheckProfitAfterCancellation ? prebookConvertedCPCharges : 0;
                            }
                            else if ((cpGainAmount < 0 || isCPDateLoosen == false || isCPTypeMatched == false) && isCPDaysDiffInLimit) //B4
                            {
                                cancellationPolicyResult.IsCancellationPolicyMatched = false;
                                cancellationPolicyResult.cPStatus = CPBucketStatus.tight.ToString();
                                cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                                cancellationPolicyResult.daysgain = cpDaysGain;
                                cancellationPolicyResult.CancellationType = prebookCPType;
                                cancellationPolicyResult.CancellationCharge = isCheckProfitAfterCancellation ? prebookConvertedCPCharges : 0;
                            }
                            else
                            {
                                cancellationPolicyResult.IsCancellationPolicyMatched = false;
                                cancellationPolicyResult.cPStatus = CPBucketStatus.Other.ToString();
                                cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                                cancellationPolicyResult.daysgain = cpDaysGain;
                                cancellationPolicyResult.CancellationType = prebookCPType;
                                cancellationPolicyResult.CancellationCharge = prebookConvertedCPCharges;
                                //Out of buffered range
                            }
                        }
                        //Out of buffer but profit after cancellation can be booked
                        else if (prebookCPType == "standard")
                        {
                            cancellationPolicyResult.IsCancellationPolicyMatched = false;
                            cancellationPolicyResult.cPStatus = CPBucketStatus.CancellationChargesApplicable.ToString();
                            cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                            cancellationPolicyResult.daysgain = cpDaysGain;
                            cancellationPolicyResult.CancellationType = prebookCPType;
                            cancellationPolicyResult.CancellationCharge = reservationcharge;
                            //Out of buffered range
                        }
                        else
                        {
                            cancellationPolicyResult.IsCancellationPolicyMatched = false;
                            cancellationPolicyResult.cPStatus = CPBucketStatus.OutOfCancellationBufferRange.ToString();
                            cancellationPolicyResult.matchedcancellationpolicygain = cpGainAmount;
                            cancellationPolicyResult.daysgain = cpDaysGain;
                            cancellationPolicyResult.daysgain = cpDaysGain;
                            cancellationPolicyResult.CancellationType = prebookCPType;
                            cancellationPolicyResult.CancellationCharge = reservationcharge;
                            //Out of buffered range
                        }
                        cancellationPolicyResult.ReservationCancellationDate = reservationCP.CancellationDate.ToString(constants.datetypeDB);
                        cancellationPolicyResult.ReservationCancellationChargeByPolicies = $"{reservationcharge} {reservationchargeCurrency}";
                        cancellationPolicyResult.PreBookCancellationDate = prebookCP?.date?.ToString(constants.datetypeDB);
                        cancellationPolicyResult.PreBookCancellationChargeByPolicies = $"{prebookConvertedCPCharges} {reservationchargeCurrency}";
                        cancellationPolicyResult.ReservationCancellationType = reservationCPType;
                        cancellationPolicyResult.CancellationType = reservationCPType;
                        cancellationPolicyResult.PreBookCancellationType = prebookCPType;

                        #endregion New Block for calculation with tightWithBuffer
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "SearchServiceHelper",
                    MethodName = "CancellationCheck",
                };

                _log.Error(irixErrorEntity, ex);
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            _log.Info($"SearchService|CancellationCheck|{elapsedTimeInSeconds} in {watch.Elapsed}");
            return cancellationPolicyResult;
        }

        public decimal RoundToDecimalPlaces(decimal value, int decimalPlaces = 5)
        {
            return Math.Round(value, decimalPlaces);
        }

        public int[] ExtractIntegers(string input)
        {
            // Use a regular expression to extract integers from the string
            var matches = Regex.Matches(input, @"\d+");

            // Convert the matches to an array of integers
            int[] result = matches
                .Cast<Match>()
                .Select(match => int.Parse(match.Value))
                .ToArray();

            return result;
        }

        public async Task InsertSearchSyncData(SearchHotel[] searchHotels, int Repriceruserid, int ReservationId, bool IsSearchSyncDataSave = false)
        {
            try
            {
                if (IsSearchSyncDataSave)
                {
                    //var watchInsertionOfSerachSync = Stopwatch.StartNew();

                    var SearchSyncjson = ConvertSearchSyncToJson(searchHotels);

                    await _reservationPersistance.InsertSerachSync(SearchSyncjson, Repriceruserid, ReservationId);

                    //watchInsertionOfSerachSync.Stop();
                    //var elapsedTimeInSeconds = watchInsertionOfSerachSync.Elapsed.TotalSeconds;

                    //_log.Info($"SearchService|InsertionOfSerachSync|{elapsedTimeInSeconds} {watchInsertionOfSerachSync.Elapsed}");
                }
            }
            catch (Exception ex)
            {
            }
        }

        public async Task InsertSupplierSearchSyncData(SearchHotel[] searchHotels, int Repriceruserid, int ReservationId, string JobToken, bool IsDifferentSupplierSearchSyncDataSave = false)
        {
            try
            {
                if (IsDifferentSupplierSearchSyncDataSave)
                {
                    //var watchInsertionOfSerachSync = Stopwatch.StartNew();

                    var SearchSyncjson = ConvertSearchSyncToJson(searchHotels, JobToken);

                    await _reservationPersistance.SupplierInsertSerachSync(SearchSyncjson, Repriceruserid, ReservationId);

                    //watchInsertionOfSerachSync.Stop();
                    //var elapsedTimeInSeconds = watchInsertionOfSerachSync.Elapsed.TotalSeconds;

                    //_log.Info($"SearchService|InsertionOfSerachSync|{elapsedTimeInSeconds} {watchInsertionOfSerachSync.Elapsed}");
                }
            }
            catch (Exception ex)
            {
            }
        }

        public async Task InsertIntoPrebooklog(
                                 int reservationid,
                                 int repricerid,
                                 RoomResult roomResult,
                                 string? hotelname,
                                 decimal percentageDifference,
                                 decimal amountLess,
                                 decimal prebookprice,
                                 string currency,
                                 string? criteriajson, string searchsyncjson, decimal currencyfactor)
        {
            try
            {
                await _reservationPersistance.InsertIntoPrebooklog(reservationid, repricerid, roomResult, hotelname, percentageDifference, amountLess, prebookprice, currency, criteriajson, searchsyncjson, currencyfactor);
            }
            catch (Exception ex)
            {
                // Handle the exception, log it, or perform any necessary actions
            }
        }

        public async Task InsertIntoPrebookSupplierlog(
                                 int reservationid,
                                 int repricerid,
                                 RoomResult roomResult,
                                 string? hotelname,
                                 decimal percentageDifference,
                                 decimal amountLess,
                                 decimal prebookprice,
                                 string currency,
                                 string? criteriajson, string searchsyncjson, decimal currencyfactor, string jobtoken = null)
        {
            try
            {
                await _reservationPersistance.InsertIntoPrebookSupplierlog(reservationid, repricerid, roomResult, hotelname, percentageDifference, amountLess, prebookprice, currency, criteriajson, searchsyncjson, currencyfactor, jobtoken);
            }
            catch (Exception ex)
            {
                // Handle the exception, log it, or perform any necessary actions
            }
        }

        public string ConvertSearchSyncToJson(SearchHotel[] searchHotels, string jobtoken = null)
        {
            List<object> searchHotelsJson = new List<object>();

            try
            {
                foreach (var hotel in searchHotels)
                {
                    try
                    {
                        var convertedSearchHotel = new
                        {
                            HotelIndex = hotel.index,
                            HotelName = hotel.name,
                            MinPriceValue = hotel.minPrice.value,
                            MinPriceCurrency = hotel.minPrice.currency,
                            Recommended = hotel.recommended,
                            SpecialDeal = hotel.specialDeal,
                            CreatedDate = DateTime.UtcNow,
                            JobToken = jobtoken,
                            HotelOffers = MapHotelOffersToJson(hotel.offers, hotel.index)
                        };

                        searchHotelsJson.Add(convertedSearchHotel);
                    }
                    catch (Exception ex)
                    {
                        // Handle exception for individual hotel conversion
                    }
                }
                return JsonConvert.SerializeObject(searchHotelsJson, Formatting.Indented);
            }
            catch (Exception ex)
            {
                // Handle exception for the entire list conversion
                return string.Empty;
            }
        }

        public object MapHotelOffersToJson(OfferInfo[] offers, string hotelIndex)
        {
            List<object> hotelOfferJsonArray = new List<object>();

            try
            {
                foreach (var offer in offers)
                {
                    var convertedOffer = new
                    {
                        HotelIndex = hotelIndex,
                        Id_offer = offer.id,
                        Recommended = offer.recommended,
                        SpecialDeal = offer.specialDeal,
                        MinPriceValue = offer.minPrice.value,
                        MinPriceCurrency = offer.minPrice.currency,
                        CreatedDate = DateTime.UtcNow,
                        supplier = offer.System.Code,
                        SearchRooms = MapSearchRoomToJson(offer.rooms, offer.id)
                    };

                    hotelOfferJsonArray.Add(convertedOffer);
                }

                return hotelOfferJsonArray;
            }
            catch (Exception ex)
            {
                // Handle exception for offer conversion
                return new List<object>();
            }
        }

        public object MapSearchRoomToJson(SearchRoom[] searchRooms, string Id_offer)
        {
            List<object> searchRoomJsonArray = new List<object>();

            try
            {
                foreach (var room in searchRooms)
                {
                    var convertedRoom = new
                    {
                        Id_offer = Id_offer,
                        RoomIndex = room?.index ?? null,
                        RoomName = room?.name ?? null,
                        RoomStatus = room?.status ?? null,
                        Board = room?.board ?? null,
                        BoardBasis = room?.boardBasis ?? null,
                        NonRefundable = room?.nonRefundable ?? null,
                        RateTags = room?.rateTags ?? null,
                        Info = room?.info ?? null,
                        SellingValue = room?.price?.selling?.value ?? null,
                        SellingCurrency = room?.price?.selling?.currency ?? null,
                        CreatedDate = DateTime.UtcNow,
                        CancellationDate = room?.cancellationDeadline?.date,
                        CancellationChargeValue = room?.cancellationDeadline?.charge?.value,
                        CancellationChargeCurrency = room?.cancellationDeadline?.charge?.currency
                    };

                    searchRoomJsonArray.Add(convertedRoom);
                }

                return searchRoomJsonArray;
            }
            catch (Exception ex)
            {
                // Handle exception for SearchRoom conversion
                return new List<object>();
            }
        }

        public object MapPackageToJson(Package[] packages, string Id_offer)
        {
            List<object> packageJsonArray = new List<object>();

            try
            {
                foreach (var package in packages)
                {
                    var convertedPackage = new
                    {
                        Id_offer = Id_offer,
                        PackageCode = package.PackageCode,
                        PackageToken = package.PackageToken,
                        SellingValue = package.Price.selling.value,
                        SellingCurrency = package.Price.selling.currency,
                        Complete = package.Complete,
                        PackageRooms = MapPackageRoomToJson(package.PackageRooms, package.PackageCode)
                    };

                    packageJsonArray.Add(convertedPackage);
                }

                return packageJsonArray;
            }
            catch (Exception ex)
            {
                // Handle exception for Package conversion
                return new List<object>();
            }
        }

        public object MapPackageRoomToJson(Packageroom[] packageRooms, string PackageCode)
        {
            List<object> packageRoomJsonArray = new List<object>();

            try
            {
                foreach (var packageRoom in packageRooms)
                {
                    var convertedPackageRoom = new
                    {
                        PackageCode = PackageCode,
                        PackageRoomCode = packageRoom.packageRoomCode,

                        Adults = packageRoom.occupancy.adults,
                        ChildrenAges = packageRoom.occupancy.childrenAges,
                        RoomReferences = MapRoomReferenceToJson(packageRoom.roomReferences, packageRoom.packageRoomCode)
                    };

                    packageRoomJsonArray.Add(convertedPackageRoom);
                }

                return packageRoomJsonArray;
            }
            catch (Exception ex)
            {
                // Handle exception for Packageroom conversion
                return new List<object>();
            }
        }

        public object MapRoomReferenceToJson(Roomreference[] roomReferences, string PackageRoomCode)
        {
            List<object> roomReferenceJsonArray = new List<object>();

            try
            {
                foreach (var roomReference in roomReferences)
                {
                    var convertedRoomReference = new
                    {
                        PackageRoomCode = PackageRoomCode,
                        RoomCode = roomReference.roomCode,
                        RoomToken = roomReference.roomToken,
                        Selected = roomReference.selected
                    };

                    roomReferenceJsonArray.Add(convertedRoomReference);
                }

                return roomReferenceJsonArray;
            }
            catch (Exception ex)
            {
                // Handle exception for Roomreference conversion
                return new List<object>();
            }
        }

        public void InsertPreBookClientConfiguration(SmtpConfigModel configModel, int repricerId, int reservationId, string token)
        {
            Task.Run(() =>
            {
                try
                {
                    _reservationPersistance.InsertPreBookClientConfiguration(configModel, repricerId, reservationId, token);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = nameof(SearchServiceHelper), // Replace with your class name
                        MethodName = nameof(InsertPreBookClientConfiguration),
                    };
                    _log.Error(irixErrorEntity, ex);
                }
            });
        }

        public void SetOrUpdateCacheValue(string key, object value, TimeSpan expiration)
        {
            try
            {
                if (RedisCacheHelper.KeyExists(key))
                {
                    RedisCacheHelper.KeyDelete(key);
                }

                RedisCacheHelper.Set(key, value, expiration);
            }
            catch (Exception ex)
            {
                // Log or handle the exception as needed
            }
        }

        public int GetTokenCount(int reservationId, int repriceruserid)
        {
            string key = $"RepriceremailSend_{repriceruserid}_{reservationId}";
            if (RedisCacheHelper.KeyExists(key))
            {
                string prebooktokencount = RedisCacheHelper.Get<string>(key);

                if (int.TryParse(prebooktokencount, out int tokenCount))
                {
                    return tokenCount;
                }
            }

            return 0;
        }

        public bool GetReservationEmailStatus(int reservationId, string id, int repriceruserid)
        {
            try
            {
                return false;

                #region No longer needed

                var clientDetails = _clientPersistance.LoadRePricerDetail(repriceruserid)?.GetAwaiter().GetResult();
                string key = $"RepriceremailSend_{repriceruserid}_{reservationId}";

                if (clientDetails.IsOptimizationAllowed)
                {
                    return false;
                }
                else
                if (RedisCacheHelper.KeyExists(key))
                {
                    // Check if the key exists in Redis before retrieving and parsing its value
                    string value = RedisCacheHelper.Get<string>(key);

                    if (bool.TryParse(value, out bool isEmailSend))
                    {
                        return isEmailSend;
                    }
                }

                // If the key doesn't exist or the parsing fails, return a default value
                return false;

                #endregion No longer needed
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public Template SendReservationEmail(int reservationId, ReservationMainModel reservationMain, PreBookCriteriaResult preBookCriteria, List<SearchRoom_Package> searchroom, int RePricerId, decimal? amountLess, string priceCurrency, decimal? percentageDifference, SmtpConfigModel? extraclientconfig, string? availabilityToken, decimal? searchsyncprice, PrebookResponseFromAPI packageRootobject, bool? isRoomLevelPriceMatched, string? roomLevelInfo, string? packageLevelInfo, decimal PriceDifferenceValue, int PreBookToken, CancellationPolicyResult cancellationPolicyResult, decimal cancellationamount, decimal profitaftercancellation)
        {
            try
            {
                StringBuilder ReservationInfo = new StringBuilder();
                StringBuilder ReservationRoomInfo = new StringBuilder();
                StringBuilder PreBookInfo = new StringBuilder();
                StringBuilder PreBookRoomInfo = new StringBuilder();
                StringBuilder ClientConfig = new StringBuilder();
                StringBuilder Cancellationpolicy = new StringBuilder();

                string childrenAgesString = (reservationMain.ChildrenAges.Count() == 0) ? "No child" : reservationMain.ChildrenAges.ToString();

                if (reservationMain != null)
                {
                    ReservationInfo.Append("<tr><td>" + RePricerId + "</td><td>" +
                                           reservationMain.ReservationId + "</td><td>" +
                                           reservationMain.AdultCount + "</td><td>" +
                                           childrenAgesString + "</td><td>" +
                                           reservationMain.ACComdationId + "</td><td>" +
                                           preBookCriteria.IssueNet + "</td><td>" +
                                           preBookCriteria.Currency + "</td><td>" +
                                           reservationMain.CheckIn + "</td><td>" +
                                           reservationMain.Checkout + "</td><td>" +
                                           reservationMain.LeaderNationality + "</td><td>" +
                                           reservationMain.BookingDate + "</td><td>" +
                                           reservationMain.CancellationDate + "</td></tr>");

                    ClientConfig.Append("<tr><td>" + PriceDifferenceValue + "</td><td>" +
                        extraclientconfig.PriceDifferencePercentage + "</td><td>" +
                        extraclientconfig.traveldaysmaxsearchindays + "</td><td>" +
                        extraclientconfig.traveldaysminsearchindays + "</td><td>" +
                        extraclientconfig.IsUsePercentage + "</td></tr>"
                        );
                }

                if (preBookCriteria.PreBookCriteriaList.Count() > 0)
                {
                    foreach (var roomcriteria in preBookCriteria.PreBookCriteriaList)
                    {
                        string childAgesString;

                        if (roomcriteria.ChildAges != null && roomcriteria.ChildAges.Length > 0)
                        {
                            childAgesString = string.Join(", ", roomcriteria.ChildAges);
                        }
                        else
                        {
                            childAgesString = "No child";
                        }
                        ReservationRoomInfo.Append("<tr><td>" + roomcriteria.RoomName + "</td><td>" + roomcriteria.RoomBoard + "</td><td>" + roomcriteria.RoomInfo + "</td><td>" + roomcriteria.passengerCount + "</td><td>" + childAgesString + "</td></tr>");
                    }
                }

                PreBookInfo.Append("<tr>" +
                           "<td>" + reservationId + "</td>" +
                           "<td>" + searchsyncprice + "</td>" +
                           "<td>" + amountLess + "</td>" +
                           "<td>" + percentageDifference + "</td>" +
                           "<td>" + DateTime.UtcNow + "</td>" +
                           "<td>" + packageRootobject.autocancelDate + "</td>" +
                            "<td>" + packageRootobject.cancellationPolicy.date + "</td>" +
                            "<td>" + roomLevelInfo + "</td>" +
                            "<td>" + packageLevelInfo + "</td>" +
                            "<td>" + PreBookToken + "</td>" +
                            "<td>" + isRoomLevelPriceMatched + "</td>" +
                           "</tr>");

                if (searchroom.Count() > 0)
                {
                    foreach (var room in searchroom)
                    {
                        string childAgesString;
                        string nonrefundableString;

                        if (room.ChildAges != null && room.ChildAges.Length > 0)
                        {
                            childAgesString = string.Join(", ", room.ChildAges);
                        }
                        else
                        {
                            childAgesString = "No child";
                        }

                        nonrefundableString = room.NonRefundable switch
                        {
                            true => " charge will be applied if booked and cancelled",
                            false => "at the moment of the booking no charge will be applied if booked and cancelled",
                            _ => " information is unknown, the supplier did not provide any information"
                        };

                        PreBookRoomInfo.Append("<tr>" +
                            "<td>" + room.RoomIndex + "</td>" +
                            "<td>" + room.RoomName + "</td>" +
                            "<td>" + room.RoomBoard + "</td>" +
                            "<td>" + room.RoomInfo + "</td>" +
                            "<td>" + room.PassengerCount + "</td>" +
                            "<td>" + childAgesString + "</td>" +
                            "<td>" + nonrefundableString + "</td>" +
                            "</tr>");
                    }
                }

                Cancellationpolicy.Append("<tr>" +
                           "<td>" + cancellationPolicyResult.IsCancellationPolicyMatched + "</td>" +
                           "<td>" + cancellationPolicyResult.CancellationType + "</td>" +
                           "<td>" + profitaftercancellation + "</td>" +
                           "<td>" + cancellationPolicyResult.CancellationPolicyRemark + "</td>" +
                           "<td>" + cancellationPolicyResult.cPStatus + "</td>" +
                           "<td>" + cancellationPolicyResult.daysgain + "</td>" +
                           "<td>" + cancellationPolicyResult.matchedcancellationpolicygain + "</td>" +
                           "</tr>");

                var body = GetEmailBody("EmailTemplate");
                var Reservationbody = GetEmailBody("ReservationTemplate");
                var PreBookbody = GetEmailBody("PreBookTemplate");
                if (ReservationInfo != null && ReservationInfo.Length > 0)
                {
                    body = body.Replace("{{ReservationInfo}}", ReservationInfo.ToString());
                    body = body.Replace("{{ReservationRoomInfo}}", ReservationRoomInfo.ToString());
                    body = body.Replace("{{PreBookInfo}}", PreBookInfo.ToString());
                    body = body.Replace("{{PreBookRoomInfo}}", PreBookRoomInfo.ToString());
                    body = body.Replace("{{ClientConfig}}", ClientConfig.ToString());
                    body = body.Replace("{{ReservationId}}", reservationId.ToString());
                    body = body.Replace("{{Cancellationpolicy}}", Cancellationpolicy.ToString());
                    Reservationbody = Reservationbody.Replace("{{ReservationInfo}}", ReservationInfo.ToString());
                    Reservationbody = Reservationbody.Replace("{{ReservationRoomInfo}}", ReservationRoomInfo.ToString());
                    Reservationbody = Reservationbody.Replace("{{ClientConfig}}", ClientConfig.ToString());
                    PreBookbody = PreBookbody.Replace("{{PreBookInfo}}", PreBookInfo.ToString());
                    PreBookbody = PreBookbody.Replace("{{PreBookRoomInfo}}", PreBookRoomInfo.ToString());
                    PreBookbody = PreBookbody.Replace("{{Cancellationpolicy}}", Cancellationpolicy.ToString());
                }
                var template = new Template();
                template.EmailTemplate = body;
                template.ReservationTemplate = Reservationbody;
                template.PreBookTemplate = PreBookbody;
                string emailSubject = "RePricer";
                var userEmailOptions = new UserEmailOptions
                {
                    Subject = emailSubject,
                    Body = body,
                };
                // _emailService.SendEmail(userEmailOptions, RePricerId);
                return template;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public string GetEmailBody(string TemplateName)
        {
            string basePath = AppDomain.CurrentDomain.BaseDirectory;

            string templatePath = "EmailTemplate";

            string fullPath = Path.Combine(basePath, templatePath, $"{TemplateName}.html");

            var body = File.ReadAllText(fullPath);

            return body;
        }

        public PackageRequest CreatePreBookCriteria(string PackageToken, List<string> RoomToken)
        {
            var packagerequest = new PackageRequest
            {
                packageToken = PackageToken,
                roomTokens = RoomToken
            };
            return packagerequest;
        }

        public SearchRequest CreateCriteria(ReservationMainModel resm, List<ReservationRoomModel> reservationRooms, List<string> suppliersArray, bool ismultiprebook = false)
        {
            var rooms = reservationRooms.Where(rr => rr.ReservationId == resm.ReservationId).ToList();
            //if (ismultiprebook == true)
            //{
            //    suppliersArray = GetSuppliersArray(suppliersArray, resm);
            //}
            var reservationRequest = new SearchRequest
            {
                accommodation = resm.ACComdationId.HasValue ? new List<int> { resm.ACComdationId.Value } : new List<int>(),
                checkIn = resm.CheckIn.ToString(constants.datetype),
                checkOut = resm.Checkout.ToString(constants.datetype),
                occupancy = new OccupancyInfo
                {
                    leaderNationality = resm.LeaderNationality,
                    rooms = rooms.Select(room => new RoomInfo
                    {
                        adults = room.PassengerCount,
                        childrenAges = DeserializeChildrenAges(room.ChildAges)
                    }).ToList(),
                    completeOffersOnly = rooms.Count > 1 ? true : (bool?)null
                },
                language = resm.Language ?? constants.lang,
                timeout = _timeout,
                sellingChannel = resm.SellingChannel ?? constants.sellingChannel,
                providers = suppliersArray
            };

            return reservationRequest;
        }

        public SearchRequest CreateCriteria(ReservationMainModel resm, List<ReservationRoomModel> reservationRooms)
        {
            var rooms = reservationRooms.Where(rr => rr.ReservationId == resm.ReservationId).ToList();

            var reservationRequest = new SearchRequest
            {
                accommodation = resm.ACComdationId.HasValue ? new List<int> { resm.ACComdationId.Value } : new List<int>(),
                checkIn = resm.CheckIn.ToString(constants.datetype),
                checkOut = resm.Checkout.ToString(constants.datetype),
                occupancy = new OccupancyInfo
                {
                    leaderNationality = resm.LeaderNationality,
                    rooms = rooms.Select(room => new RoomInfo
                    {
                        adults = room.PassengerCount,
                        childrenAges = DeserializeChildrenAges(room.ChildAges)
                    }).ToList(),
                    completeOffersOnly = rooms.Count > 1 ? true : (bool?)null
                },
                language = resm.Language ?? constants.lang,
                timeout = _timeout,
                sellingChannel = resm.SellingChannel ?? constants.sellingChannel,
                providers = GetSuppliersArray(resm)
            };

            return reservationRequest;
        }

        public List<int> DeserializeChildrenAges(string childrenAgesJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<List<int>>(childrenAgesJson);
            }
            catch (System.Text.Json.JsonException)
            {
                return new List<int>();
            }
        }

        public List<string> GetSuppliersArray(ReservationMainModel resm)
        {
            if (resm.supplierName != null || resm.supplierName != "null")
            {
                return new List<string> { resm.supplierName };
            }
            else
            {
                return null;
            }
        }

        public List<string> GetSuppliersArray(List<string> suppliersArray, ReservationMainModel resm)
        {
            try
            {
                if (!string.IsNullOrEmpty(resm.supplierName) && resm.supplierName != "null")
                {
                    suppliersArray.RemoveAll(s => s.Equals(resm.supplierName, StringComparison.OrdinalIgnoreCase));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(SearchServiceHelper),
                    MethodName = nameof(GetSuppliersArray),
                };
                _log.Error(irixErrorEntity, ex);
            }

            return suppliersArray;
        }

        public bool IsPriceThreshold(decimal originalReservationPrice, decimal profitAmount, RePricerDetail rePricerDetail, PreBookCriteriaResult preBookCriteriaResult
        , int reservationId = 0
        , PreBookResponseResult preBookResponseResult = null
        )
        {
            var isPriceThreshold = false;
            try
            {
                var repricerId = rePricerDetail.RepricerUserID;
                var extraClientConfig = _clientServices.GetClientEmail(repricerId);
                var profit = profitAmount;//RoundToDecimalPlaces(originalReservationPrice - profitAmount);
                var profitperc = RoundToDecimalPlaces((profit / originalReservationPrice) * 100);
                var pricethresholdInOriginalReservationCurrency = extraClientConfig.PriceDifferenceValue;
                if (extraClientConfig != null && preBookCriteriaResult != null)
                {
                    if ((extraClientConfig?.PriceDifferenceValue) != 0)
                    {
                        var ClientFactor = RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, extraClientConfig.pricedifferencecurrency, preBookCriteriaResult.Currency));
                        if (ClientFactor != 0)
                        {
                            pricethresholdInOriginalReservationCurrency = RoundToDecimalPlaces(ClientFactor * extraClientConfig.PriceDifferenceValue);
                        }
                    }
                    if (extraClientConfig?.IsUsePercentage == true
                        && profitperc >= extraClientConfig.PriceDifferencePercentage
                    )
                    {
                        isPriceThreshold = true;
                    }
                    else
                    {
                        if (extraClientConfig?.IsUsePercentage != true
                            && profit >= pricethresholdInOriginalReservationCurrency

                        )
                        {
                            isPriceThreshold = true;
                        }
                    }
                    if (!(profit >= UtilCommonConstants.ProfitMinimumGlobalCheck))
                    {
                        isPriceThreshold = false;
                    }
                    if (!isPriceThreshold)
                    {
                        if (preBookResponseResult == null)
                        {
                            preBookResponseResult = new PreBookResponseResult();
                        }
                        preBookResponseResult.Status = new Dictionary<int, string>
                        {
                            {
                                reservationId, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString()
                            }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(SearchServiceHelper),
                    MethodName = nameof(IsPriceThreshold),
                };
                _log.Error(irixErrorEntity, ex);
            }

            return isPriceThreshold;
        }

        public bool IsOptimizationAlreadyRunning(int repricerId, int reservationId, bool isSameSupplier, bool isRemove = false, bool isCheckAndSet = true)
        {
            var key = $"OptimizationQueueItem_{repricerId.ToString("0000")}_{reservationId.ToString("0000000000")}";
            bool isAlreadyRunning = false;

            try
            {
                if (isRemove)
                {
                    RedisCacheHelper.Remove(key);
                    //Console.WriteLine($"Removed cache entry for {key}");
                }
                else
                {
                    isAlreadyRunning = RedisCacheHelper.Get<bool>(key);

                    if (!isAlreadyRunning)
                    {
                        if (isCheckAndSet)
                        {
                            RedisCacheHelper.Set(key, true, TimeSpan.FromMinutes(5));
                            //Console.WriteLine($"Set cache entry for {key}, optimization now running.");
                        }
                    }
                    else
                    {
                        var supplier = isSameSupplier ? "SAME_SUPPLIER" : "MULTI_SUPPLIER";
                        ConsoleColor consoleColor = !isSameSupplier switch
                        {
                            false => ConsoleColor.Cyan,
                            true => ConsoleColor.Magenta,
                        };
                        if (isCheckAndSet)
                        {
                            consoleColor = ConsoleColor.DarkCyan;
                        }
                        var messageLog = $"{DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss")}\tOptimization ALREADY RUNNING {key}\t{supplier}";
                        Console.ForegroundColor = consoleColor;
                        Console.WriteLine(messageLog);
                        Console.ResetColor();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log any errors that occur during Redis operations
                Console.WriteLine($"Error while accessing Redis: {ex.Message}");
                return false;  // Return false in case of an error
            }

            return isAlreadyRunning;
        }

        public async Task<bool> CheckIfOptimizationIsRunningAsync(int repricerId, bool isRemove = false, string job = "", bool isAnyReservationsToOptimize = true, bool isCheckAndCreate = true)
        {
            var key = $"_IsOptimizationAlreadyRunning_{repricerId:000}_{job}";
            bool isRunning = false;
            var timeSpan = TimeSpan.FromMinutes(30);

            if (!isAnyReservationsToOptimize)
            {
                timeSpan = TimeSpan.FromHours(2);
                RedisCacheHelper.Set(key, true, timeSpan);
                Console.WriteLine($"Nothing to Optimize {key}");

                // Use async delay to prevent blocking the thread
                await Task.Delay(timeSpan);
                isRunning = true;
                return isRunning;
            }
            else
            {
                if (isRemove)
                {
                    RedisCacheHelper.Remove(key);
                    var msg = $"{DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss")}\tOptimization END     RUNNING {key}\t{job}";
                }
                else
                {
                    isRunning = RedisCacheHelper.Get<bool>(key);

                    if (!isRunning)
                    {
                        if (isCheckAndCreate)
                        {
                            RedisCacheHelper.Set(key, true, timeSpan);
                            var msg = $"{DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss")}\tOptimization START   RUNNING {key}\t{job}";
                        }
                        isRunning = false;
                    }
                    else
                    {
                        isRunning = true;
                        var msg = $"{DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss")}\tOptimization ALREADY RUNNING {key}\t{job}";
                    }
                }
            }
            return isRunning;
        }

        public async Task<List<OptimizeAsyncBooking>> GetOptimizationRequests(int repricerId, string jobId)
        {
            var optimizationRequests = new List<OptimizeAsyncBooking>();
            var isAnyReservationsToOptimize = true;
            try
            {
                var optimizationRequestsNotOptimizable = await _log.GetUnoptimizedBookingsAsync(repricerId);

                if (optimizationRequests?.Any() != true)
                {
                    var searchCriterias = await _reservationPersistance.GetReservationsAsync(repricerId, 0, false);
                    optimizationRequests = searchCriterias.Select(x => new OptimizeAsyncBooking
                    {
                        RepricerId = repricerId,
                        ReservationId = x.ReservationId,
                    }).ToList();

                    isAnyReservationsToOptimize = optimizationRequests.Count > 0;
                    if (!isAnyReservationsToOptimize)
                    {
                        await CheckIfOptimizationIsRunningAsync(repricerId, false, jobId, isAnyReservationsToOptimize, false);
                    }
                }
                if (optimizationRequestsNotOptimizable?.Any() == true)
                {
                    optimizationRequests = optimizationRequests
                                             .Where(rq => !optimizationRequestsNotOptimizable
                                                          .Any(no => no.RepricerId == rq.RepricerId
                                                                  && no.ReservationId == rq.ReservationId
                                                                  && no.IsOptimized == true))
                                             .ToList();
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = jobId,
                    RePricerId = repricerId,
                };
                _log.Error(irixErrorEntity, ex);
            }
            return optimizationRequests;
        }

        public bool IsEmailAlreadySent(int repricerId, int reservationId)
        {
            var currentDate = DateTime.Now.ToString("yyyyMMdd"); // Format as YYYYMMDD for the cache key
            var key = $"EmailAlreadySent_{currentDate}_{repricerId.ToString("0000")}_{reservationId.ToString("0000000000")}";
            bool isAlreadyRunning = false;

            try
            {
                isAlreadyRunning = RedisCacheHelper.Get<bool>(key);

                if (!isAlreadyRunning)
                {
                    RedisCacheHelper.Set(key, true, TimeSpan.FromHours(12));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error while accessing Redis: {ex.Message}");
                return false;
            }

            return isAlreadyRunning;
        }

        public List<CancellationPolicyReservation> GetMissingCancellationPolicyReservations(int repricerId, int reservationId, List<CancellationPolicyReservation> cancellationPolicies, ReservationMainModel reservation)
        {
            var cancellationpolicybyreservationid = cancellationPolicies
                                                    ?.Where(cp => cp?.ReservationId == reservationId)
                                                    ?.ToList();

            try
            {
                if (cancellationpolicybyreservationid?.Any() == false && reservation?.CancellationPoliciesBySource?.Any() == true)
                {
                    try
                    {
                        var resCPbySource = reservation?.CancellationPoliciesBySource?.OrderBy(x => x?.CancellationPolicyStartDate)?.FirstOrDefault();
                        if (resCPbySource != null)
                        {
                            cancellationpolicybyreservationid = new List<CancellationPolicyReservation>();
                            var date_withoutTimezone = DateTime.ParseExact(resCPbySource.CancellationPolicyStartDate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                            var days = Convert.ToInt32((date_withoutTimezone - DateTime.UtcNow).TotalDays.ToString("F0"));

                            if (days > 1)
                            {
                                var cp = new CancellationPolicyReservation
                                {
                                    CancellationCharge = resCPbySource.CancellationCharge,
                                    CancellationDate = date_withoutTimezone,
                                    CancellationType = resCPbySource.CancellationPolicyType,
                                    Currency = resCPbySource.Currency,
                                    ReservationId = reservationId,
                                    MainCancellationDate = date_withoutTimezone,
                                    DaysDifference = Convert.ToInt32((date_withoutTimezone - DateTime.UtcNow).TotalDays.ToString("F0"))
                                };
                                if (resCPbySource.CancellationPolicyType?.ToLower() == "limit")
                                {
                                    var extraClientConfig = _clientPersistance.LoadRePricerDetail(repricerId).GetAwaiter().GetResult();
                                    var currency = extraClientConfig?.ExtraClientDetail?.Currency ?? "EUR";
                                    cp.Currency = currency;
                                }
                                cancellationpolicybyreservationid.Add(cp);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetMissingCancellationPolicyReservations),
                    RePricerId = repricerId,
                };
                _log.Error(irixErrorEntity, ex);
            }
            return cancellationpolicybyreservationid;
        }
    }
}