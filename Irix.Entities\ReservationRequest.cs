﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Irix.Entities
{
    public class ReservationRequest
    {
        public string? Id
        {
            get; set;
        }

        public string? ClientRef
        {
            get; set;
        }

        public string? Service
        {
            get; set;
        }

        public bool? Archived
        {
            get; set;
        }

        public string? SupplierConfirmationNumber
        {
            get; set;
        }

        public string? System
        {
            get; set;
        }

        public long? UserId
        {
            get; set;
        }

        public string? PaymentCommitment
        {
            get; set;
        }

        public string? PaymentStatus
        {
            get; set;
        }

        public long? OfficeId
        {
            get; set;
        }

        public string? PaxName
        {
            get; set;
        }

        public string? CancelledBy
        {
            get; set;
        }

        public string? CustomSystem
        {
            get; set;
        }

        public string? AgentRef
        {
            get; set;
        }

        public string? BosRef
        {
            get; set;
        }

        public string? CancelPenalty
        {
            get; set;
        }

        public string? CancellationPolicyDate
        {
            get; set;
        }

        public string? AutocancelDate
        {
            get; set;
        }

        public string? ProformaNo
        {
            get; set;
        }

        public string? SupplierName
        {
            get; set;
        }

        public string? VoucherGenerated
        {
            get; set;
        }

        public string? UserComments
        {
            get; set;
        }

        public string? PaidOperations
        {
            get; set;
        }

        public string? Source
        {
            get; set;
        }

        public string? Status
        {
            get; set;
        }

        public string? Text
        {
            get; set;
        }

        public string? ServiceDate
        {
            get; set;
        }

        /// <summary>
        /// string type of date
        /// Example: reservationDate=2021-01-01|2021-01-10
        /// Filter reservations by add date
        /// </summary>
        public string? ReservationDate
        {
            get; set;
        }

        public long? DestinationCityId
        {
            get; set;
        }

        public long? DestinationCountryId
        {
            get; set;
        }

        public long? HotelId
        {
            get; set;
        }

        public int? PerPage
        {
            get; set;
        }

        public int? Page
        {
            get; set;
        }

        public string? OrderField
        {
            get; set;
        }

        public string? OrderDirection
        {
            get; set;
        }

        public bool IsTotalCount
        {
            get; set;
        }
    }

    public enum CancelPenalty
    {
        yes = 1,
        No = 0
    }

    public class RepricerReportRequest : CommonReportRequest
    {
        public string? ReservationId
        {
            get; set;
        }

        /// <summary>
        /// OK (Confirm), XX (Cancelled)
        /// </summary>
        public string? ReservationStatus
        {
            get; set;
        }

        public string? PreBookFromDate
        {
            get; set;
        }

        public string? PreBookToDate
        {
            get; set;
        }

        public string? BookingFromDate
        {
            get; set;
        }

        public string? BookingToDate
        {
            get; set;
        }

        public string? CheckInFromDate
        {
            get; set;
        }

        public string? CheckInToDate
        {
            get; set;
        }

        public decimal? ProfitFrom
        {
            get; set;
        }

        public decimal? ProfitTo
        {
            get; set;
        }

        public int? PageNumber
        {
            get; set;
        }

        public int? PageSize
        {
            get; set;
        }

        /// <summary>
        /// Supplier Filters comma separated like travelgatex_dedge, dotw, expediarapid
        /// </summary>
        public string? Suppliers
        {
            get; set;
        }

        public int? CPDaysGain
        {
            get; set;
        }

        /// <summary>
        /// True means All open/current recommendation where check in date not yet passed.
        /// False means all recommendation despite check in date passed or not.
        /// </summary>
        public bool IsPrebookLive
        {
            get; set;
        }

        /// <summary>
        /// True means recommendation where action taken = Y
        /// False means recommendation where action taken <> Y
        /// NULL means recommendation where action taken not taken
        /// </summary>
        public bool? IsReservationActionTaken
        {
            get; set;
        }
        public bool IsAdditionalPrebookOptionsRequested
        {
            get; set;
        }

        /// <summary>
        /// Set it to true to Mock AdditionalPrebook if nothing is coming
        /// </summary>
        public bool IsMockAdditionalPrebook
        {
            get; set;
        }
    }
}