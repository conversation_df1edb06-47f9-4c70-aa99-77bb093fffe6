﻿using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Service.Contract;
using Irix.ServiceAdapters;
using Logger.Contract;
using Microsoft.Extensions.Caching.Memory;
using Repricer.Cache;
using RePricer.Constants;
using RePricer.Util;
using System.Collections.Concurrent;
using System.Diagnostics;
using Constant = RePricer.Constants.PersistanceConstant;
using constants = RePricer.Constants.ServiceConstants;
using UtilCommonConstants = Repricer.Util.Constant;

namespace Irix.Service
{
    public class SearchService : ISearchService
    {
        private readonly IIrixAdapter _irixAdapter;
        private readonly IReservationPersistence _reservationPersistance;
        private readonly IMasterService _masterService;
        private readonly IClientServices _clientServices;
        private readonly IMemoryCache _memoryCache;

        private readonly IEmailService _emailService;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly ILogger _log;
        private readonly ISearchServicerHelper _searchserviceHelper;
        private readonly IOptimizationService _optimizationService;
        private readonly IClientPersistance _clientPersistance;
        private readonly IDryRunOptimizationService _dryRunOptimizationService;
        private readonly IOptimizationHelper _optimizationHelper;
        private string _className = nameof(SearchService);
        private bool _isMock = false;
        private bool _isSameSupplier = true;

        public SearchService(IIrixAdapter irixAdapter, IReservationPersistence reservationPersistance, IMasterService masterService, ILogger log, IClientServices clientService, IEmailService emailService, IExchangeRateService exchangeRateService, IMemoryCache memoryCache,
            ISearchServicerHelper searchServicerHelper
            , IOptimizationService optimizationService
            , IClientPersistance clientPersistence
            , IDryRunOptimizationService dryRunOptimizationService
            , IOptimizationHelper optimizationHelper)
        {
            _irixAdapter = irixAdapter;
            _reservationPersistance = reservationPersistance;
            _masterService = masterService;
            _log = log;
            _clientServices = clientService;
            _emailService = emailService;
            _exchangeRateService = exchangeRateService;
            _memoryCache = memoryCache;
            _searchserviceHelper = searchServicerHelper;
            _optimizationService = optimizationService;
            _clientPersistance = clientPersistence;
            _optimizationHelper = optimizationHelper;
            _dryRunOptimizationService = dryRunOptimizationService;
            _className = nameof(SearchService);
        }

        /// <summary>
        /// Search and prebook for qualified reservations. Qualified are the ones returned by  _reservationPersistence.GetReservationsAsync(repricerId). These fulfill client settings criteria
        /// </summary>
        /// <param name="repricerId"></param>
        /// <param name="reservationId"></param>
        /// <returns></returns>
        public async Task<PreBookResults> _1_PrebookAndOptimize_SameSupplier_Automatic(int repricerId, int? reservationId = 0, bool isUpdateDB = true, bool isMultiSupplier = false, int totalItems = 0, int currentItem = 0, bool isOptimizeTriggeredManually = false)
        {
            List<int> reservationIds = new List<int>();
            List<int> reservationIdsInput = new List<int>();

            var prebookresultresp = new List<PreBookResponseResult>();
            var prebookresult = new PreBookResults();
            var lockObject = new object();
            prebookresult.PreBookResponseResults = new List<PreBookResponseResult>();
            var _method = nameof(_1_PrebookAndOptimize_SameSupplier_Automatic);
            bool isAnyOptimized = false;
            bool isCurrentPrebookOptimized = false;
            var method = $"_6_SAME_SUPPLIER";
            try
            {
                #region Try Block

                Guid newGuidmain = Guid.NewGuid();
                string steptoken = newGuidmain.ToString();
                List<ReservationPrebookCount> reservationids_maxcount = null;
                var repricerSchedules = _clientServices.GetClientScheduler()?.FirstOrDefault(x => x.RepricerId == repricerId);
                var extraClientConfig = _clientServices.GetClientEmail(repricerId);
                var repricerName = repricerSchedules.RepricerName;

                #region Get Dashboard Items

                if (
                       (repricerSchedules?.IsJobsEnable ?? false) == false
                    || (repricerSchedules?.IsActive ?? false) == false
                )
                {
                    var preBookResponseResult = new PreBookResponseResult
                    {
                        RepricerId = repricerId,
                        ReservationId = reservationId ?? 0,
                        Repricername = extraClientConfig?.RepricerUserName
                    };

                    preBookResponseResult = UpdateErrorAndReason(repricerId, 0, preBookResponseResult
                                                                                , OptimizationStatusEnum.MultiSupplierNotSETforTheClient
                                                                                , ReasonCode.MultiSupplierNotSETforTheClient);
                    prebookresult.PreBookResponseResults.Add(preBookResponseResult);
                    return prebookresult;
                }

                #endregion Get Dashboard Items

                var preBookResponseResults = new ConcurrentBag<PreBookResponseResult>();
                var watchCriteria = Stopwatch.StartNew();
                var searchCriterias = await _reservationPersistance.GetReservationsAsync(repricerId, reservationId ?? 0);
                var roomReservations = await _reservationPersistance.GetReservationsRoomAsync(repricerId);
                var reservationPrebookCounts = await _reservationPersistance.GetPrebookReservationIdsAsync(repricerId);
                var cancellationPolicies = await _reservationPersistance.GetCancellationPolicyReservationIdsAsync(repricerId, reservationId);
                var repricerClientDetail = _clientServices.GetRePricerDetail(repricerId);

                if ((repricerSchedules?.IsJobsEnable ?? false) == false
                || (repricerSchedules?.IsActive ?? false) == false
                )
                {
                    var preBookResponseResult = new PreBookResults
                    {
                        PreBookResponseResults = new List<PreBookResponseResult>
                        {
                            new PreBookResponseResult
                            {
                                RepricerId = repricerId,
                                ReservationId = reservationId ?? 0,
                            }
                        },

                        Error = new Error { Message = ReasonCode.OptimizationNotAllowed.ToString(), StatusCode = System.Net.HttpStatusCode.BadRequest }
                    };

                    return preBookResponseResult;
                }

                #region Run for particular ReservationId

                if (reservationId > 0)
                {
                    searchCriterias = searchCriterias?.Where(x => x.ReservationId == reservationId).ToList();
                    roomReservations = roomReservations?.Where(x => x.ReservationId == reservationId).ToList();
                    reservationPrebookCounts = reservationPrebookCounts?.Where(x => x.ReservationId == reservationId).ToList();
                    cancellationPolicies = cancellationPolicies?.Where(x => x.ReservationId == reservationId).ToList();
                }
                else if (reservationIdsInput.Count > 0)
                {
                    var searchCriteriasQ = from items in searchCriterias
                                           from input in reservationIdsInput
                                           where items.ReservationId == input
                                           select items;

                    var roomReservationsQ = from items in roomReservations
                                            from input in reservationIdsInput
                                            where items.ReservationId == input
                                            select items;

                    var reservationPrebookCountsQ = from items in reservationPrebookCounts
                                                    from input in reservationIdsInput
                                                    where items.ReservationId == input
                                                    select items;

                    var cancellationPoliciesQ = from items in cancellationPolicies
                                                from input in reservationIdsInput
                                                where items.ReservationId == input
                                                select items;

                    searchCriterias = searchCriteriasQ.ToList();
                    roomReservations = roomReservationsQ.ToList();
                    reservationPrebookCounts = reservationPrebookCountsQ.ToList();
                    cancellationPolicies = cancellationPoliciesQ.ToList();
                }

                #endregion Run for particular ReservationId

                if (searchCriterias != null && reservationPrebookCounts != null && extraClientConfig != null)
                {
                    reservationids_maxcount = reservationPrebookCounts
                               .GroupBy(r => r.ReservationId)
                               .Select(group => new ReservationPrebookCount
                               {
                                   ReservationId = group.Key,
                                   PreBookCount = group.Max(r => r.PreBookCount),
                                   CreateDate = group.FirstOrDefault(r => r.PreBookCount == group.Max(p => p.PreBookCount))?.CreateDate ?? DateTime.MinValue
                               })
                               .ToList();
                }

                int traveldaysmaxsearchindays = extraClientConfig?.traveldaysmaxsearchindays ?? 60;
                int traveldaysminsearchindays = extraClientConfig?.traveldaysminsearchindays ?? 10;

                var filteredReservations = searchCriterias?.ToList();

                watchCriteria.Stop();
                var elapsedTimeInSeconds = watchCriteria.Elapsed.TotalSeconds;
                if (isUpdateDB)
                {
                    LoggerPersistance.SearchSyncLogging(Constant.SearchSync, 0, 0, steptoken, elapsedTimeInSeconds, "ReservationFilter", repricerId);
                }

                cancellationPolicies = cancellationPolicies
                    ?.Where(cancellation =>
                        filteredReservations?.Any(reservation => reservation.ReservationId == cancellation.ReservationId)
                        == true
                        )
                    ?.ToList();

                if (filteredReservations?.Count > 1)
                {
                    /* SkipFrom if needed
                    if (repricerId == 8)
                    {
                        var startFromReservationID = 387702;
                        filteredReservations = filteredReservations?.Where(x => x.ReservationId >= startFromReservationID)?.ToList();
                        searchCriterias = filteredReservations?.Where(x => x.ReservationId >= startFromReservationID)?.ToList();
                    }

                    if (repricerId == 10)
                    {
                        var startFromReservationID = 13121;
                        filteredReservations = filteredReservations?.Where(x => x.ReservationId >= startFromReservationID)?.ToList();
                        searchCriterias = filteredReservations?.Where(x => x.ReservationId >= startFromReservationID)?.ToList();
                    }
                    //*/
                }

                filteredReservations = filteredReservations?.OrderBy(x => x.ReservationId)?.ToList();
                filteredReservations = SerializeDeSerializeHelper.DeSerialize<List<ReservationMainModel>>(SerializeDeSerializeHelper.Serialize(filteredReservations));

                if (filteredReservations != null && filteredReservations?.Count > 0)
                {
                    var counter = currentItem > 0 ? currentItem : 0;
                    var parallelOptions = UtilCommonConstants.GetParallelOptions(50);
                    if (reservationId > 0)
                    {
                        parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 1 };
                    }
                    var counterTotal = totalItems > 0 ? totalItems : filteredReservations.Count();
                    var startTime = DateTime.UtcNow;

                    Parallel.ForEach(filteredReservations, parallelOptions, reservation =>
                    //foreach (var reservation in filteredReservations)
                    {
                        var reservationIdInLoop = reservation.ReservationId;
                        var preBookResponseResult = new PreBookResponseResult
                        {
                            RepricerId = repricerId,
                            Repricername = extraClientConfig?.RepricerUserName,
                            ReservationId = reservationIdInLoop
                        };

                        _log.UpdateOptimizationToQueue(OptimizationStatusEnum.IsOptimizableTrue.ToString(), reservationIdInLoop, repricerId, true, false, false);

                        var offersFound = 0;
                        List<OfferInfo> offerInfoList = default(List<OfferInfo>);

                        OptimizationStatusResponse optimizationStatus = null;
                        try
                        {
                            // Checking if optimization is already running
                            var IsOptimizationAlreadyRunningForReservation = _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: false, isCheckAndSet: false);
                            var waitCounter = 0;
                            var cancellationpolicybyreservationid = _searchserviceHelper.GetMissingCancellationPolicyReservations(repricerId, reservationIdInLoop, cancellationPolicies, reservation);

                            while (IsOptimizationAlreadyRunningForReservation)
                            {
                                waitCounter++;
                                Task.Delay(TimeSpan.FromSeconds(5))?.GetAwaiter().GetResult();

                                IsOptimizationAlreadyRunningForReservation = _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: false, isCheckAndSet: false);
                                if (waitCounter > 6)
                                {
                                    return; // Exit if optimization is still running after several retries
                                }
                            }
                            try
                            {
                                counter++;
                                startTime = DateTime.UtcNow;

                                #region logging at start

                                //var logInfoEntry = new
                                //{
                                //    RepricerId = repricerId,
                                //    Counter = $"{counter}\\{counterTotal}) START",
                                //    Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                //    OffersFound = offersFound,
                                //    reservation?.ReservationId,
                                //    isPrebookCreated = false,
                                //    isOptimized = false,
                                //    Method = "SameSupplier",
                                //    StartTime = startTime,
                                //    EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                //    EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                                //};

                                //var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                                //var irixErrorEntity = new IrixErrorEntity
                                //{
                                //    ClassName = _className,
                                //    MethodName = "SameSupplier",
                                //    RePricerId = repricerId,
                                //    ReservationId = Convert.ToInt32(reservationId),
                                //    Params = msg
                                //};
                                //_log.Info(msg, irixErrorEntity, true);

                                #endregion logging at start
                            }
                            catch (Exception ex)
                            {
                            }
                            //check only if automatic or demo
                            if (repricerClientDetail.IsOptimizationAllowed)
                            {
                                if (_isMock)
                                {
                                    optimizationStatus = new OptimizationStatusResponse
                                    {
                                        IsOptimizable = true
                                    };
                                }
                                else
                                {
                                    optimizationStatus = _optimizationHelper.IsOptimizationStatus(repricerId, reservationIdInLoop, isOptimizeTriggeredManually, isOptimizeTriggeredManually, isMultiSupplier: false);
                                }

                                preBookResponseResult.OptimizableStatus = new OptimizationOptimizationBooking
                                {
                                    Status = optimizationStatus?.Optimization?.Status ?? ""
                                };
                                isAnyOptimized = preBookResponseResult.IsOptimized = optimizationStatus?.Optimization?.Status == "optimized";

                                //optimizationStatus = new OptimizationStatusResponse
                                //{
                                //    IsOptimizable = true
                                //};
                            }
                            if (optimizationStatus?.IsOptimizable == false)
                            {
                                bool isAlreadyoptimized = (optimizationStatus?.OptimizedBy > 0) == true;

                                _log.UpdateOptimizationToQueue(OptimizationStatusEnum.Repricer_OptimizationNotAllowed.ToString(), reservationIdInLoop, repricerId, !isAlreadyoptimized, true, isAlreadyoptimized);

                                preBookResponseResult.Status = new Dictionary<int, string>();
                                preBookResponseResult.Status.Add
                                (reservationIdInLoop
                                    , (
                                        optimizationStatus?.OptimizationRestrictions?.Values?.FirstOrDefault()
                                        ?? optimizationStatus?.Optimization?.Status
                                        ?? optimizationStatus?.IsOptimizable?.ToString()
                                        ?? string.Empty
                                       )
                                    );
                                lock (lockObject) // Lock the shared resource to ensure thread safety
                                {
                                    prebookresult.PreBookResponseResults.Add(preBookResponseResult);
                                }
                            }
                            //Step 1) Check optimizationStatus?.IsOptimizable by calling IRIX API
                            //If OptimizationType is initial Stage no need to check optimizationStatus
                            if (IsOptimizationAlreadyRunningForReservation == false && (extraClientConfig?.OptimizationType == OptimizationType.Demo
                                || (repricerClientDetail.IsOptimizationAllowed && optimizationStatus?.IsOptimizable == true))
                            )
                            {
                                var prebookrequest = new PackageRequest();
                                try
                                {
                                    _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: false, isCheckAndSet: true);
                                    Guid newGuidSerach = Guid.NewGuid();
                                    string stepSearchtoken = newGuidSerach.ToString();

                                    int prebookcount = 0;
                                    var maxprebookcount = reservationids_maxcount?.FirstOrDefault(r => r.ReservationId == reservationIdInLoop);

                                    if (maxprebookcount != null)
                                    {
                                        prebookcount = maxprebookcount.PreBookCount;
                                    }
                                    var watchCreateCriteria = Stopwatch.StartNew();

                                    //var criteria = SerializeDeSerializeHelper.DeSerialize<SearchRequest>(reservation?.CriteriaJson as string);
                                    //if (criteria == null || criteria?.occupancy?.rooms?.Count == 0)
                                    //{
                                    var criteria = _searchserviceHelper.CreateCriteria(reservation, roomReservations);
                                    var criteriajson = Newtonsoft.Json.JsonConvert.SerializeObject(criteria);

                                    //if (isUpdateDB)
                                    //{
                                    //    _reservationPersistence.InsertCriteriaJson(criteriajson, reservationIdInLoop);
                                    //}
                                    reservation.CriteriaJson = criteriajson;
                                    //}

                                    watchCreateCriteria.Stop();
                                    var elapsedTimeInSecondsccreatecriteria = watchCreateCriteria.Elapsed.TotalSeconds;
                                    if (isUpdateDB)
                                    {
                                        LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 1, stepSearchtoken, elapsedTimeInSecondsccreatecriteria, "CriteriaCreation", repricerId);
                                    }

                                    var searchresponse = _irixAdapter.SearchForBooking(reservationIdInLoop, criteria, constants.SearchService, repricerId, stepSearchtoken);
                                    if (searchresponse == null)
                                    {
                                        preBookResponseResult.Status = new Dictionary<int, string>
                                        {
                                            { reservation.ReservationId, OptimizationStatusEnum.Repricer_SearchSyncFailed.ToString() }
                                        };
                                    }
                                    if (searchresponse != null)
                                    {
                                        if (isUpdateDB)
                                        {
                                            _searchserviceHelper.InsertSearchSyncData(searchresponse.hotels, repricerId, reservationIdInLoop, extraClientConfig?.IsSearchSyncDataSave ?? false)?.GetAwaiter().GetResult();
                                        }
                                        var prebookCriteriaResult = (_reservationPersistance?.GetPreBookCriteria(reservationIdInLoop, repricerId))?.GetAwaiter().GetResult();
                                        var prebookCriteria = prebookCriteriaResult?.PreBookCriteriaList;

                                        var isRoomBoard = true;
                                        var isRoomInfo = true;
                                        var isRoomFoundUsingRoomInfo = false;

                                        if (prebookCriteria != null && prebookCriteria.Count > 0)
                                        {
                                            var watchOfer = Stopwatch.StartNew();
                                            watchOfer.Stop();

                                            offerInfoList = GetOfferListFromSearchResponse(searchresponse, prebookCriteria, repricerId, reservationIdInLoop);
                                            offersFound = offerInfoList?.Count ?? 0;
                                            watchOfer.Stop();
                                            var elapsedTimeInSecondoffer = watchCreateCriteria.Elapsed.TotalSeconds;
                                            if (isUpdateDB)
                                            {
                                                LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 5, stepSearchtoken, elapsedTimeInSecondoffer, "offerfilter", repricerId);
                                            }
                                            if (offerInfoList == null || offerInfoList.Count <= 0)
                                            {
                                                preBookResponseResult.Status = new Dictionary<int, string>
                                                {
                                                    { reservationIdInLoop, OptimizationStatusEnum.Repricer_NoNewOfferOrMatchedCancellationPolicy.ToString() }
                                                };
                                            }
                                            bool isOptimized = optimizationStatus?.Optimization?.Status == "optimized";
                                            if (offerInfoList != null && offerInfoList.Count > 0)
                                            {
                                                foreach (var offerInfo in offerInfoList)
                                                {
                                                    if (!isOptimized)
                                                    {
                                                        var watchroomselection = Stopwatch.StartNew();

                                                        var mainRoomInfoList = new List<SearchRoom_Package>();
                                                        var matchedRoomInfoList = new List<SearchRoom_Package>();
                                                        var packageInfo = new Package();
                                                        var selectedRoomTokens = new List<string>();

                                                        isRoomFoundUsingRoomInfo = UpdateMatchingRoomOffer(
                                                                                    mainRoomInfoList
                                                                                    , matchedRoomInfoList
                                                                                    , offerInfo
                                                                                    , prebookCriteria
                                                                                    , selectedRoomTokens
                                                                                    , ref packageInfo
                                                                                    , repricerId
                                                                                    , reservationIdInLoop

                                                                                    );

                                                        //Does not reattempt prebook if already attempted in same day
                                                        if (!_searchserviceHelper.GetReservationEmailStatus(reservationIdInLoop, offerInfo?.id, repricerId))
                                                        {
                                                            var roominfofromoffersList = new List<SearchRoom>();
                                                            if (prebookCriteria != null)
                                                            {
                                                                watchroomselection.Stop();
                                                                var elapsedTimeInroom = watchroomselection.Elapsed.TotalSeconds;
                                                                if (isUpdateDB)
                                                                {
                                                                    LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 6, stepSearchtoken, elapsedTimeInroom, "roomfilter", repricerId);
                                                                }
                                                                if (selectedRoomTokens == null || selectedRoomTokens.Count <= 0)
                                                                {
                                                                    preBookResponseResult.Status = new Dictionary<int, string>
                                                                {
                                                                    { reservationIdInLoop, OptimizationStatusEnum.Repricer_RoomNoLongerAvailable.ToString() }
                                                                };
                                                                }
                                                                else
                                                                {
                                                                    var watchroomprice = Stopwatch.StartNew();

                                                                    if (packageInfo == null)
                                                                    {
                                                                        preBookResponseResult.Status = new Dictionary<int, string>
                                                                    {
                                                                        { reservationIdInLoop, OptimizationStatusEnum.Repricer_RoomNoLongerAvailable.ToString() }
                                                                    };
                                                                    }
                                                                    else
                                                                    {
                                                                        decimal pricethresholdInOriginalReservationCurrency = extraClientConfig.PriceDifferenceValue;
                                                                        if (prebookCriteria.Count() == selectedRoomTokens.Count())
                                                                        {
                                                                            if (selectedRoomTokens != null)
                                                                            {
                                                                                var packageinfo = packageInfo;

                                                                                if (packageinfo != null)
                                                                                {
                                                                                    double? hotelPrice = 0.0;
                                                                                    double? RoomlevelPrice = 0.0;
                                                                                    string? hotelCurrency;
                                                                                    string? RoomLevelCurrency = null;
                                                                                    decimal hotellevelfactor = 0.0m;
                                                                                    var isRoomLevelPriceMatched = true;
                                                                                    var packagelevelprice = packageinfo.Price?.components?.supplier?.value;
                                                                                    var packagelevelcurrency = packageinfo.Price?.components?.supplier?.currency;
                                                                                    var packagelevelfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, packagelevelcurrency, prebookCriteriaResult.Currency));

                                                                                    if (matchedRoomInfoList.Any(room => room.SearchRoomPrice == null))
                                                                                    {
                                                                                        isRoomLevelPriceMatched = false;
                                                                                        hotelPrice = packagelevelprice;
                                                                                        hotelCurrency = packagelevelcurrency;
                                                                                        hotellevelfactor = packagelevelfactor;
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        RoomlevelPrice = matchedRoomInfoList.Sum(room => room.SearchRoomPrice ?? 0.0);
                                                                                        RoomLevelCurrency = matchedRoomInfoList[0].SearchRoomCurrency;
                                                                                        var RoomlevelFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, RoomLevelCurrency, prebookCriteriaResult.Currency));
                                                                                        hotelPrice = RoomlevelPrice;
                                                                                        hotelCurrency = RoomLevelCurrency;
                                                                                        hotellevelfactor = RoomlevelFactor;
                                                                                    }

                                                                                    string roomLevelInfo = RoomlevelPrice.ToString() + " " + (RoomLevelCurrency ?? "");
                                                                                    string packageLevelInfo = packagelevelprice.ToString() + " " + (packagelevelcurrency ?? "");

                                                                                    watchroomprice.Stop();
                                                                                    var elapsedTimeprice = watchroomprice.Elapsed.TotalSeconds;
                                                                                    if (isUpdateDB)
                                                                                    {
                                                                                        LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 7, stepSearchtoken, elapsedTimeprice, "pricefilter", repricerId);
                                                                                    }

                                                                                    if (hotelPrice > 0)
                                                                                    {
                                                                                        if ((extraClientConfig?.PriceDifferenceValue) != 0)
                                                                                        {
                                                                                            var ClientFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, extraClientConfig?.pricedifferencecurrency, prebookCriteriaResult.Currency));
                                                                                            if (ClientFactor != 0)
                                                                                            {
                                                                                                pricethresholdInOriginalReservationCurrency = _searchserviceHelper.RoundToDecimalPlaces(ClientFactor * extraClientConfig.PriceDifferenceValue);
                                                                                            }
                                                                                        }
                                                                                        var originalReservationPrice = prebookCriteriaResult.IssueNet;
                                                                                        var packageToken = packageinfo.PackageToken;
                                                                                        prebookrequest = _searchserviceHelper.CreatePreBookCriteria(packageToken, selectedRoomTokens?.ToList());

                                                                                        var searchsyncprice = _searchserviceHelper.RoundToDecimalPlaces(System.Convert.ToDecimal(hotelPrice) * hotellevelfactor);
                                                                                        var profit = _searchserviceHelper.RoundToDecimalPlaces(originalReservationPrice - searchsyncprice);

                                                                                        var profitperc = _searchserviceHelper.RoundToDecimalPlaces((profit / originalReservationPrice) * 100);

                                                                                        if (originalReservationPrice <= searchsyncprice)
                                                                                        {
                                                                                            preBookResponseResult.Status = new Dictionary<int, string>
                                                                                            {
                                                                                                { reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString() }
                                                                                            };
                                                                                        }
                                                                                        if (searchsyncprice > 0 && prebookCriteriaResult.IssueNet > searchsyncprice)
                                                                                        {
                                                                                            Guid newGuid = Guid.NewGuid();
                                                                                            string token = newGuid.ToString();
                                                                                            if (isUpdateDB)
                                                                                            {
                                                                                                _searchserviceHelper.InsertPreBookClientConfiguration(extraClientConfig, repricerId, reservationIdInLoop, token);
                                                                                            }

                                                                                            var CurrencyFactorTOSAVEINDB = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, prebookCriteriaResult.Currency, CommonConstant.EUR_DefaultTo));
                                                                                            var roomsdetail = _searchserviceHelper.GetRoomResult(prebookCriteriaResult, matchedRoomInfoList, reservation, offerInfo.System);
                                                                                            var roomsdetailJson = SerializeDeSerializeHelper.Serialize(roomsdetail);
                                                                                            var searchsyncjson = SerializeDeSerializeHelper.Serialize(searchresponse);

                                                                                            var fisrtPrebookCriteria = prebookCriteria?.FirstOrDefault();
                                                                                            var reservationCriteriaJson = reservation?.CriteriaJson;

                                                                                            if (fisrtPrebookCriteria != null && string.IsNullOrEmpty(fisrtPrebookCriteria?.Currency))
                                                                                            {
                                                                                                fisrtPrebookCriteria.Currency = reservation?.CancellationPoliciesBySource?.FirstOrDefault()?.Currency;
                                                                                            }
                                                                                            if (fisrtPrebookCriteria == null || string.IsNullOrEmpty(fisrtPrebookCriteria?.Currency))
                                                                                            {
                                                                                                return;
                                                                                            }

                                                                                            var hotelName = fisrtPrebookCriteria?.HotelName ?? "Unknown";
                                                                                            var currencyPrebook = prebookCriteria?.FirstOrDefault()?.Currency ?? prebookCriteriaResult.Currency ?? "Unknown";
                                                                                            Task.Run(() =>
                                                                                            {
                                                                                                var roomResult = SerializeDeSerializeHelper.DeSerialize<RoomResult>(roomsdetailJson);
                                                                                                if (isUpdateDB)
                                                                                                {
                                                                                                    _searchserviceHelper.InsertIntoPrebooklog(
                                                                                                            reservationIdInLoop,
                                                                                                            repricerId,
                                                                                                            roomResult,
                                                                                                            hotelName,
                                                                                                            profitperc,
                                                                                                            profit,
                                                                                                            searchsyncprice,
                                                                                                            currencyPrebook,
                                                                                                            reservationCriteriaJson, searchsyncjson, CurrencyFactorTOSAVEINDB
                                                                                                        ).GetAwaiter().GetResult();
                                                                                                }
                                                                                            });
                                                                                            var isPriceThreshold = false;
                                                                                            if (extraClientConfig?.IsUsePercentage == true
                                                                                                && profitperc >= extraClientConfig.PriceDifferencePercentage //extraClientConfig is object of class SmtpConfigModel
                                                                                                )
                                                                                            {
                                                                                                isPriceThreshold = true;
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                                if (extraClientConfig?.IsUsePercentage != true
                                                                                                    && profit >= pricethresholdInOriginalReservationCurrency
                                                                                                    )
                                                                                                {
                                                                                                    isPriceThreshold = true;
                                                                                                    if (!isPriceThreshold)
                                                                                                    {
                                                                                                        preBookResponseResult.Status = new Dictionary<int, string>
                                                                                                        {
                                                                                                            { reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString() }
                                                                                                        };
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                            OptimizeBookingReq optimizeBookingReq = new OptimizeBookingReq { RePricerID = repricerId }; // Instantiate RoomPackageResponse object

                                                                                            if ((profit >= UtilCommonConstants.ProfitMinimumGlobalCheck) && (isPriceThreshold || extraClientConfig.IsCreatePrebookFoPriceEdgeCase))
                                                                                            {
                                                                                                var offerIdForRoomInfo = offerInfo.id;

                                                                                                var srk = searchresponse?.srk;
                                                                                                var prebooktokens = searchresponse?.tokens.results;
                                                                                                var hotelsIndex = searchresponse?.hotels[0].index;
                                                                                                var offerIndex = offerIdForRoomInfo;
                                                                                                var reservationSupplierName = reservation.supplierName;
                                                                                                var prebookSupplierName = offerInfo?.System?.Code;

                                                                                                //IsSearchSucess true when every condition meet to create prebook
                                                                                                if (reservationSupplierName?.ToLower().Trim() == prebookSupplierName?.ToLower().Trim())
                                                                                                {
                                                                                                    preBookResponseResult.IsSearchSucess = true;

                                                                                                    var prebook = _irixAdapter.Prebookresponse(reservationIdInLoop, prebookrequest, constants.prebookresp, repricerId, prebooktokens, srk, offerIndex, hotelsIndex, stepSearchtoken);
                                                                                                    bool isPrebookPriceValid = false;
                                                                                                    if (prebook != null)
                                                                                                    {
                                                                                                        try
                                                                                                        {
                                                                                                            //IsPrebook true when PreBook Created
                                                                                                            preBookResponseResult.IsPrebookSucess = true;
                                                                                                            var prebookPrice = System.Convert.ToDecimal(prebook?.package?.price?.components?.supplier?.value);
                                                                                                            var prebookCurrecnyFactor = 1.0M;
                                                                                                            if (prebookPrice > 0)
                                                                                                            {
                                                                                                                currencyPrebook = prebook?.package?.price?.components?.supplier?.currency;
                                                                                                                prebookCurrecnyFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, currencyPrebook, prebookCriteriaResult.Currency));
                                                                                                                prebookPrice = _searchserviceHelper.RoundToDecimalPlaces(System.Convert.ToDecimal(prebookPrice) * prebookCurrecnyFactor);
                                                                                                                profit = _searchserviceHelper.RoundToDecimalPlaces(originalReservationPrice - prebookPrice);
                                                                                                                profitperc = _searchserviceHelper.RoundToDecimalPlaces((profit / originalReservationPrice) * 100);
                                                                                                                searchsyncprice = prebookPrice;

                                                                                                                #region Recheck price threshold

                                                                                                                if (extraClientConfig?.IsUsePercentage == true
                                                                                                                    && profitperc >= extraClientConfig.PriceDifferencePercentage
                                                                                                                )
                                                                                                                {
                                                                                                                    isPriceThreshold = true;
                                                                                                                }
                                                                                                                else
                                                                                                                {
                                                                                                                    if (extraClientConfig?.IsUsePercentage != true
                                                                                                                        && profit >= pricethresholdInOriginalReservationCurrency
                                                                                                                        )
                                                                                                                    {
                                                                                                                        isPriceThreshold = true;
                                                                                                                    }
                                                                                                                }

                                                                                                                if (!(profit >= UtilCommonConstants.ProfitMinimumGlobalCheck))
                                                                                                                {
                                                                                                                    isPriceThreshold = false;
                                                                                                                }

                                                                                                                if (!isPriceThreshold)
                                                                                                                {
                                                                                                                    preBookResponseResult.Status = new Dictionary<int, string>
                                                                                                                    {
                                                                                                                        { reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString() }
                                                                                                                    };
                                                                                                                }

                                                                                                                #endregion Recheck price threshold
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                                prebookPrice = searchsyncprice;
                                                                                                            }
                                                                                                            isPrebookPriceValid = isPriceThreshold;

                                                                                                            var watchcancellation = Stopwatch.StartNew();

                                                                                                            var cancellationresult = _searchserviceHelper.CancellationCheck(repricerId, cancellationpolicybyreservationid, prebook.cancellationPolicy, prebookCriteriaResult, prebookPrice, reservation);
                                                                                                            var prebookjson = SerializeDeSerializeHelper.Serialize(prebook);
                                                                                                            var cancellationresultJson = SerializeDeSerializeHelper.Serialize(cancellationresult);

                                                                                                            var AppSettingKeys_isUseSendGrid = ConfigurationManagerHelper.GetValuefromAppSettings(constants.AppSettingKeys_isUseSendGrid);
                                                                                                            int AppSettingKeys_isUseSendGridValue = !string.IsNullOrEmpty(AppSettingKeys_isUseSendGrid)
                                                                                                                ? Convert.ToInt32(AppSettingKeys_isUseSendGrid)
                                                                                                                : 0;

                                                                                                            optimizeBookingReq.srk = srk;
                                                                                                            optimizeBookingReq.hotelIndex = hotelsIndex;
                                                                                                            optimizeBookingReq.offerIndex = offerIndex;
                                                                                                            optimizeBookingReq.AvailabilityToken = prebook.availabilityToken;
                                                                                                            optimizeBookingReq.token = prebooktokens;
                                                                                                            optimizeBookingReq.ClientRef = token;

                                                                                                            var watchPreBook = Stopwatch.StartNew();

                                                                                                            var cancellationfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, cancellationresult?.Currency, prebookCriteriaResult?.Currency));
                                                                                                            decimal cancellationamount = 0;

                                                                                                            if (cancellationresult?.CancellationCharge != 0)
                                                                                                            {
                                                                                                                cancellationamount = _searchserviceHelper.RoundToDecimalPlaces(cancellationresult?.CancellationCharge ?? 0 * cancellationfactor);
                                                                                                            }

                                                                                                            var profitAfterCancellation = profit - cancellationamount;

                                                                                                            if (profitAfterCancellation > 0 && isPriceThreshold)
                                                                                                            {
                                                                                                                var profitPercentageAfterCancellation = _searchserviceHelper.RoundToDecimalPlaces((profitAfterCancellation / originalReservationPrice) * 100);

                                                                                                                if (extraClientConfig?.IsUsePercentage == true
                                                                                                                    && profitPercentageAfterCancellation >= extraClientConfig.PriceDifferencePercentage
                                                                                                                )
                                                                                                                {
                                                                                                                    isPriceThreshold = true;
                                                                                                                }
                                                                                                                else if (extraClientConfig?.IsUsePercentage != true
                                                                                                                        && profitAfterCancellation >= pricethresholdInOriginalReservationCurrency

                                                                                                                )
                                                                                                                {
                                                                                                                    isPriceThreshold = true;
                                                                                                                }
                                                                                                                else
                                                                                                                {
                                                                                                                    isPriceThreshold = false;
                                                                                                                }
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                                preBookResponseResult.Status = new Dictionary<int, string>
                                                                                                                {
                                                                                                                    { reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString() }
                                                                                                                };
                                                                                                            }

                                                                                                            if (!(profit >= UtilCommonConstants.ProfitMinimumGlobalCheck))
                                                                                                            {
                                                                                                                isPriceThreshold = false;
                                                                                                            }

                                                                                                            if (cancellationresult != null
                                                                                                                && (isPrebookPriceValid || extraClientConfig.IsCreatePrebookFoPriceEdgeCase)
                                                                                                                && (cancellationresult.cPStatus == CPBucketStatus.loose.ToString()
                                                                                                                || cancellationresult.cPStatus == CPBucketStatus.tightWithBuffer.ToString()
                                                                                                                || cancellationresult.cPStatus == CPBucketStatus.CancellationChargesApplicable.ToString()
                                                                                                                )
                                                                                                            )
                                                                                                            {
                                                                                                                RoomResult roomResult = null;
                                                                                                                CancellationPolicyResult cpResult = null;
                                                                                                                var clientConfig_DaysDifferenceInPreBookCreation = 0;
                                                                                                                try
                                                                                                                {
                                                                                                                    watchcancellation.Stop();
                                                                                                                    var elapsedTimecancellation = 0.0;
                                                                                                                    elapsedTimecancellation = watchcancellation.Elapsed.TotalSeconds;
                                                                                                                    cpResult = SerializeDeSerializeHelper.DeSerialize<CancellationPolicyResult>(cancellationresultJson);
                                                                                                                    roomResult = SerializeDeSerializeHelper.DeSerialize<RoomResult>(roomsdetailJson);
                                                                                                                    clientConfig_DaysDifferenceInPreBookCreation = extraClientConfig.ClientConfig_DaysDifferenceInPreBookCreation;
                                                                                                                }
                                                                                                                catch (Exception ex)
                                                                                                                {
                                                                                                                    var irixErrorEntity = new IrixErrorEntity
                                                                                                                    {
                                                                                                                        ClassName = nameof(SearchService),
                                                                                                                        MethodName = nameof(_1_PrebookAndOptimize_SameSupplier_Automatic),
                                                                                                                        Params = $"RePricerId: {repricerId}, ReservationId: {reservationIdInLoop}"
                                                                                                                    };
                                                                                                                    _log.Error(irixErrorEntity, ex);
                                                                                                                }

                                                                                                                OptimizationBookingResponse optimizationBookingResponse = null;
                                                                                                                preBookResponseResult.IsOptimized = isOptimized;
                                                                                                                isCurrentPrebookOptimized = false;

                                                                                                                //Step 2) Optimize booking by calling IRIX API for valid reservations
                                                                                                                if (_isMock == false)
                                                                                                                {
                                                                                                                    if (extraClientConfig?.OptimizationType != null
                                                                                                                        && (
                                                                                                                                   extraClientConfig?.OptimizationType == OptimizationType.Automatic
                                                                                                                                || isOptimizeTriggeredManually == true
                                                                                                                           )
                                                                                                                        && cancellationresult?.cPStatus?.ToLower() == CPBucketStatus.loose.ToString()
                                                                                                                        && isPrebookPriceValid
                                                                                                                        && optimizationStatus?.IsOptimizable == true
                                                                                                                        && isRoomFoundUsingRoomInfo == true
                                                                                                                        && isUpdateDB == true
                                                                                                                        && preBookResponseResult.IsPrebookSucess == true
                                                                                                                        && isOptimized == false
                                                                                                                        && isMultiSupplier == false
                                                                                                                    )
                                                                                                                    {
                                                                                                                        #region Attempt to optimize qualified prebook

                                                                                                                        #region Logging

                                                                                                                        try
                                                                                                                        {
                                                                                                                            var logInfoEntry = new
                                                                                                                            {
                                                                                                                                RepricerId = repricerId,
                                                                                                                                Message = "Entered into OptimizeAtIrix before after dry run _6_SAME_SUPPLIER",
                                                                                                                                Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                                                                                                                isMultiSupplierMethod = false,
                                                                                                                                OffersFound = offersFound,
                                                                                                                                reservation?.ReservationId,
                                                                                                                                Params = new
                                                                                                                                {
                                                                                                                                    repricerId,
                                                                                                                                    reservationId,
                                                                                                                                    isUpdateDB,
                                                                                                                                    isMultiSupplier,
                                                                                                                                    reservationSupplierName,
                                                                                                                                    prebookSupplierName,
                                                                                                                                    isOptimizeTriggeredManually = false,
                                                                                                                                    totalItems,
                                                                                                                                    currentItem,
                                                                                                                                },
                                                                                                                                isPrebookCreated = true,
                                                                                                                                isOptimized,
                                                                                                                                Method = method,
                                                                                                                                StartTime = startTime.ToString("yyyy-MMM-dd HH:mm:ss"),

                                                                                                                                EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                                                                                                                EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                                                                                                                            };

                                                                                                                            var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                                                                                                                            var irixErrorEntity = new IrixErrorEntity
                                                                                                                            {
                                                                                                                                ClassName = "OPTIMIZE_TRIGGERED",
                                                                                                                                MethodName = method,
                                                                                                                                RePricerId = repricerId,
                                                                                                                                ReservationId = Convert.ToInt32(reservation?.ReservationId),
                                                                                                                                Params = msg
                                                                                                                            };
                                                                                                                            _log.Info(msg, irixErrorEntity, true);
                                                                                                                        }
                                                                                                                        catch (Exception ex)
                                                                                                                        {
                                                                                                                        }

                                                                                                                        #endregion Logging

                                                                                                                        var dryRunResponse = _dryRunOptimizationService._2_DryRunOptimizationApiIRIX(optimizeBookingReq, reservationIdInLoop, isUpdateDB: true, isCacheRefresh: isOptimizeTriggeredManually, isMultiSupplier: false);
                                                                                                                        preBookResponseResult.IsDryRunExecuted = true;
                                                                                                                        preBookResponseResult.DryRunResponse = dryRunResponse;

                                                                                                                        if (dryRunResponse?.ExpectedGain?.Value > 0)
                                                                                                                        {
                                                                                                                            isPriceThreshold = _searchserviceHelper.IsPriceThreshold
                                                                                                                            (
                                                                                                                                originalReservationPrice
                                                                                                                                , dryRunResponse.ExpectedGain.Value
                                                                                                                                , repricerClientDetail
                                                                                                                                , prebookCriteriaResult
                                                                                                                                , reservationIdInLoop
                                                                                                                                , preBookResponseResult
                                                                                                                            );
                                                                                                                            if (isPriceThreshold)
                                                                                                                            {
                                                                                                                                preBookResponseResult.IsOptimizable = true;
                                                                                                                                preBookResponseResult.IsExpectedGainAboveThreshold = true;
                                                                                                                                var isCheckBetterCrossSupplier = repricerClientDetail?.ExtraClientDetail?.IsCheckCrossSupperBeforeOptimization ?? false;
                                                                                                                                var isBetterCrossSupplierExists = false;
                                                                                                                                DashboardReportResponseRow activeTabDashboard = default(DashboardReportResponseRow);
                                                                                                                                try
                                                                                                                                {
                                                                                                                                    if (isCheckBetterCrossSupplier)
                                                                                                                                    {
                                                                                                                                        var reservationRequestReport = new RepricerReportRequest
                                                                                                                                        {
                                                                                                                                            RepricerId = repricerId,
                                                                                                                                            ReportType = "Prebook"
                                                                                                                                        };
                                                                                                                                        if (reservationId > 0)
                                                                                                                                        {
                                                                                                                                            reservationRequestReport = new RepricerReportRequest
                                                                                                                                            {
                                                                                                                                                RepricerId = repricerId,
                                                                                                                                                ReportType = "Prebook",
                                                                                                                                                ReservationId = reservationId.ToString()
                                                                                                                                            };
                                                                                                                                        }
                                                                                                                                        activeTabDashboard = _masterService.GetReservationReport(reservationRequestReport)?.Data
                                                                                                                                                                    ?.FirstOrDefault(x =>
                                                                                                                                                                        x.RepricerId == repricerId
                                                                                                                                                                        && x.ReservationId == reservationId
                                                                                                                                                                        && x.ReportType?.ToLower() == "prebook"
                                                                                                                                                                    );

                                                                                                                                        if (activeTabDashboard?.Prebook?.Supplier?.ToLower()?.Trim() != prebookSupplierName?.ToLower()?.Trim())
                                                                                                                                        {
                                                                                                                                            var factor = _exchangeRateService.ExchangeRateFactor(repricerId, dryRunResponse.ExpectedGain.Currency, activeTabDashboard.Currency);
                                                                                                                                            var convertedProfit = activeTabDashboard.OptimizationProfit * factor;
                                                                                                                                            if (convertedProfit > dryRunResponse.ExpectedGain.Value)
                                                                                                                                            {
                                                                                                                                                isBetterCrossSupplierExists = true;
                                                                                                                                            }
                                                                                                                                        }
                                                                                                                                    }
                                                                                                                                }
                                                                                                                                catch (Exception ex)
                                                                                                                                {
                                                                                                                                    var irixErrorEntity = new IrixErrorEntity
                                                                                                                                    {
                                                                                                                                        ClassName = _className,
                                                                                                                                        MethodName = _method,
                                                                                                                                        Params = SerializeDeSerializeHelper.Serialize(new
                                                                                                                                        {
                                                                                                                                            repricerId,
                                                                                                                                            reservationId = reservationIdInLoop,
                                                                                                                                            reservationSupplierName,
                                                                                                                                            prebookSupplierName,
                                                                                                                                            dryRunResponse.ExpectedGain,
                                                                                                                                            ex.Message,
                                                                                                                                            activeTabDashboard
                                                                                                                                        })
                                                                                                                                    };
                                                                                                                                    _log.Error(irixErrorEntity, ex);
                                                                                                                                }
                                                                                                                                if (!isBetterCrossSupplierExists && reservationSupplierName?.ToLower().Trim() == prebookSupplierName?.ToLower().Trim())
                                                                                                                                {
                                                                                                                                    #region Logging

                                                                                                                                    try
                                                                                                                                    {
                                                                                                                                        var logInfoEntry = new
                                                                                                                                        {
                                                                                                                                            RepricerId = repricerId,
                                                                                                                                            Message = "Entered into OptimizeAtIrix after dry run _6_SAME_SUPPLIER",
                                                                                                                                            Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                                                                                                                            isMultiSupplierMethod = false,
                                                                                                                                            dryRunResponse,
                                                                                                                                            OffersFound = offersFound,
                                                                                                                                            reservation?.ReservationId,
                                                                                                                                            Params = new
                                                                                                                                            {
                                                                                                                                                repricerId,
                                                                                                                                                reservationId,
                                                                                                                                                isUpdateDB,
                                                                                                                                                isMultiSupplier,
                                                                                                                                                reservationSupplierName,
                                                                                                                                                prebookSupplierName,
                                                                                                                                                isOptimizeTriggeredManually = false,
                                                                                                                                                totalItems,
                                                                                                                                                currentItem,
                                                                                                                                            },
                                                                                                                                            isPrebookCreated = true,
                                                                                                                                            isOptimized,
                                                                                                                                            Method = method,
                                                                                                                                            StartTime = startTime.ToString("yyyy-MMM-dd HH:mm:ss"),

                                                                                                                                            EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                                                                                                                            EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                                                                                                                                        };

                                                                                                                                        var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                                                                                                                                        var irixErrorEntity = new IrixErrorEntity
                                                                                                                                        {
                                                                                                                                            ClassName = "OPTIMIZE_TRIGGERED",
                                                                                                                                            MethodName = method,
                                                                                                                                            RePricerId = repricerId,
                                                                                                                                            ReservationId = Convert.ToInt32(reservation?.ReservationId),
                                                                                                                                            Params = msg
                                                                                                                                        };
                                                                                                                                        _log.Info(msg, irixErrorEntity, true);
                                                                                                                                    }
                                                                                                                                    catch (Exception ex)
                                                                                                                                    {
                                                                                                                                    }

                                                                                                                                    #endregion Logging

                                                                                                                                    //Call IRIX Optimize booking API
                                                                                                                                    optimizationBookingResponse = _optimizationService._3_OptimizeBookingApiIRIX(optimizeBookingReq, reservationIdInLoop, isCacheRefresh: isOptimizeTriggeredManually, isMultiSupplier: false);

                                                                                                                                    if (optimizationBookingResponse != null &&
                                                                                                                                        (optimizationBookingResponse?.NewReservation?.Id > 0
                                                                                                                                        || optimizationBookingResponse?.optimizedBy > 0)
                                                                                                                                     )
                                                                                                                                    {
                                                                                                                                        isOptimized = true;
                                                                                                                                        preBookResponseResult.IsOptimized = true;
                                                                                                                                        isAnyOptimized = true;
                                                                                                                                        isCurrentPrebookOptimized = true;
                                                                                                                                        var optimization = optimizationBookingResponse?.Optimization;
                                                                                                                                        preBookResponseResult.OptimizableStatus = optimization;
                                                                                                                                        // _log.AddOptimizationToQueue(OptimizationStatusEnum.optimized.ToString(), reservationIdInLoop, repricerId, false, true);
                                                                                                                                        _log.UpdateOptimizationToQueue(OptimizationStatusEnum.optimized.ToString(), reservationIdInLoop, repricerId, false, false, isOptimized);
                                                                                                                                        try
                                                                                                                                        {
                                                                                                                                            preBookResponseResult.IsExpectedGainAboveThreshold = true;
                                                                                                                                            Console.ForegroundColor = ConsoleColor.DarkRed;
                                                                                                                                            Console.WriteLine($"\n***********************************************( OPTIMIZATION )**********************************************************");
                                                                                                                                            Console.ForegroundColor = ConsoleColor.Green;
                                                                                                                                            Console.WriteLine($"RepricerId    \t{repricerId}");
                                                                                                                                            Console.WriteLine($"ReservationId \t{reservationIdInLoop}");
                                                                                                                                            Console.WriteLine($"Optimization  \tSAME_SUPPLIER  ");
                                                                                                                                            Console.WriteLine($"SupplierName  \t{offerInfo?.System?.Code}  ");
                                                                                                                                            Console.WriteLine($"Counter       \t({counter}\\{counterTotal})");
                                                                                                                                            Console.WriteLine($"ExpectedGain  \t{dryRunResponse?.ExpectedGain?.Value} {dryRunResponse?.ExpectedGain?.Currency}");
                                                                                                                                            Console.WriteLine($"ActualGain    \t{optimization?.Prices?.Gain?.Value} {optimization?.Prices?.Gain?.Currency}");
                                                                                                                                            Console.WriteLine($"DateTimeLocal \t{DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss")}");
                                                                                                                                            Console.ForegroundColor = ConsoleColor.DarkRed;
                                                                                                                                            Console.WriteLine($"***********************************************( OPTIMIZATION )**********************************************************\n");
                                                                                                                                            Console.ResetColor();
                                                                                                                                        }
                                                                                                                                        catch
                                                                                                                                        {
                                                                                                                                        }
                                                                                                                                    }
                                                                                                                                    else
                                                                                                                                    {
                                                                                                                                        preBookResponseResult.IsOptimized = isOptimized = isCurrentPrebookOptimized = false;
                                                                                                                                    }
                                                                                                                                }
                                                                                                                            }
                                                                                                                            else
                                                                                                                            {
                                                                                                                                _log.UpdateOptimizationToQueue(OptimizationStatusEnum.DryRunOptimizationFailed.ToString(), reservationIdInLoop, repricerId, true, false, false);

                                                                                                                                preBookResponseResult.IsOptimizable = false;
                                                                                                                                isOptimized = false;
                                                                                                                                preBookResponseResult.Status = new Dictionary<int, string>();
                                                                                                                                if (preBookResponseResult.Status != null)
                                                                                                                                {
                                                                                                                                    preBookResponseResult.Status.Add(reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed_AfterDryRun.ToString());
                                                                                                                                }
                                                                                                                            }
                                                                                                                        }
                                                                                                                        else
                                                                                                                        {
                                                                                                                            preBookResponseResult.Status = new Dictionary<int, string>
                                                                                                                        {
                                                                                                                            { reservationIdInLoop, nameof(OptimizationStatusEnum.DryRunOptimizationFailed) }
                                                                                                                        };
                                                                                                                        }

                                                                                                                        #endregion Attempt to optimize qualified prebook
                                                                                                                    }
                                                                                                                    else
                                                                                                                    {
                                                                                                                        try
                                                                                                                        {
                                                                                                                            if (!isPrebookPriceValid)
                                                                                                                            {
                                                                                                                                preBookResponseResult.Status = new Dictionary<int, string>
                                                                                                                        {
                                                                                                                            { reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString() }
                                                                                                                        };
                                                                                                                            }
                                                                                                                            else
                                                                                                                            {
                                                                                                                                preBookResponseResult.Status = new Dictionary<int, string>
                                                                                                                        {
                                                                                                                            { reservationIdInLoop, OptimizationStatusEnum.Repricer_OptimizationNotAllowed.ToString() }
                                                                                                                        };
                                                                                                                            }
                                                                                                                        }
                                                                                                                        catch
                                                                                                                        {
                                                                                                                        }
                                                                                                                    }
                                                                                                                }

                                                                                                                #region Log Prebook into DB

                                                                                                                Task.Run(() =>
                                                                                                                {
                                                                                                                    try
                                                                                                                    {
                                                                                                                        if (isUpdateDB || isCurrentPrebookOptimized)
                                                                                                                        {
                                                                                                                            LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 11, stepSearchtoken, watchcancellation.Elapsed.TotalSeconds, "cancellationfilter", repricerId);

                                                                                                                            var prebookAvailabilityToken = prebook?.availabilityToken ?? string.Empty;

                                                                                                                            _reservationPersistance.InsertPreBookReservation(
                                                                                                                                prebookAvailabilityToken,
                                                                                                                                reservationIdInLoop,
                                                                                                                                repricerId,
                                                                                                                                AppSettingKeys_isUseSendGridValue,
                                                                                                                                searchsyncprice,
                                                                                                                                prebookcount + 1,
                                                                                                                                string.Empty, //emailTemplateEmailTemplate,
                                                                                                                                prebookjson,
                                                                                                                                string.Empty, //emailTemplateReservationTemplate,
                                                                                                                                string.Empty, //emailTemplatePreBookTemplate,
                                                                                                                                profit,
                                                                                                                                profitAfterCancellation,
                                                                                                                                cpResult,
                                                                                                                                token,
                                                                                                                                roomResult,
                                                                                                                                searchsyncjson, CurrencyFactorTOSAVEINDB, clientConfig_DaysDifferenceInPreBookCreation, stepSearchtoken, isCurrentPrebookOptimized
                                                                                                                            );

                                                                                                                            preBookResponseResult.IsPrebookSucess = true;
                                                                                                                            if (isCurrentPrebookOptimized || isAnyOptimized || preBookResponseResult.IsExpectedGainAboveThreshold)
                                                                                                                            {
                                                                                                                                _masterService.RefreshDbAndCachedReport(repricerId, reservationIdInLoop, false);
                                                                                                                            }
                                                                                                                        }
                                                                                                                    }
                                                                                                                    catch (Exception ex)
                                                                                                                    {
                                                                                                                        var irixErrorEntity = new IrixErrorEntity
                                                                                                                        {
                                                                                                                            ClassName = nameof(SearchService),
                                                                                                                            MethodName = nameof(_1_PrebookAndOptimize_SameSupplier_Automatic),
                                                                                                                            Params = $"RePricerId: {repricerId}, ReservationId: {reservationIdInLoop}"
                                                                                                                        };
                                                                                                                        _log.Error(irixErrorEntity, ex);
                                                                                                                        preBookResponseResult.IsPrebookSucess = false;
                                                                                                                    }
                                                                                                                })?.Wait();

                                                                                                                #endregion Log Prebook into DB
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                                #region Prebook Cancellation policy not matched or not found

                                                                                                                #region Update return model

                                                                                                                try
                                                                                                                {
                                                                                                                    if (cancellationresult?.cPStatus?.ToLower() == "tight")
                                                                                                                    {
                                                                                                                        preBookResponseResult.Status = new Dictionary<int, string>();
                                                                                                                        if (preBookResponseResult.Status != null)
                                                                                                                        {
                                                                                                                            preBookResponseResult.Status.Add(reservationIdInLoop, OptimizationStatusEnum.Repricer_TightCancellationPolicy.ToString());
                                                                                                                        }
                                                                                                                    }
                                                                                                                    else if (!isPrebookPriceValid)
                                                                                                                    {
                                                                                                                        preBookResponseResult.Status = new Dictionary<int, string>();
                                                                                                                        if (preBookResponseResult.Status != null)
                                                                                                                        {
                                                                                                                            preBookResponseResult.Status.Add(reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString());
                                                                                                                        }
                                                                                                                    }
                                                                                                                    else
                                                                                                                    {
                                                                                                                        preBookResponseResult.Status = new Dictionary<int, string>();
                                                                                                                        if (preBookResponseResult.Status != null)
                                                                                                                        {
                                                                                                                            preBookResponseResult.Status.Add(reservationIdInLoop, OptimizationStatusEnum.Repricer_NoNewOfferOrMatchedCancellationPolicy.ToString());
                                                                                                                        }
                                                                                                                    }
                                                                                                                }
                                                                                                                catch
                                                                                                                {
                                                                                                                }

                                                                                                                #endregion Update return model

                                                                                                                Task.Run(() =>
                                                                                                                {
                                                                                                                    if (!isOptimized)
                                                                                                                    {
                                                                                                                        if (isUpdateDB)
                                                                                                                        {
                                                                                                                            var roomResult = SerializeDeSerializeHelper.DeSerialize<RoomResult>(roomsdetailJson);
                                                                                                                            var cpResult = SerializeDeSerializeHelper.DeSerialize<CancellationPolicyResult>(cancellationresultJson);

                                                                                                                            _reservationPersistance.InsertPreBookReservationLog(prebook.availabilityToken,
                                                                                                                                    reservationIdInLoop,
                                                                                                                                    repricerId,
                                                                                                                                    AppSettingKeys_isUseSendGridValue,
                                                                                                                                    searchsyncprice,
                                                                                                                                    prebookjson,
                                                                                                                                    profit,
                                                                                                                                    profitAfterCancellation,
                                                                                                                                    cpResult,
                                                                                                                                    token,
                                                                                                                                    roomResult,
                                                                                                                                    searchsyncjson, CurrencyFactorTOSAVEINDB, stepSearchtoken);
                                                                                                                        }
                                                                                                                    }
                                                                                                                });

                                                                                                                #endregion Prebook Cancellation policy not matched or not found
                                                                                                            }
                                                                                                            RedisCacheHelper.ProcessReservation(reservationIdInLoop);

                                                                                                            reservationIds.Add(reservationIdInLoop);
                                                                                                        }
                                                                                                        catch (Exception ex)
                                                                                                        {
                                                                                                            var irixErrorEntity = new IrixErrorEntity
                                                                                                            {
                                                                                                                ClassName = nameof(SearchService),
                                                                                                                MethodName = nameof(_1_PrebookAndOptimize_SameSupplier_Automatic),
                                                                                                            };
                                                                                                            _log.Error(irixErrorEntity, ex);
                                                                                                        }
                                                                                                    }
                                                                                                }
                                                                                                else
                                                                                                {
                                                                                                    preBookResponseResult.IsSearchSucess = false;
                                                                                                    isRoomFoundUsingRoomInfo = false;
                                                                                                    if (prebookresult == null)
                                                                                                    {
                                                                                                        prebookresult = new PreBookResults();
                                                                                                    }
                                                                                                    if (prebookresult.Error == null)
                                                                                                    {
                                                                                                        prebookresult.Error = new Error();
                                                                                                    }
                                                                                                    var msg = $"Reservation and Prebook Supplier are not same!";
                                                                                                    var errorMsg = new Dictionary<int, string>();
                                                                                                    errorMsg.Add(reservationId ?? 0, msg);
                                                                                                    prebookresult.Error.Message = SerializeDeSerializeHelper.Serialize(errorMsg);
                                                                                                    throw new Exception(prebookresult.Error.Message);
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                        else
                                                                                        {
                                                                                            preBookResponseResult.IsSearchSucess = false;
                                                                                            isRoomFoundUsingRoomInfo = false;
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            try
                                                            {
                                                                //just to breakpoint of else part
                                                                _log.Info($"{nameof(SearchService)}|{nameof(_1_PrebookAndOptimize_SameSupplier_Automatic)}| !_searchserviceHelper.GetReservationEmailStatus(reservationId = {reservationIdInLoop}, offerInfoId = {offerInfo.id})");
                                                            }
                                                            catch (Exception)
                                                            {
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        preBookResponseResult.IsSearchSucess = false;
                                    }

                                    prebookresultresp.Add(preBookResponseResult);
                                }
                                catch (Exception ex)
                                {
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = constants.SearchService,
                                        MethodName = "InnerCreateSearch",
                                    };
                                    _log.Error(irixErrorEntity, ex);
                                    throw;
                                }
                            }
                            else
                            {
                                if (preBookResponseResult.Status == null)
                                {
                                    preBookResponseResult.Status = new Dictionary<int, string>
                                    {
                                      { reservationIdInLoop, OptimizationStatusEnum.Repricer_NotOptimizable.ToString() }
                                    };
                                }
                            }
                            //}

                            try
                            {
                                #region logging at END

                                var logEntry = new LogParams
                                {
                                    RepricerId = repricerId,
                                    Counter = counter,
                                    CounterTotal = counterTotal,
                                    StartTime = startTime,
                                    OffersFound = offersFound,
                                    IsMultiSupplier = false,
                                    ReservationId = reservation?.ReservationId ?? 0,
                                    IsUpdateDB = isUpdateDB,
                                    SupplierName = "SameAsOriginal",
                                    IsOptimizeTriggeredManually = isOptimizeTriggeredManually,
                                    TotalItems = totalItems,
                                    CurrentItem = currentItem,
                                    IsPrebookCreated = false,
                                    IsOptimized = false,
                                    Method = method ?? string.Empty,
                                    Params = new
                                    {
                                        repricerId,
                                        reservationId = reservation?.ReservationId ?? 0,
                                        isUpdateDB,
                                        isMultiSupplier = false,
                                        supplierName = "SameAsOriginal",
                                        isOptimizeTriggeredManually = isOptimizeTriggeredManually,
                                        totalItems,
                                        currentItem
                                    },
                                };

                                _log.LogDynamic(logEntry);

                                #endregion logging at END

                                _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: true, isCheckAndSet: false);
                            }
                            catch (Exception ex)
                            {
                            }
                        }
                        catch (Exception ex)
                        {
                            _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: true, isCheckAndSet: false);

                            #region Logging on Error

                            try
                            {
                                var logInfoEntry = new
                                {
                                    RepricerId = repricerId,
                                    Counter = $"{counter}\\{counterTotal})   END",
                                    Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                    OffersFound = offersFound,
                                    reservation?.ReservationId,
                                    isPrebookCreated = false,
                                    isOptimized = false,
                                    Method = "SameSupplier",
                                    StartTime = startTime,
                                    EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                    EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                                };

                                var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = _className,
                                    MethodName = "SameSupplier",
                                    RePricerId = repricerId,
                                    ReservationId = Convert.ToInt32(reservationId),
                                    Params = msg
                                };
                                _log.Info(msg, irixErrorEntity, true);
                            }
                            catch (Exception ex1)
                            {
                            }

                            #endregion Logging on Error

                            if (prebookresult == null)
                            {
                                prebookresult = new PreBookResults();
                                prebookresult.PreBookResponseResults = new List<PreBookResponseResult>();
                                prebookresult.PreBookResponseResults.Add(new PreBookResponseResult
                                {
                                    RepricerId = repricerId,
                                    ReservationId = reservation?.ReservationId ?? reservationId ?? 0,
                                });
                            }
                            if (prebookresult.Error == null)
                            {
                                prebookresult.Error = new Error();
                            }
                            prebookresult.Error.Message = $"{ex.Message}";
                        }
                        finally
                        {
                            var searchkey = $"{ServiceAdapterConstants.SearchSync}_{reservationId}";
                            var prebookkey = $"{ServiceAdapterConstants.PreBook}_{reservationId}";
                            RedisCacheHelper.Remove(searchkey);
                            RedisCacheHelper.Remove(prebookkey);
                            if (repricerId == 29 || repricerId == 36)
                            {
                                Task.Delay(TimeSpan.FromSeconds(5))?.GetAwaiter().GetResult();
                            }
                            else
                            {
                                Task.Delay(TimeSpan.FromSeconds(1))?.GetAwaiter().GetResult();
                            }
                        }
                    });
                }

                #endregion Try Block
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constants.SearchService,
                    MethodName = "CreateSearch",
                };
                try
                {
                    _log.Error(irixErrorEntity, ex);
                    if (prebookresult == null)
                    {
                        prebookresult = new PreBookResults();
                    }
                    if (prebookresult.Error == null)
                    {
                        prebookresult.Error = new Error();
                    }
                    prebookresult.Error.Message = $"{ex.Message}\n{ex.StackTrace}";
                    prebookresult.Error.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                }
                catch (Exception ex1)
                {
                }
                //throw;
            }

            if (isUpdateDB)
            {
                var itemJobName = $"{repricerId.ToString("000")}_8_1_UPDATE_RRD_Same_Supplier".ToUpper();
                try
                {
                    var startTimeItem = DateTime.UtcNow;

                    if (reservationId > 0 && (isAnyOptimized || isCurrentPrebookOptimized))
                    {
                        itemJobName = $"{repricerId.ToString("000")}_{reservationId}_8_1_UPDATE_RRD_Same_Supplier".ToUpper();

                        _reservationPersistance.InsertReportinTable(repricerId, reservationId ?? 0);
                        _log.InfoV1(repricerId, _className, itemJobName, $"InsertReportinTable", startTimeItem, true, "end");

                        if (isAnyOptimized || isOptimizeTriggeredManually)
                        {
                            var refreshTask = Task.Run(() =>
                            {
                                _masterService.RefreshCache(repricerId);
                            });
                        }
                    }
                    //else if (reservationId == 0)
                    //{
                    //    _log.InfoV1(repricerId, _className, itemJobName, $"InsertReportinTable", startTimeItem, true, "start");
                    //    _reservationPersistence.InsertReportinTable(repricerId);
                    //    _log.InfoV1(repricerId, _className, itemJobName, $"InsertReportinTable", startTimeItem, true, "end");
                    //}

                    //disable here as it will run in multi-supplier step
                    //_masterService.RefreshCache(repricerId);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = itemJobName,
                        RePricerId = repricerId,
                        Params = itemJobName
                    };
                    _log.Error(irixErrorEntity, ex);
                }
            }

            if (prebookresultresp != null && prebookresultresp.Count() > 0)
            {
                prebookresult.PreBookResponseResults = prebookresultresp;
            }

            return prebookresult;
        }

        public async Task<List<int>> CreateDifferentSupplierSearch(int Repriceruserid)
        {
            var watch = Stopwatch.StartNew();

            List<int> reservationIds = new List<int>();

            try
            {
                var watchCriteria = Stopwatch.StartNew();
                var searchcriteria = await _reservationPersistance.GetReservationsAsync(Repriceruserid);
                var roomReservation = _reservationPersistance.GetReservationsRoomAsync(Repriceruserid)?.GetAwaiter().GetResult();
                var reservationids = _reservationPersistance.GetPrebookReservationIdsAsync(Repriceruserid).GetAwaiter().GetResult();
                var cancellationpolicy = _reservationPersistance.GetCancellationPolicyReservationIdsAsync(Repriceruserid).GetAwaiter().GetResult();
                var extraClientConfig = _clientServices.GetClientEmail(Repriceruserid);
                List<ReservationPrebookCount> reservationids_maxcount = null;

                if (searchcriteria != null && reservationids != null && extraClientConfig != null)
                {
                    reservationids_maxcount = reservationids
                               .GroupBy(r => r.ReservationId)
                               .Select(group => new ReservationPrebookCount
                               {
                                   ReservationId = group.Key,
                                   PreBookCount = group.Max(r => r.PreBookCount),
                                   CreateDate = group.FirstOrDefault(r => r.PreBookCount == group.Max(p => p.PreBookCount))?.CreateDate ?? DateTime.MinValue
                               })
                               .ToList();

                    searchcriteria = searchcriteria
                                     .Where(sc =>
                                         (!reservationids_maxcount.Any(r =>
                                             r.ReservationId == sc.ReservationId &&
                                             r.PreBookCount >= extraClientConfig.MaxNumberOfTimesOptimization))).ToList();
                }

                var filteredReservations = searchcriteria?.ToList(); //.Where(x => x.ReservationId == 126946).ToList();

                List<string> SupplierArray = filteredReservations
                                                .Select(x => x.supplierName)
                                                .Where(name => !string.IsNullOrEmpty(name) && name != "null")
                                                .ToList();

                watchCriteria.Stop();

                cancellationpolicy = cancellationpolicy
                    .Where(cancellation =>
                        filteredReservations.Any(reservation => reservation.ReservationId == cancellation.ReservationId))
                    .ToList();

                Guid newGuidtoken = Guid.NewGuid();
                string JobToken = newGuidtoken.ToString();
                var prebookrequest = new PackageRequest();
                if (filteredReservations != null && filteredReservations.Count > 0)
                {
                    //Parallel.ForEach(searchcriteria, constants.GetParallelOptions25 , reservation =>125108
                    foreach (var reservation in filteredReservations)
                    {
                        var reservationIdInLoop = reservation.ReservationId;
                        try
                        {
                            int prebookcount = 0;
                            var maxprebookcount = reservationids_maxcount.FirstOrDefault(r => r.ReservationId == reservation.ReservationId);

                            var cancellationpolicybyreservationid = cancellationpolicy.Where(cp => cp.ReservationId == reservation.ReservationId).ToList();

                            if (maxprebookcount != null)
                            {
                                prebookcount = maxprebookcount.PreBookCount;
                            }

                            var criteria = _searchserviceHelper.CreateCriteria(reservation, roomReservation, SupplierArray);

                            var searchresponse = _irixAdapter.SearchForBooking(reservation.ReservationId, criteria, constants.GetAllBooking, Repriceruserid);

                            if (searchresponse != null)
                            {
                                _searchserviceHelper.InsertSupplierSearchSyncData(searchresponse.hotels, Repriceruserid, reservation.ReservationId, JobToken).GetAwaiter().GetResult();

                                var prebookcriteriaresult = (_reservationPersistance?.GetPreBookCriteria(reservation.ReservationId, Repriceruserid))?.GetAwaiter().GetResult();
                                var prebookcriteria = prebookcriteriaresult?.PreBookCriteriaList;

                                var isRoomBoard = true;
                                var isRoomInfo = true;

                                if (prebookcriteria != null && prebookcriteria.Count() > 0)
                                {
                                    var watchOfer = Stopwatch.StartNew();
                                    watchOfer.Stop();
                                    var offerInfoList = GetOfferListFromSearchResponse(searchresponse, prebookcriteria, Repriceruserid, reservation.ReservationId);

                                    watchOfer.Stop();
                                    _log.Info($"CreateSearch|PreBookSelectingOfferSupplierArray|{reservation.ReservationId} {watchOfer.Elapsed}");
                                    if (offerInfoList != null && offerInfoList.Count > 0)
                                    {
                                        foreach (var offerInfo in offerInfoList)
                                        {
                                            if (offerInfo.System.Code != reservation.supplierName)
                                            {
                                                var watchroomselection = Stopwatch.StartNew();

                                                var mainRoomInfoList = new List<SearchRoom_Package>();
                                                var matchedRoomInfoList = new List<SearchRoom_Package>();
                                                var packageInfo = new Package();
                                                var selectedRoomTokens = new List<string>();

                                                UpdateMatchingRoomOffer(
                                                                            mainRoomInfoList
                                                                            , matchedRoomInfoList
                                                                            , offerInfo
                                                                            , prebookcriteria
                                                                            , selectedRoomTokens
                                                                            , ref packageInfo
                                                                            , Repriceruserid
                                                                            , reservation.ReservationId

                                                                            );
                                                if (!_searchserviceHelper.GetReservationEmailStatus(reservation.ReservationId, offerInfo.id, Repriceruserid))
                                                {
                                                    var roominfofromoffersList = new List<SearchRoom>();
                                                    if (prebookcriteria != null)
                                                    {
                                                        watchroomselection.Stop();
                                                        _log.Info($"CreateSearch|RoomSelectionSupplierSearch|{reservation.ReservationId} {watchroomselection.Elapsed} ");

                                                        if (selectedRoomTokens != null && selectedRoomTokens.Count > 0)
                                                        {
                                                            if (packageInfo != null)
                                                            {
                                                                decimal pricethresholdInOriginalReservationCurrency = 0.0m;
                                                                if (prebookcriteria.Count() == selectedRoomTokens.Count())
                                                                {
                                                                    if (selectedRoomTokens != null)
                                                                    {
                                                                        var packageinfo = packageInfo;

                                                                        if (packageinfo != null)
                                                                        {
                                                                            double? hotelPrice = 0.0;
                                                                            double? RoomlevelPrice = 0.0;
                                                                            string? hotelCurrency;
                                                                            string? RoomLevelCurrency = null;
                                                                            decimal hotellevelfactor = 0.0m;
                                                                            var isRoomLevelPriceMatched = true;
                                                                            var packagelevelprice = packageinfo.Price?.components?.supplier?.value;
                                                                            var packagelevelcurrency = packageinfo.Price?.components?.supplier?.currency;
                                                                            var packagelevelfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(Repriceruserid, packagelevelcurrency, prebookcriteriaresult.Currency));
                                                                            var CurrencyFactorTOSAVEINDB = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(Repriceruserid, prebookcriteriaresult.Currency, "EUR"));
                                                                            if (matchedRoomInfoList.Any(room => room.SearchRoomPrice == null))
                                                                            {
                                                                                isRoomLevelPriceMatched = false;
                                                                                hotelPrice = packagelevelprice;
                                                                                hotelCurrency = packagelevelcurrency;
                                                                                hotellevelfactor = packagelevelfactor;
                                                                            }
                                                                            else
                                                                            {
                                                                                RoomlevelPrice = matchedRoomInfoList.Sum(room => room.SearchRoomPrice ?? 0.0);
                                                                                RoomLevelCurrency = matchedRoomInfoList[0].SearchRoomCurrency;
                                                                                var RoomlevelFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(Repriceruserid, RoomLevelCurrency, prebookcriteriaresult.Currency));
                                                                                hotelPrice = RoomlevelPrice;
                                                                                hotelCurrency = RoomLevelCurrency;
                                                                                hotellevelfactor = RoomlevelFactor;
                                                                            }

                                                                            string roomLevelInfo = RoomlevelPrice.ToString() + " " + (RoomLevelCurrency ?? "");
                                                                            string packageLevelInfo = packagelevelprice.ToString() + " " + (packagelevelcurrency ?? "");

                                                                            if (hotelPrice > 0)
                                                                            {
                                                                                if ((extraClientConfig?.PriceDifferenceValue) != 0)
                                                                                {
                                                                                    var ClientFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(Repriceruserid, extraClientConfig?.pricedifferencecurrency, prebookcriteriaresult.Currency));
                                                                                    pricethresholdInOriginalReservationCurrency = _searchserviceHelper.RoundToDecimalPlaces(ClientFactor * extraClientConfig.PriceDifferenceValue);
                                                                                }

                                                                                var packageToken = packageinfo.PackageToken;
                                                                                prebookrequest = _searchserviceHelper.CreatePreBookCriteria(packageToken, selectedRoomTokens?.ToList());

                                                                                var searchsyncprice = _searchserviceHelper.RoundToDecimalPlaces(System.Convert.ToDecimal(hotelPrice) * hotellevelfactor);
                                                                                var profit = _searchserviceHelper.RoundToDecimalPlaces(prebookcriteriaresult.IssueNet - searchsyncprice);

                                                                                var profitperc = _searchserviceHelper.RoundToDecimalPlaces((profit / prebookcriteriaresult.IssueNet) * 100);
                                                                                var originalReservationPrice = prebookcriteriaresult.IssueNet;

                                                                                if (searchsyncprice > 0 && prebookcriteriaresult.IssueNet > searchsyncprice)
                                                                                {
                                                                                    var roomsdetail = _searchserviceHelper.GetRoomResult(prebookcriteriaresult, matchedRoomInfoList, reservation, offerInfo.System);
                                                                                    var searchsyncjson = SerializeDeSerializeHelper.Serialize(searchresponse);

                                                                                    _searchserviceHelper.InsertIntoPrebookSupplierlog(
                                                                                            reservation.ReservationId,
                                                                                            Repriceruserid,
                                                                                            roomsdetail,
                                                                                            prebookcriteria?.FirstOrDefault()?.HotelName ?? "Unknown",
                                                                                            profitperc,
                                                                                            profit,
                                                                                            searchsyncprice,
                                                                                            prebookcriteria?.FirstOrDefault()?.Currency ?? "Unknown",
                                                                                            reservation?.CriteriaJson, searchsyncjson, CurrencyFactorTOSAVEINDB, JobToken
                                                                                        ).GetAwaiter().GetResult();

                                                                                    var isPriceThreshold = false;
                                                                                    if (extraClientConfig?.IsUsePercentage == true && profitperc >= extraClientConfig.PriceDifferencePercentage)
                                                                                    {
                                                                                        isPriceThreshold = true;
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        if (extraClientConfig?.IsUsePercentage != true
                                                                                            && profit >= pricethresholdInOriginalReservationCurrency
                                                                                            )
                                                                                        {
                                                                                            isPriceThreshold = true;
                                                                                        }
                                                                                    }

                                                                                    if (isPriceThreshold || extraClientConfig.IsCreatePrebookFoPriceEdgeCase)
                                                                                    {
                                                                                        var offerIdForRoomInfo = offerInfo.id;

                                                                                        var srk = searchresponse?.srk;
                                                                                        var prebooktokens = searchresponse?.tokens.results;
                                                                                        var hotelsIndex = searchresponse?.hotels[0].index;
                                                                                        var offerIndex = offerIdForRoomInfo;
                                                                                        var prebook = _irixAdapter.Prebookresponse(reservation.ReservationId, prebookrequest, constants.prebookresp, Repriceruserid, prebooktokens, srk, offerIndex, hotelsIndex);

                                                                                        if (prebook != null)
                                                                                        {
                                                                                            try
                                                                                            {
                                                                                                var cancellationresult = _searchserviceHelper.CancellationCheck(Repriceruserid, cancellationpolicybyreservationid, prebook.cancellationPolicy, prebookcriteriaresult, searchsyncprice, reservation);
                                                                                                var prebookjson = SerializeDeSerializeHelper.Serialize(prebook);

                                                                                                var AppSettingKeys_isUseSendGrid = ConfigurationManagerHelper.GetValuefromAppSettings(constants.AppSettingKeys_isUseSendGrid);
                                                                                                int AppSettingKeys_isUseSendGridValue = !string.IsNullOrEmpty(AppSettingKeys_isUseSendGrid)
                                                                                                    ? Convert.ToInt32(AppSettingKeys_isUseSendGrid)
                                                                                                    : 0;

                                                                                                if (cancellationresult == null || cancellationresult.CancellationType == "standard")
                                                                                                {
                                                                                                    var watchPreBook = Stopwatch.StartNew();

                                                                                                    var cancellationfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(Repriceruserid, cancellationresult?.Currency, prebookcriteriaresult.Currency));
                                                                                                    decimal cancellationamount = 0;

                                                                                                    if (cancellationresult.CancellationCharge != 0)
                                                                                                    {
                                                                                                        cancellationamount = _searchserviceHelper.RoundToDecimalPlaces(cancellationresult.CancellationCharge * cancellationfactor);
                                                                                                    }

                                                                                                    var profitaftercancellation = profit - cancellationamount;
                                                                                                    if (profitaftercancellation > 0)
                                                                                                    {
                                                                                                        Guid newGuid = Guid.NewGuid();
                                                                                                        string token = newGuid.ToString();
                                                                                                        _searchserviceHelper.InsertPreBookClientConfiguration(extraClientConfig, Repriceruserid, reservation.ReservationId, token);

                                                                                                        if (profitaftercancellation >= pricethresholdInOriginalReservationCurrency && cancellationresult.cPStatus == "loose")
                                                                                                        {
                                                                                                            var emailTemplate = _searchserviceHelper.SendReservationEmail(reservation.ReservationId, reservation, prebookcriteriaresult, matchedRoomInfoList, Repriceruserid, profit, packagelevelcurrency, profitperc, extraClientConfig, prebook.availabilityToken, searchsyncprice, prebook, isRoomLevelPriceMatched, roomLevelInfo, packageLevelInfo, pricethresholdInOriginalReservationCurrency, prebookcount + 1, cancellationresult, cancellationamount, profitaftercancellation);

                                                                                                            string emailCachingInHrsValue = ConfigurationManagerHelper.GetValuefromAppSettings(constants.EmailCachinginHrs);
                                                                                                            double emailCachingInHrs;

                                                                                                            string key = $"RepriceremailSend_{Repriceruserid}_{reservation.ReservationId}";

                                                                                                            if (double.TryParse(emailCachingInHrsValue, out emailCachingInHrs))
                                                                                                            {
                                                                                                                RedisCacheHelper.Set(key, true, TimeSpan.FromHours(emailCachingInHrs));
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                                RedisCacheHelper.Set(key, true, TimeSpan.FromHours(1));
                                                                                                            }

                                                                                                            watchPreBook.Stop();
                                                                                                            _log.Info($"CreateSearch|PreBookCreation|{reservation.ReservationId} {watchPreBook.Elapsed}");

                                                                                                            _reservationPersistance.InsertPreBookReservationSupplierSearch(
                                                                                                                        prebook.availabilityToken,
                                                                                                                        reservation.ReservationId,
                                                                                                                        Repriceruserid,
                                                                                                                        AppSettingKeys_isUseSendGridValue,
                                                                                                                        searchsyncprice,
                                                                                                                        prebookcount + 1,
                                                                                                                        emailTemplate.EmailTemplate,
                                                                                                                        prebookjson,
                                                                                                                        emailTemplate.ReservationTemplate,
                                                                                                                        emailTemplate.PreBookTemplate,
                                                                                                                        profit,
                                                                                                                        profitaftercancellation,
                                                                                                                        cancellationresult,
                                                                                                                        token,
                                                                                                                        roomsdetail,
                                                                                                                        searchsyncjson, CurrencyFactorTOSAVEINDB, extraClientConfig.ClientConfig_DaysDifferenceInPreBookCreation, JobToken
                                                                                                                    );
                                                                                                        }
                                                                                                        else
                                                                                                        {
                                                                                                            _reservationPersistance.InsertPreBookReservationSupplierSearchLog(prebook.availabilityToken,
                                                                                                                        reservation.ReservationId,
                                                                                                                        Repriceruserid,
                                                                                                                        AppSettingKeys_isUseSendGridValue,
                                                                                                                        searchsyncprice,
                                                                                                                        prebookjson,
                                                                                                                        profit,
                                                                                                                        profitaftercancellation,
                                                                                                                        cancellationresult,
                                                                                                                        token,
                                                                                                                        roomsdetail,
                                                                                                                        searchsyncjson, CurrencyFactorTOSAVEINDB, JobToken);
                                                                                                        }
                                                                                                    }
                                                                                                    reservationIds.Add(reservation.ReservationId);
                                                                                                }
                                                                                            }
                                                                                            catch (Exception ex)
                                                                                            {
                                                                                                var irixErrorEntity = new IrixErrorEntity
                                                                                                {
                                                                                                    ClassName = nameof(SearchService),
                                                                                                    MethodName = nameof(GetOfferListFromSearchResponse),
                                                                                                    Params = $"RePricerId: {Repriceruserid}, ReservationId: {reservationIdInLoop}"
                                                                                                };

                                                                                                _log.Error(irixErrorEntity, ex);
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = constants.SearchService,
                                MethodName = "InnerCreateSearch",
                            };
                            _log.Error(irixErrorEntity, ex);
                            //throw;
                        }
                    }
                    //});
                }
                watch.Stop();
                var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
                _log.Info($"SearchService|CreateSearch|{elapsedTimeInSeconds} in {watch.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constants.SearchService,
                    MethodName = "CreateSearch",
                };
                _log.Error(irixErrorEntity, ex);
                //throw;
            }

            return reservationIds;
        }

        public bool UpdateMatchingRoomOffer(
             List<SearchRoom_Package> mainRoomInfoList,
             List<SearchRoom_Package> matchedRoomInfoList,
             OfferInfo offerInfo,
             List<PrebookCriteria> prebookCriteria,
             List<string> selectedRoomTokens,
             ref Package packageInfo,
             int rePricerId,
             int reservationId
        )
        {
            bool isRoomFound = false;

            try
            {
                mainRoomInfoList.Clear();
                matchedRoomInfoList.Clear();
                selectedRoomTokens.Clear();

                foreach (var criteria in prebookCriteria)
                {
                    if (criteria != null)
                    {
                        var rooms = offerInfo?.rooms
                                            ?.Where
                                            (
                                                room =>

                                                room != null &&  // Add a null check for room
                                                NormalizeString(room.name) == NormalizeString(criteria?.RoomName)
                                                && !string.IsNullOrWhiteSpace(NormalizeString(room.name))
                                                && !string.IsNullOrWhiteSpace(NormalizeString(criteria?.RoomName))
                                                && (
                                                    NormalizeString(room?.board)
                                                    ==
                                                    NormalizeString(criteria?.RoomBoard)
                                                    && !string.IsNullOrWhiteSpace(NormalizeString(criteria?.RoomBoard))
                                                    && !string.IsNullOrWhiteSpace(NormalizeString(criteria?.RoomBoard))
                                                )
                                            )
                                            ?.Distinct()
                                            ?.ToList();

                        if (string.IsNullOrEmpty(criteria.RoomBoard))
                        {
                            rooms = rooms?.Where(room => string.IsNullOrEmpty(room?.board))?.ToList();
                        }

                        if (rooms != null && rooms.Count > 0)
                        {
                            foreach (var room in rooms)
                            {
                                var roomInfo = new SearchRoom_Package
                                {
                                    RoomIndex = room.index,
                                    RoomName = room.name,
                                    RoomBoard = room.board,
                                    RoomInfo = room.info,
                                    PassengerCount = criteria.passengerCount,
                                    ChildAges = _searchserviceHelper.ExtractIntegers(criteria.ChildAges),
                                    SearchRoomPrice = room.price?.components?.supplier?.value,
                                    SearchRoomCurrency = room.price?.components?.supplier?.currency,
                                    NonRefundable = room.nonRefundable,
                                };

                                mainRoomInfoList.Add(roomInfo);
                            }
                        }
                    }
                }
                mainRoomInfoList = mainRoomInfoList.Distinct().ToList();

                var packageInfoQuery = from pack in offerInfo?.packages
                                       from matchedRoom in mainRoomInfoList
                                       from pr in pack?.PackageRooms
                                       from rr in pr?.roomReferences
                                       from criteria in prebookCriteria
                                       let criteriaChildAges = criteria.ChildAges == null || criteria.ChildAges == "[]"
                                           ? new List<int>()
                                           : _searchserviceHelper.ExtractIntegers(criteria.ChildAges).ToList() // Ensure null is treated as empty list
                                       let packageRoomChildrenAges = pr?.occupancy?.childrenAges?.ToList() ?? new List<int>()
                                       let matchedRoomChildrenAges = matchedRoom.ChildAges?.ToList() ?? new List<int>()

                                       where pack != null
                                           && pack.Price?.components?.supplier != null
                                           && pr != null
                                           && rr != null
                                           && rr.roomCode.ToString() == matchedRoom.RoomIndex
                                           && criteria.passengerCount == matchedRoom.PassengerCount
                                           && criteria.passengerCount == pr.occupancy.adults
                                           && (criteriaChildAges.Count == 0
                                               || (criteriaChildAges.SequenceEqual(packageRoomChildrenAges)
                                               && criteriaChildAges.SequenceEqual(matchedRoomChildrenAges)))
                                           && NormalizeString(matchedRoom.RoomBoard) == NormalizeString(criteria.RoomBoard)
                                       orderby (matchedRoom.SearchRoomPrice > 0 ? matchedRoom.SearchRoomPrice
                                               : pack.Price.components.supplier.value) ascending

                                       select new Package
                                       {
                                           OfferId = offerInfo?.id,
                                           Supplier = offerInfo?.System?.Code,
                                           Complete = pack.Complete,
                                           PackageCode = pack.PackageCode,
                                           PackageRooms = pack.PackageRooms,
                                           PackageToken = pack.PackageToken,
                                           Price = pack.Price,
                                       };

                packageInfo = packageInfoQuery
                                ?.Where(p => p != null && p?.Price?.components?.supplier != null)
                                ?.OrderBy(p => p?.Price?.components?.supplier?.value)
                                ?.FirstOrDefault();

                var packageInfoQueryNew = from pack in offerInfo?.packages
                                          from matchedRoom in mainRoomInfoList
                                          from pr in pack?.PackageRooms
                                          from rr in pr?.roomReferences
                                          from criteria in prebookCriteria
                                          let criteriaChildAges = criteria.ChildAges == null || criteria.ChildAges == "[]"
                                              ? new List<int>()
                                              : _searchserviceHelper.ExtractIntegers(criteria.ChildAges).ToList() // Ensure null is treated as empty list
                                          let packageRoomChildrenAges = pr?.occupancy?.childrenAges?.ToList() ?? new List<int>()
                                          let matchedRoomChildrenAges = matchedRoom.ChildAges?.ToList() ?? new List<int>()

                                          where pack != null
                                              && pack.Price?.components?.supplier != null
                                              && pr != null
                                              && rr != null
                                              && rr.roomCode.ToString() == matchedRoom.RoomIndex
                                              && criteria.passengerCount == matchedRoom.PassengerCount
                                              && criteria.passengerCount == pr.occupancy.adults
                                              && (criteriaChildAges.Count == 0
                                                  || (criteriaChildAges.SequenceEqual(packageRoomChildrenAges)
                                                  && criteriaChildAges.SequenceEqual(matchedRoomChildrenAges)))
                                              && NormalizeString(matchedRoom.RoomBoard) == NormalizeString(criteria.RoomBoard)
                                          orderby (matchedRoom.SearchRoomPrice > 0 ? matchedRoom.SearchRoomPrice
                                                  : pack.Price.components.supplier.value) ascending
                                          select new
                                          {
                                              matchedRoom,
                                              rr,
                                              PackageRoomCode = pr.packageRoomCode,
                                              pack = new Package
                                              {
                                                  OfferId = offerInfo?.id,
                                                  Supplier = offerInfo?.System?.Code,
                                                  Complete = pack.Complete,
                                                  PackageCode = pack.PackageCode,
                                                  PackageRooms = pack.PackageRooms,
                                                  PackageToken = pack.PackageToken,
                                                  Price = pack.Price,
                                              }
                                          };

                // First, get all the rooms with their details
                var pi5 = packageInfoQueryNew?.OrderBy(x => x.matchedRoom.SearchRoomPrice)
                                ?.Select(y => new RoomPackageInfo
                                {
                                    RoomIndex = y.matchedRoom.RoomIndex,
                                    RoomName = y.matchedRoom.RoomName,
                                    RoomBoard = y.matchedRoom.RoomBoard,
                                    RoomInfo = y.matchedRoom.RoomInfo,
                                    PassengerCount = y.matchedRoom.PassengerCount,
                                    ChildAges = y.matchedRoom.ChildAges,
                                    SearchRoomPrice = y.matchedRoom.SearchRoomPrice,
                                    NonRefundable = y.matchedRoom.NonRefundable,
                                    SearchRoomCurrency = y.matchedRoom.SearchRoomCurrency,
                                    selected = y.rr.selected,
                                    roomToken = y.rr.roomToken,
                                    roomCode = y.rr.roomCode,
                                    PackageRoomCode = y.PackageRoomCode,
                                    PackageToken = y.pack.PackageToken,
                                    OfferId = y.pack.OfferId,
                                    Supplier = y.pack.Supplier
                                })?.Distinct().ToList();

                // Check if we need to filter by prebookCriteria
                if (prebookCriteria != null && prebookCriteria.Count > 0 && pi5 != null && pi5.Count > 0)
                {
                    try
                    {
                        // Group rooms by PackageToken
                        var roomsByPackage = pi5.GroupBy(r => r.PackageToken)
                                                .Select(g => new
                                                {
                                                    PackageToken = g.Key,
                                                    Rooms = g.ToList(),
                                                    RoomCount = g.Count()
                                                })
                                                .ToList();

                        // Filter packages that have the exact number of rooms matching the prebookCriteria
                        var matchingPackages = roomsByPackage
                                                .Where(p => p.RoomCount == prebookCriteria.Count)
                                                .Where(p =>
                                                {
                                                    // Group rooms by room name and board to count them
                                                    var roomGroups = p.Rooms
                                                        .GroupBy(r => new
                                                        {
                                                            RoomName = NormalizeString(r.RoomName),
                                                            RoomBoard = NormalizeString(r.RoomBoard)
                                                        })
                                                        .ToDictionary(g => g.Key, g => g.Count());

                                                    // Group prebookCriteria by room name and board to count them
                                                    var criteriaGroups = prebookCriteria
                                                        .GroupBy(pc => new
                                                        {
                                                            RoomName = NormalizeString(pc.RoomName),
                                                            RoomBoard = NormalizeString(pc.RoomBoard)
                                                        })
                                                        .ToDictionary(g => g.Key, g => g.Count());

                                                    // Check if the counts match for each room type
                                                    return roomGroups.Count == criteriaGroups.Count &&
                                                           roomGroups.All(rg =>
                                                               criteriaGroups.TryGetValue(rg.Key, out int count) &&
                                                               count == rg.Value);
                                                })
                                                .Select(p => p.PackageToken)
                                                .ToList();

                        // Filter the original rooms list to only include rooms from matching packages
                        if (matchingPackages.Count > 0)
                        {
                            pi5 = pi5.Where(r => matchingPackages.Contains(r.PackageToken)).ToList();
                            packageInfo = packageInfoQuery?.FirstOrDefault(x => x.PackageToken == pi5?.FirstOrDefault()?.PackageToken);
                        }
                        else
                        {
                            // No matching packages found, use an empty list with the correct type
                            pi5 = new List<RoomPackageInfo>();
                            packageInfo = null;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error but continue with the original list
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(UpdateMatchingRoomOffer),
                            Params = $"Error filtering packages: {ex.Message}"
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }

                if (pi5 != null && pi5.Count > 0)
                {
                    foreach (var ct in prebookCriteria)
                    {
                        var selectedList = pi5.Where(x => x?.RoomName?.ToLower()?.Trim() == ct?.RoomName?.ToLower()?.Trim()
                        && x?.RoomBoard?.ToLower()?.Trim() == ct?.RoomBoard?.ToLower()?.Trim()
                        )?.ToList();
                        if (selectedList?.Count > 0)
                        {
                            foreach (var selected in selectedList)
                            {
                                if (selected != null)
                                {
                                    if (matchedRoomInfoList.Any(x =>
                                     x?.PackageRoomCode == selected?.PackageRoomCode
                                    ) == false)
                                    {
                                        try
                                        {
                                            var room = new SearchRoom_Package
                                            {
                                                ChildAges = selected.ChildAges,
                                                RoomName = selected.RoomName,
                                                PassengerCount = selected.PassengerCount,
                                                NonRefundable = selected.NonRefundable,
                                                RoomBoard = selected.RoomBoard,
                                                RoomIndex = selected.RoomIndex,
                                                RoomInfo = selected.RoomInfo,
                                                SearchRoomCurrency = selected.SearchRoomCurrency,
                                                SearchRoomPrice = selected.SearchRoomPrice,
                                                PackageRoomCode = selected.PackageRoomCode,
                                                RoomToken = selected.roomToken,
                                                PackageToken = selected.PackageToken,
                                                SearchOfferId = selected.OfferId,
                                                SearchSupplier = selected.Supplier,
                                            };

                                            if (matchedRoomInfoList.Any(x =>
                                                x?.PackageRoomCode == room?.PackageRoomCode
                                                && x?.RoomName == room?.RoomName
                                                && x?.RoomIndex == room?.RoomIndex
                                                && x?.RoomToken == room?.RoomToken
                                               ) == false)
                                            {
                                                matchedRoomInfoList.Add(room);
                                                selectedRoomTokens.Add(selected.roomToken);
                                                isRoomFound = true;
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            var irixErrorEntity = new IrixErrorEntity
                                            {
                                                ClassName = nameof(SearchService),
                                                MethodName = nameof(UpdateMatchingRoomOffer),
                                                Params = $"RePricerId: {rePricerId}, ReservationId: {reservationId}, RoomName: {selected?.RoomName}"
                                            };
                                            mainRoomInfoList = new List<SearchRoom_Package>();
                                            matchedRoomInfoList = new List<SearchRoom_Package>();
                                            offerInfo = new OfferInfo();
                                            selectedRoomTokens = new List<string>();
                                            _log.Error(irixErrorEntity, ex);
                                            isRoomFound = false;
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (mainRoomInfoList?.All(room => room?.SearchRoomPrice != null && room?.SearchRoomPrice != 0) == true)
                    {
                        mainRoomInfoList = mainRoomInfoList?.OrderBy(x => x?.SearchRoomPrice)?.ToList();
                    }
                    if (matchedRoomInfoList?.All(room => room?.SearchRoomPrice != null && room?.SearchRoomPrice != 0) == true)
                    {
                        matchedRoomInfoList = matchedRoomInfoList?.OrderBy(x => x?.SearchRoomPrice)?.ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(SearchService),
                    MethodName = nameof(UpdateMatchingRoomOffer),
                    Params = $"RePricerId: {rePricerId}, ReservationId: {reservationId}"
                };
                mainRoomInfoList = new List<SearchRoom_Package>();
                matchedRoomInfoList = new List<SearchRoom_Package>();
                selectedRoomTokens = new List<string>();
                _log.Error(irixErrorEntity, ex);
            }
            return isRoomFound;
        }

        public List<OfferInfo> GetOfferListFromSearchResponse(SearchResponseFromAPI searchResponse,
            List<PrebookCriteria> prebookCriteria,
            int rePricerId,
            int reservationId
        )
        {
            try
            {
                var offerInfoList = searchResponse?.hotels
                                                                                ?.SelectMany(hotel => hotel.offers)
                                                                                ?.Where(offer =>
                                                                                    prebookCriteria.All(prebookcriteria =>
                                                                                        offer?.rooms?.Any(room =>
                                                                                            room != null &&  // Add a null check for room
                                                                                            NormalizeString(room.name) == NormalizeString(prebookcriteria?.RoomName)
                                                                                            && !string.IsNullOrWhiteSpace(NormalizeString(room.name))
                                                                                            && !string.IsNullOrWhiteSpace(NormalizeString(prebookcriteria?.RoomName))
                                                                                            && (
                                                                                                   NormalizeString(room?.board)
                                                                                                   ==
                                                                                                   NormalizeString(prebookcriteria?.RoomBoard)
                                                                                                   && !string.IsNullOrWhiteSpace(NormalizeString(room?.board))
                                                                                                   && !string.IsNullOrWhiteSpace(NormalizeString(prebookcriteria?.RoomBoard))
                                                                                                )
                                                                                            && (room?.price?.components?.supplier?.value ?? null) != null
                                                                                            ?
                                                                                                    (
                                                                                                        Convert.ToDecimal(room?.price?.components?.supplier?.value) >= UtilCommonConstants.ProfitMinimumGlobalCheck ?
                                                                                                        true : false
                                                                                                    )
                                                                                            : true

                                                                                    ) == true)
                                                                                )
                                                                                ?.ToList();

                return offerInfoList;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(SearchService),
                    MethodName = nameof(GetOfferListFromSearchResponse),
                    Params = $"RePricerId: {rePricerId}, ReservationId: {reservationId}"
                };

                _log.Error(irixErrorEntity, ex);
            }
            return new List<OfferInfo>();
        }

        private string NormalizeString(string input)
        {
            return (input?.ToLower()?.Trim() ?? string.Empty)
                ?.Replace(Environment.NewLine, " ")
                ?.Replace("\n", " ") ?? string.Empty;
        }

        private PreBookResponseResult UpdateErrorAndReason(int repricerId, int reservationId, PreBookResponseResult preBookResponseResult, OptimizationStatusEnum optimizationStatusEnum, ReasonCode reasonCode)
        {
            //lock (_lock_UpdateErrorAndReason)
            //{
            try
            {
                if (preBookResponseResult == null)
                {
                    preBookResponseResult = new PreBookResponseResult
                    {
                        Status = new Dictionary<int, string>
                        {
                            { reservationId, optimizationStatusEnum.ToString() }
                        },
                        IsOptimizable = false,
                        IsOptimized = false,
                        IsPrebookSucess = false,
                        IsSearchSucess = false,
                        RepricerId = repricerId,
                    };
                }

                preBookResponseResult.OptimizableStatus = new OptimizationOptimizationBooking
                {
                    Status = optimizationStatusEnum.ToString(),
                };

                preBookResponseResult.Status = new Dictionary<int, string>
                {
                    { reservationId, optimizationStatusEnum.ToString() }
                };

                var logEntry = new MultiSupplierlog
                {
                    RePricerId = repricerId,
                    ReservationId = reservationId,
                    ReasonCode = reasonCode
                };
                _masterService.MultiSupplierPreBooklog(logEntry);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateErrorAndReason),
                    Params = $"RePricerId: {reservationId}, ReservationId: {reservationId}"
                };

                _log.Error(irixErrorEntity, ex);
            }
            //}
            return preBookResponseResult;
        }
    }
}