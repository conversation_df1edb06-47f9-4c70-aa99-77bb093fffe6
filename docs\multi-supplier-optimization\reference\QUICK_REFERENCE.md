# Multi-Supplier Optimization - Quick Reference Guide
Once done with documents, please revalidate and then move all information material into a folder like `docs` and create sub-folder tree as per documents.

## 🚀 Quick Start

### Key Entry Point
```csharp
// Main method in SupplierSearchService.cs
Task<PreBookResults> _1_PrebookAndOptimize_MultiSupplier(
    int repricerId,
    int? reservationId = 0,
    bool isUpdateDB = true,
    bool isMultiSupplier = true,
    string supplierName = null,
    bool isOptimizeTriggeredManually = false,
    int totalItems = 0,
    int currentItem = 0
)
```

### Required Configuration
```json
{
  "IsMultiSupplierEnabled": true,
  "IsJobsEnable": true,
  "IsActive": true,
  "IsOptimizationAllowed": true,
  "OptimizationType": "Manual"
}
```

## 📊 Key Database Tables

### Core Tables
| Table | Purpose | Key Fields |
|-------|---------|------------|
| `ReservationMain` | Source reservations | `ReservationId`, `supplierName`, `ReservationStatus` |
| `MultiSupplierSearchRoom` | Search results | `SupplierName`, `IssueNetPrice`, `CancellationDate` |
| `ReservationTableSupplier` | Prebook records | `PrebookProviders`, `Profit`, `cPStatus` |
| `BookingActionsTaken` | Optimization history | `ActionId`, `GainAmountInOriginalCurrency` |

### Key Stored Procedures
| Procedure | Purpose | Parameters |
|-----------|---------|------------|
| `usp_get_PreBookCriteria` | Load eligible reservations | `@RepricerId`, `@isMultiSupplier` |
| `usp_Ins_Prebook_Supplier_V2` | Create prebook | `@PrebookProviders`, `@Profit` |
| `usp_get_MultiSupplierRoomDetailsByRepricerId` | Dashboard data | `@RepricerId`, `@PageSize` |

## 🔄 Process Flow (10 Phases)

1. **Initialization** (Lines 194-276) - Validate client config
2. **Data Loading** (Lines 245-359) - Load reservations and suppliers
3. **Supplier Filtering** (Lines 330-682) - Batch processing (5 suppliers)
4. **Giata Mapping** (Lines 744-772) - Room equivalency validation
5. **Offer Filtering** (Lines 798-842) - Match and validate offers
6. **Price Validation** (Lines 858-1009) - Threshold checking
7. **Prebook Creation** (Lines 1023-1070) - IRIX API calls
8. **Cancellation Check** (Lines 1074-1136) - Policy validation
9. **Dry Run** (Lines 1174-1288) - Final validation
10. **Optimization** (Lines 1290-1397) - Manual approval required

## ⚡ Performance Targets

| Metric | Target | Monitoring |
|--------|--------|------------|
| Data Loading | <30 seconds | Watch parallel queries |
| Search Results | <2 minutes | Monitor batch processing |
| Room Mapping | <1 minute | Track Giata API calls |
| Prebook Creation | <30 seconds | Monitor IRIX responses |
| Final Optimization | <1 minute | Track approval workflow |

## 🚨 Common Error Codes

| Error | Cause | Resolution |
|-------|-------|------------|
| `GiataMappingNotFound` | Room mapping failed | Check room name variations |
| `NoMatchingOffers` | No alternatives found | Normal - log for analysis |
| `PriceThresholdCheckFailed` | Savings too low | Adjust client thresholds |
| `OptimizationNotAllowed` | Client restrictions | Check configuration |
| `SupplierNotAllowed` | Supplier not permitted | Update allowed providers |

## 📈 Key Business Rules

### Revenue Model
- **Platform Commission**: 50% of savings
- **Customer Benefit**: 50% of savings
- **Minimum Thresholds**: Configurable per client

### Approval Process
- **Multi-supplier**: Always requires manual approval
- **Same-supplier**: Can be automatic
- **Email Alerts**: Sent to operations team
- **Dry Run**: Mandatory before optimization

### Quality Assurance
- **Room Equivalency**: Via Giata mapping (>90% success rate)
- **Cancellation Policies**: Must be equal or better
- **Price Validation**: Multiple threshold checks
- **Manual Oversight**: Human approval for cross-supplier

## 🔧 Configuration Quick Reference

### Client Settings
```sql
-- Check client configuration
SELECT 
    IsMultiSupplierEnabled,
    IsJobsEnable,
    IsActive,
    IsOptimizationAllowed,
    OptimizationType
FROM RepricerClientDetail 
WHERE RepricerId = @RepricerId;
```

### Supplier Settings
```sql
-- Get allowed providers
SELECT ProviderName 
FROM AllowedProviders 
WHERE RepricerId = @RepricerId 
    AND IsActive = 1;
```

### Threshold Settings
```sql
-- Check price thresholds
SELECT 
    PriceDifferenceValue,
    PriceDifferencePercentage,
    IsUsePercentage
FROM ExtraClientConfig 
WHERE RepricerId = @RepricerId;
```

## 📊 Monitoring Queries

### Success Rate
```sql
SELECT 
    COUNT(CASE WHEN ActionId = 1 THEN 1 END) * 100.0 / COUNT(*) AS SuccessRate
FROM BookingActionsTaken 
WHERE RepricerId = @RepricerId 
    AND createdOn >= DATEADD(day, -30, GETUTCDATE());
```

### Revenue Tracking
```sql
SELECT 
    SUM(GainAmountInOriginalCurrency * 0.5) AS PlatformRevenue,
    COUNT(*) AS OptimizationsCount,
    AVG(GainAmountInOriginalCurrency) AS AvgSavings
FROM BookingActionsTaken 
WHERE ActionId = 1 
    AND RepricerId = @RepricerId
    AND createdOn >= DATEADD(month, -1, GETUTCDATE());
```

### Error Analysis
```sql
SELECT 
    Status,
    COUNT(*) AS ErrorCount,
    COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS ErrorPercentage
FROM PreBookResponseResult 
WHERE RepricerId = @RepricerId 
    AND IsPrebookSucess = 0
    AND CreatedDate >= DATEADD(day, -7, GETUTCDATE())
GROUP BY Status
ORDER BY ErrorCount DESC;
```

## 🛠️ Troubleshooting Steps

### Performance Issues
1. Check parallel processing settings (default: 50 concurrent)
2. Monitor API response times (target: <30 seconds)
3. Analyze database query performance
4. Review memory usage and garbage collection

### Optimization Failures
1. Verify client configuration (IsMultiSupplierEnabled = true)
2. Check supplier availability and contracts
3. Validate Giata mapping coverage (>90% target)
4. Review price thresholds and currency settings

### Data Quality Issues
1. Validate room name consistency
2. Check cancellation policy parsing
3. Review occupancy matching logic
4. Verify currency conversion factors

## 📞 Escalation Contacts

### Immediate Issues (Production)
- **Operations Team**: For manual approvals and customer issues
- **On-call Developer**: For system failures and critical bugs

### Business Questions
- **Product Team**: For process changes and business rules
- **Finance Team**: For revenue calculations and commission disputes

### Technical Questions
- **Development Team**: For code issues and enhancements
- **Database Team**: For performance and schema questions

---

*Quick Reference Guide - Last Updated: December 2024*
