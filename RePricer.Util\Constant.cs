﻿namespace Repricer.Util
{
    public class Constant
    {
        public const string JobConstant = "Job is AlreadyRunning";
        public const string JobConfirmation = "Successful";
        public const string JobCaching = "ClearAllCaches is running, please wait.";
        public const string JobError = "MemoryCache is disposed. Handle accordingly.";

        public const string AccountLogin = "/Account/Login";

        public const string username = "username";
        public const string Password = "Password";
        public const string token = "token";
        public const string CredentialError = "No valid credentials available.";

        public const string SaveAllReservations = "SaveAllReservation_{0}";
        public const string SaveAllReservationJobs = "{0}_SaveAllReservation";

        public const string PreBook = "PreBook_{0}";
        public const string PreBookJobs = "{0}_PreBook";

        public const string CurrencyExchange = "CurrencyExchange_{0}";
        public const string CurrencyExchangeJobs = "{0}_CurrencyExchange";

        public const string ConfigureJob = "ConfigureJob";
        public const string RefreshJobSchedules = "RefreshJobSchedules";
        public const string ClearAllCache = "ClearAllCache";
        public const string CacheRefreshService = "CacheRefreshService";
        public const string CacheRefreshDb = "CacheRefreshDb";

        public const string OptimizeService = "OptimizeService_{0}";
        public const string OptimizeServiceJobs = "{0}_OptimizeService";

        //public const string SendEmailJob = "{0}_SendEmail";
        //public const string PreBookDifferentSupplierJob = "{0}_PreBookDifferentSupplier";

        public const string Bearer = "Bearer";
        public const string authorizationError = "invalid_grant";
        public const string B2CUserName = "Admin";
        public const string B2CPassword = "Admin@123";
        public const string b2CAuthentication = "B2CAuthentication";

        public const string RePricer_APIEndpoint = "RePricer_APIEndpoint";
        public const string ApplicationJson = "application/json";
        public const string Authorization = "Authorization";
        public const string superadminusername = "SuperAdmin";
        public const string superadminpassword = "g}zF@5luOG1-";

        public const string PreBooks = "PreBook";

        public const string GetRepricerReport = "api/Admin/GetRepricerReport";
        public const string GetRepricerReportSummary = "api/Admin/GetRepricerReportSummary";
        public const string GetDateWiseReservationCount = "api/Admin/GetDateWiseReservationCount";

        public const string deletecacheurl = "api/Irix/MemoryCacheByRepricerId";
        public const string baseAddress = "https://rpnapidcs.azurewebsites.net/";

        // Threading and Parallel Processing Settings
        public const int MainJobSemaphoreCount = 2;

        public const decimal ProfitMinimumGlobalCheck = 3.0M;

        private static readonly int EnvironmentProcessorCount = Environment.ProcessorCount;

        private static DateTime lastPrintTime = DateTime.MinValue;

        public static int GetCountAsPerProcessorPercent(double percent)
        {
            try
            {
                int count = (int)(EnvironmentProcessorCount * (percent / 100));
                var status = GetThreadPoolStatus();

                if (status.availableThreads < count)
                {
                    count = status.availableThreads - 1;
                }

                var result = Math.Clamp(count, 1, 3);

                if (status.inUseThreads >= 100 && (DateTime.Now - lastPrintTime).TotalMinutes >= 5)
                {
                    lastPrintTime = DateTime.Now;
                    Console.WriteLine("---------------------------------------");
                    Console.WriteLine($"MIN Threads: {status.minThreads}");
                    Console.WriteLine($"MAX Threads: {status.maxThreads}");
                    Console.WriteLine($"FREE Threads: {status.availableThreads}");
                    Console.WriteLine($"USED Threads: {status.inUseThreads}");
                    Console.WriteLine($"REQUESTED Threads in code: {count}");
                    Console.WriteLine($"ALLOTTED Threads in code: {result}");
                    Console.WriteLine("---------------------------------------");
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error in GetCountAsPerProcessorPercent: " + ex.Message);
                return 1;
            }
        }

        // Returns a ParallelOptions instance based on the specified processor usage percentage
        public static ParallelOptions GetParallelOptions(double percent)
        {
            var count = GetCountAsPerProcessorPercent(percent);

            return new ParallelOptions { MaxDegreeOfParallelism = count };
        }

        private static (int minThreads, int maxThreads, int availableThreads, int inUseThreads) GetThreadPoolStatus()
        {
            int minWorkerThreads, maxWorkerThreads, availableWorkerThreads, inUseWorkerThreads;
            int minIOThreads, maxIOThreads, availableIOThreads, inUseIOThreads;

            // Get the minimum, maximum, and available threads for worker threads and IO threads.
            ThreadPool.GetMinThreads(out minWorkerThreads, out minIOThreads);
            ThreadPool.GetMaxThreads(out maxWorkerThreads, out maxIOThreads);
            ThreadPool.GetAvailableThreads(out availableWorkerThreads, out availableIOThreads);

            // Calculate the number of threads in use by subtracting available from max
            inUseWorkerThreads = maxWorkerThreads - availableWorkerThreads;
            inUseIOThreads = maxIOThreads - availableIOThreads;

            // Return the status as a tuple
            return (minWorkerThreads, maxWorkerThreads, availableWorkerThreads + availableIOThreads, inUseWorkerThreads + inUseIOThreads);
        }
    }
}