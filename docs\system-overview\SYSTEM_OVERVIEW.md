# Hotel Reservation Re-Optimization Platform
## Complete Technical Reference Documentation

## ⚠️ CRITICAL CODE ANALYSIS PROTOCOL

**🚨 NEVER ASSUME ANYTHING - ALWAYS READ ALL CODE AND PROCEDURES LINE BY LINE**

### **🔍 MANDATORY LINE-BY-LINE ANALYSIS PROTOCOL**

**B<PERSON>OR<PERSON> making ANY changes or references to code:**

1. **READ EVERY SINGLE LINE** of target methods, stored procedures, and related code
2. **VERIFY EVERY COLUMN NAME** against actual table schemas before using them
3. **CHECK EVERY STORED PROCEDURE NAME** in Constants files (never assume names)
4. **VALIDATE EVERY TABLE REFERENCE** against actual database definitions
5. **TRACE COMPLETE DATA FLOW** through actual code execution paths
6. **FOLLOW ALL CONDITIONAL LOGIC** (if/else branches, parameter conditions)
7. **IDENTIFY ALL DATABASE CALLS** and their exact parameters
8. **CROSS-REFERENCE ALL COLUMN MAPPINGS** between source and target
9. **VERIFY CASE SENSITIVITY** for all column and table names
10. **NEVER MAKE ASSUMPTIONS** about what code "probably" does

### **🚨 CRITICAL MISTAKES TO AVOID**

**Column Name Assumptions:**
- ❌ NEVER assume column names without checking actual table schemas
- ❌ NEVER use `PrebookProviders` when the actual column is `PrebookSupplier`
- ❌ NEVER mix case (`PrebookChildAges` vs `Prebookchildages`) without verification
- ✅ ALWAYS verify column names in actual table definitions

**Stored Procedure Assumptions:**
- ❌ NEVER assume procedure names without checking Constants files
- ❌ NEVER assume `usp_Ins_Prebooklog_V1` populates `ReservationTable` (it populates `ReservationTablelog`)
- ✅ ALWAYS read the actual stored procedure to see what table it operates on

**Code Reference Assumptions:**
- ❌ NEVER copy-paste code without adapting ALL column references
- ❌ NEVER assume similar procedures have identical column names
- ✅ ALWAYS validate each column reference against the target context

### **📋 VERIFICATION CHECKLIST**

Before writing any code that references database objects:

- [ ] Read the complete target method/procedure line by line
- [ ] Verify all column names against actual table schemas
- [ ] Check all stored procedure names in Constants files
- [ ] Cross-reference all column mappings between source and target
- [ ] Validate case sensitivity for all database object names
- [ ] Test all assumptions against actual code/schema definitions

**Example of Critical Miss:** Assuming `usp_Ins_Prebooklog_V1` populates `ReservationTable` when it actually populates `ReservationTablelog`. The actual gateway procedure is `usp_Ins_Prebook_V1` which populates `ReservationTable`.

**Recent Critical Mistakes Fixed:**
- Using `PrebookProviders` instead of `PrebookSupplier` in stored procedures
- Mixing case `PrebookChildAges` vs `Prebookchildages` causing SQL errors
- Assuming column names without verifying against actual table schemas

### 🎯 Executive Summary

**Business Model**: Automated hotel booking arbitrage platform that finds better deals for existing client reservations
- **Revenue Model**: 50% commission on savings achieved (client retains 50%)
- **Value Proposition**: Zero-risk savings delivery with guaranteed service quality maintenance
- **Target Market**: Hotels, travel agencies, and booking platforms with existing reservation portfolios
- **Core Technology**: Multi-supplier search engine with intelligent room mapping and cancellation policy optimization

**Key Metrics**:
- Platform Revenue: 50% of total savings generated
- Risk-Free Billing: Only charge after guest check-in and risk elimination
- Automated Processing: Handles thousands of reservations with minimal manual intervention
- Quality Assurance: Same or better room features and cancellation policies guaranteed

---

### 🏗️ System Architecture Overview

#### **3-Layer Data Processing Pipeline**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌──────────────────┐
│   Raw Data      │───▶│ Data             │───▶│ Business Logic  │───▶│ API Access      │
│   Sources       │    │ Consolidation    │    │ Engine          │    │ Layer           │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └──────────────────┘
│                      │                      │                      │
│ • ReservationMain    │ • usp_upd_          │ • usp_ins_upd_      │ • usp_get_         │
│ • BookingActionsTaken│   reservationreport │   tbl_vw_           │   ResevationReports│
│ • ClientConfiguration│ • Data validation   │   ResevationReports │   _V1              │
│ • ExchangeRateData   │ • Currency conversion│ • Business rules    │ • GetRepricerReport│
│ • CancellationPolicy │ • Profit calculation │ • Report generation │   API              │
└──────────────────────┴──────────────────────┴─────────────────────┴──────────────────┘
```

#### **Technology Stack**
- **Primary Database**: SQL Server (transactional data, reporting, business logic)
- **Secondary Database**: MongoDB (Giata room mapping, supplementary data)
- **Backend**: .NET Core Web API with dependency injection
- **Background Processing**: HangFire jobs for automated optimization
- **Caching**: Multi-level (Memory Cache 5min → Redis 6hr → Database)
- **External APIs**: IRIX hotel booking API, Giata room mapping service
- **Logging**: Structured logging with Azure Table Storage migration

---

### 📊 Complete Database Schema

#### **Core Data Tables**

##### **ReservationMain** - Source of Truth
```sql
CREATE TABLE ReservationMain (
    ReservationId INT PRIMARY KEY IDENTITY(1,1),
    RepricerId INT NOT NULL,                    -- Client identifier
    ReservationStatus VARCHAR(50),              -- 'OK', 'Cancelled'
    CheckIn DATE NOT NULL,
    CheckOut DATE NOT NULL,
    Destinations NVARCHAR(255),
    PaymentType VARCHAR(50),
    supplierName VARCHAR(100),                  -- Original booking supplier
    cpJSON NVARCHAR(MAX),                      -- Cancellation policy details
    CriteriaJson NVARCHAR(MAX),                -- Search criteria cache
    CreatedDate DATETIME DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME
);
```

##### **ReservationReportDetails** - Processed Optimization Data
```sql
CREATE TABLE ReservationReportDetails (
    Id INT PRIMARY KEY IDENTITY(1,1),
    ReservationId INT NOT NULL,
    RepricerId INT NOT NULL,
    ReservationPrice DECIMAL(18,4),            -- Original price
    PrebookPrice DECIMAL(18,4),                -- Optimized price
    Profit DECIMAL(18,4),                      -- Savings amount
    CurrencyFactorToEur DECIMAL(18,6),         -- EUR conversion rate
    cpstatus VARCHAR(50),                      -- 'loose', 'tight', etc.
    CPDaysGain INT,                           -- CP improvement days
    ActionTakenGain DECIMAL(18,4),            -- Realized profit
    newReservationID INT,                     -- New booking ID
    matchedreservationcancellationdate DATE,  -- Original CP deadline
    matchedprebookcancellationdate DATE,      -- New CP deadline
    CreateDate DATETIME DEFAULT GETUTCDATE()
);
```

##### **tbl_vw_ResevationReports** - Materialized Business Intelligence
```sql
CREATE TABLE tbl_vw_ResevationReports (
    Id INT PRIMARY KEY IDENTITY(1,1),
    RepricerId INT NOT NULL,
    ReservationId INT NOT NULL,
    ReportType VARCHAR(50),                    -- 'Prebook', 'CancellationEdgeCase', etc.
    ReservationPrice DECIMAL(18,4),
    PrebookPrice DECIMAL(18,4),
    Profit DECIMAL(18,4),
    ProfitPercentage DECIMAL(5,2),
    Currency VARCHAR(3),
    CheckIn DATE,
    CheckOut DATE,
    HotelName NVARCHAR(255),
    Destinations NVARCHAR(255),
    ReservationSupplier VARCHAR(100),
    PrebookSupplier VARCHAR(100),
    OptimizationStatus VARCHAR(100),
    CreateDate DATETIME DEFAULT GETUTCDATE(),

    INDEX IX_RepricerId_ReportType (RepricerId, ReportType),
    INDEX IX_CheckIn_Status (CheckIn, OptimizationStatus)
);
```

#### **Configuration Tables**

##### **RePricerDetail** - Core Client Configuration
```sql
CREATE TABLE RePricerDetail (
    RepricerUserID INT PRIMARY KEY IDENTITY(1,1),
    RepricerUserName NVARCHAR(255) NOT NULL,
    AdminURL NVARCHAR(500),                    -- IRIX API endpoint
    AdminUserID NVARCHAR(255),                 -- API credentials
    AdminPassword NVARCHAR(255),
    ResellerURL NVARCHAR(500),
    ResellerUserID NVARCHAR(255),
    ResellerPassword NVARCHAR(255),
    ApiScope NVARCHAR(255),
    ResellerApiScope NVARCHAR(255),
    IsActive BIT DEFAULT 1,                    -- Master on/off switch
    OptimizationType INT DEFAULT 1,            -- 1=Manual, 2=Automatic
    IsJobsEnable BIT DEFAULT 1,                -- Enable background jobs
    IsOptimization BIT DEFAULT 1,              -- Allow optimization
    IsMultiSupplierRoomSync BIT DEFAULT 0,     -- Cross-supplier optimization
    IsUseResellerCPHourDifference BIT DEFAULT 0, -- CP time buffer logic
    ResellerCPHourDifference INT DEFAULT 0,    -- Hours buffer for CP
    DelayBetweenOptimizationJob INT DEFAULT 300, -- Rate limiting (seconds)
    DelayBetweenDailyDownloadReservation INT DEFAULT 600,
    DelayBetweenRequestsSameSupplier INT DEFAULT 15,
    DelayBetweenRequestsMultiSupplier INT DEFAULT 20,
    CPTimeAllowedBufferPercentage DECIMAL(5,2) DEFAULT 0, -- CP time buffer %
    CPAmountAllowedBufferPercentage DECIMAL(5,2) DEFAULT 0, -- CP amount buffer %
    RestrictedHotelId NVARCHAR(MAX),           -- Allowed/blocked hotels
    RestrictedCityId NVARCHAR(MAX),            -- Allowed/blocked cities
    RestrictedCountryId NVARCHAR(MAX),         -- Allowed/blocked countries
    RestrictedSupplier NVARCHAR(MAX),          -- Allowed/blocked suppliers
    RestrictedReseller NVARCHAR(MAX),          -- Allowed/blocked resellers
    CreatedDate DATETIME DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME
);
```

##### **clientconfiguration_ExtraCriteria** - Business Rules
```sql
CREATE TABLE clientconfiguration_ExtraCriteria (
    id INT PRIMARY KEY IDENTITY(1,1),
    RePricerId INT NOT NULL,
    priceDifferenceValue DECIMAL(18,5),        -- Minimum profit amount
    pricedifferencecurrency VARCHAR(5),        -- Currency for thresholds
    priceDifferencePercentage DECIMAL(18,5),   -- Minimum profit percentage
    isUsePercentage BIT,                       -- Use percentage vs fixed amount
    traveldaysmaxsearchindays INT DEFAULT 60,  -- Maximum days ahead to search
    traveldaysminsearchindays INT DEFAULT 10,  -- Minimum days ahead to search
    MaxNumberOfTimesOptimization INT,          -- Max optimization attempts
    ClientConfig_DaysDifferenceInPreBookCreation INT, -- Prebook timing
    DaysLimitCancellationPolicyEdgeCase INT DEFAULT 5, -- CP edge case buffer
    IsUseDaysLimitCancellationPolicyEdgeCase BIT DEFAULT 1,
    IsCreatePrebookFoPriceEdgeCase BIT DEFAULT 0,
    IsSearchSyncDataSave BIT DEFAULT 0,
    IsDifferentSupplierSearchSyncDataSave BIT DEFAULT 0,
    OptimizationType INT DEFAULT 1,
    -- NEW COLUMNS FOR ENHANCED FUNCTIONALITY
    PriceDifferenceValueAllowedBufferPercentage DECIMAL(18,6) DEFAULT 0,
    IsCheckCrossSupperBeforeOptimization BIT DEFAULT 0,
    CreatedDate DATETIME DEFAULT GETUTCDATE()
);
```

##### **IrixConfiguration** - API-Level Controls
```sql
CREATE TABLE IrixConfiguration (
    ConfigurationId INT PRIMARY KEY IDENTITY(1,1),
    RepricerId INT NOT NULL,
    AllowedProvidersForOptimization VARCHAR(MAX), -- Supplier whitelist
    MinimumSupplierReservationPrice DECIMAL(18,4), -- Minimum reservation value
    MinimumSupplierReservationPriceCurrency VARCHAR(3),
    AllowNonRefundableReservations BIT,           -- Allow non-refundable
    DaysBeforeCancellationPolicy INT,             -- CP deadline buffer
    RejectPayNowReservations BIT,                 -- Block pay-now bookings
    RejectOptimizedReservations BIT,              -- Block already optimized
    MinimumOfferGainValue DECIMAL(18,4),          -- Minimum gain threshold
    MinimumOfferGainCurrency VARCHAR(3),
    OfferCancellationDeadlineRestriction NVARCHAR(MAX), -- CP restrictions
    ClientLicense BIT,
    CreateDate DATETIME DEFAULT GETUTCDATE()
);
```

##### **BookingActionsTaken** - Optimization History
```sql
CREATE TABLE BookingActionsTaken (
    Id INT PRIMARY KEY IDENTITY(1,1),
    RepricerId INT NOT NULL,
    ReservationId INT NOT NULL,
    ActionId INT NOT NULL,                     -- 1=Optimized, 2=Attempted, etc.
    ActionDate DATETIME DEFAULT GETUTCDATE(),
    NewBookingId INT,                         -- New reservation ID
    ProfitAmount DECIMAL(18,4),               -- Actual profit realized
    Currency VARCHAR(3),
    OptimizationMethod VARCHAR(50),           -- 'SameSupplier', 'MultiSupplier'
    SupplierName VARCHAR(100),                -- New supplier used
    Status VARCHAR(100),                      -- Success/failure status
    ErrorMessage NVARCHAR(MAX),               -- Error details if failed
    CreateDate DATETIME DEFAULT GETUTCDATE()
);
```

---

### 🔄 Dual Optimization Workflows

#### **Flow 1: Same Supplier Optimization**
**File**: `Irix.Service/SearchService.cs`
**Method**: `_1_PrebookAndOptimize_SameSupplier_Automatic`

**Strategy**: Find better rates with the same supplier that provided the original booking

```csharp
// Key validation: Must be same supplier
if (reservationSupplierName?.ToLower().Trim() == prebookSupplierName?.ToLower().Trim())
{
    preBookResponseResult.IsSearchSucess = true;
    // Proceed with optimization
}
```

**Process Flow**:
1. **Eligibility Check**: Validate client configuration and optimization status
2. **Search Execution**: Call IRIX API with original supplier only
3. **Room Matching**: Direct room/board matching (exact match required)
4. **Price Validation**: Check profit thresholds (percentage or fixed amount)
5. **Prebook Creation**: Create prebook with IRIX API
6. **CP Validation**: Ensure cancellation policy is same or better
7. **Execution Decision**: Automatic if configured, manual if required

**Characteristics**:
- ✅ Lower complexity and risk
- ✅ Can execute automatically
- ✅ Faster processing (single supplier)
- ✅ Direct room token matching
- ✅ Simpler validation logic

#### **Flow 2: Multi-Supplier Optimization**
**File**: `Irix.Service/SupplierSearchService.cs`
**Method**: `_1_PrebookAndOptimize_MultiSupplier`

**Strategy**: Find better rates across different suppliers using Giata room mapping

```csharp
// Cross-supplier room mapping with Giata
giatadatafororiginalreservation = _giataService.GiataApiCall(repricerId,
    reservation.ReservationId.ToString(), hotelName, prebookcriteriaroom.RoomName,
    Destinations, reservationsupplier).GetAwaiter().GetResult();
```

**Process Flow**:
1. **Enhanced Eligibility**: Check IsMultiSupplierEnabled + supplier whitelist
2. **Batch Processing**: Search multiple suppliers in batches of 5
3. **Giata Mapping**: Map original room to equivalent rooms across suppliers
4. **Room Compatibility**: Validate board groups and room features
5. **Cross-Supplier Matching**: Find equivalent rooms with different suppliers
6. **Enhanced Validation**: Additional Giata mapping and compatibility checks
7. **Dry Run Required**: Must perform dry run before execution
8. **Manual Approval**: Requires manual trigger for execution

**Characteristics**:
- ✅ Maximum savings potential
- ✅ Complex Giata room mapping
- ✅ Manual approval required
- ✅ Batch supplier processing
- ✅ Enhanced validation logic
- ✅ Dry run optimization mandatory

---

### ⚙️ Configuration System Decision Points

#### **5-Gate Decision Process**

##### **Gate 1: Initial Eligibility**
```sql
-- Configuration checks
WHERE IsActive = 1
  AND IsOptimization = 1
  AND IsJobsEnable = 1
  AND CheckIn BETWEEN DATEADD(DAY, traveldaysminsearchindays, GETUTCDATE())
                  AND DATEADD(DAY, traveldaysmaxsearchindays, GETUTCDATE())
```

##### **Gate 2: Profit Validation**
```sql
-- Percentage-based threshold
WHEN ISNULL(cce.IsUsePercentage, 0) = 1
     AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) >= cce.priceDifferencePercentage

-- Fixed amount threshold
WHEN ISNULL(cce.IsUsePercentage, 0) = 0
     AND rrd.Profit * ISNULL(ER.Factor, 1) >= cce.priceDifferenceValue
```

##### **Gate 3: Cancellation Policy Rules**
```sql
-- CP status validation
WHERE rrd.cpstatus = 'loose'
   OR (rrd.cpstatus = 'tightWithBuffer' AND CPTimeAllowedBufferPercentage > 0)

-- CP deadline validation
AND CAST(rrd.matchedreservationcancellationdate AS DATE) >
    CAST(DATEADD(DAY, cce.traveldaysminsearchindays, GETUTCDATE()) AS DATE)
```

##### **Gate 4: Geographic & Supplier Restrictions**
```sql
-- Supplier validation
WHERE supplierName IN (SELECT value FROM STRING_SPLIT(AllowedProvidersForOptimization, ','))
  AND (RestrictedSupplier IS NULL OR supplierName NOT IN (SELECT value FROM STRING_SPLIT(RestrictedSupplier, ',')))

-- Geographic validation
AND (RestrictedHotelId IS NULL OR HotelId NOT IN (SELECT value FROM STRING_SPLIT(RestrictedHotelId, ',')))
AND (RestrictedCityId IS NULL OR CityId NOT IN (SELECT value FROM STRING_SPLIT(RestrictedCityId, ',')))
```

##### **Gate 5: Execution Controls**
```sql
-- Optimization type validation
WHERE (OptimizationType = 2) -- Automatic
   OR (OptimizationType = 1 AND @isManuallyTriggered = 1) -- Manual approval

-- Attempt limit validation
AND ISNULL(OptimizationAttempts, 0) < ISNULL(MaxNumberOfTimesOptimization, 3)
```

---

### 💰 Financial Model & Invoicing

#### **50% Commission Structure**
```sql
-- Invoice calculation (only after all risks passed)
SELECT RepricerId,
       YEAR(InvoiceDate) AS [Year],
       MONTH(InvoiceDate) AS [Month],
       SUM(ReservationPrice) AS TotalOriginalPrice,
       SUM(PrebookPrice) AS TotalOptimizedPrice,
       SUM(Profit) AS TotalSavingsGenerated,
       SUM(Profit) / 2.0 AS InvoiceableAmount,  -- 50% commission
       COUNT(DISTINCT ReservationId) AS OptimizationCount
FROM InvoiceData
WHERE ActionTakenGain > 0                    -- Optimization completed
  AND newReservationID > 0                   -- New booking confirmed
  AND reservationcheckin < @currentDate      -- Guest checked in
  AND matchedreservationcancellationdate < @currentDate -- Cancellation risk passed
GROUP BY RepricerId, YEAR(InvoiceDate), MONTH(InvoiceDate)
```

#### **Risk-Free Billing Criteria**
- ✅ **Guest Check-in Completed**: Eliminates no-show risk
- ✅ **Cancellation Deadlines Passed**: Eliminates cancellation risk
- ✅ **New Booking Confirmed**: Eliminates execution risk
- ✅ **Profit Realized**: Only bill actual savings achieved

#### **Revenue Example**
```
Original Reservation: €450.00
Optimized Price: €380.00
Total Savings: €70.00

Platform Revenue (50%): €35.00
Client Net Savings (50%): €35.00
```

---

### 🔌 API Endpoints & Integration

#### **Core API Endpoints**

##### **GetRepricerReport** - Main Dashboard Data
```http
POST /api/Repricer/GetRepricerReport
Content-Type: application/json

{
  "RepricerId": 12,
  "ReportType": "Prebook",
  "PageNumber": 1,
  "PageSize": 50,
  "SortBy": "CheckIn",
  "SortDirection": "DESC"
}
```

**Response Structure**:
```json
{
  "requestBody": { "RepricerId": 12, "ReportType": "Prebook" },
  "pagesSummary": { "totalPages": 15, "currentPage": 1, "totalRecords": 742 },
  "action": [
    { "actionId": 1, "actionName": "Optimize", "isEnabled": true },
    { "actionId": 2, "actionName": "Preview", "isEnabled": true }
  ],
  "overallSummary": {
    "summarizedView": [
      { "reportType": "Prebook", "count": 45, "totalProfit": 2250.50 },
      { "reportType": "CancellationEdgeCase", "count": 12, "totalProfit": 480.25 }
    ]
  },
  "data": [
    {
      "reservation": {
        "reservationId": 12345,
        "checkIn": "2024-03-15",
        "checkOut": "2024-03-18",
        "hotelName": "Grand Hotel Vienna",
        "destinations": "Vienna, Austria",
        "reservationPrice": 450.00,
        "currency": "EUR",
        "supplierName": "Booking.com"
      },
      "prebook": {
        "prebookPrice": 380.00,
        "profit": 70.00,
        "profitPercentage": 15.56,
        "supplierName": "Booking.com",
        "cancellationPolicy": "Free cancellation until 2024-03-13"
      },
      "clientConfigurationUsed": {
        "priceDifferencePercentage": 10.0,
        "isUsePercentage": true,
        "traveldaysminsearchindays": 14
      }
    }
  ]
}
```

##### **Optimization Execution**
```http
POST /api/Optimization/ExecuteOptimization
Content-Type: application/json

{
  "RepricerId": 12,
  "ReservationId": 12345,
  "OptimizationType": "SameSupplier",
  "IsManuallyTriggered": true
}
```

##### **Configuration Management**
```http
POST /api/Configuration/UpdateClientConfiguration
Content-Type: application/json

{
  "RepricerId": 12,
  "priceDifferencePercentage": 15.0,
  "isUsePercentage": true,
  "traveldaysmaxsearchindays": 90,
  "traveldaysminsearchindays": 14,
  "OptimizationType": 2,
  "IsMultiSupplierEnabled": true
}
```

#### **External API Integrations**

##### **IRIX Hotel Booking API**
- **Search**: Find available rooms and rates
- **Prebook**: Create temporary booking holds
- **Optimize**: Execute final booking optimization
- **Status**: Check optimization eligibility

##### **Giata Room Mapping Service**
- **Room Mapping**: Cross-supplier room equivalency
- **Board Mapping**: Meal plan compatibility
- **Hotel Mapping**: Property identification across suppliers

---

### 📊 Key Workflows & Data Flows

#### **Daily Processing Workflow**
```
1. RESERVATION DOWNLOAD (Scheduled Jobs)
   ├── Download new/updated reservations from client systems
   ├── Insert/update ReservationMain table
   ├── Validate data integrity and completeness
   └── Trigger optimization eligibility assessment

2. DATA CONSOLIDATION (usp_upd_reservationreport)
   ├── Join reservation data with room details
   ├── Apply currency conversions to EUR
   ├── Calculate base profit potential
   ├── Update ReservationReportDetails table
   └── Log processing metrics and errors

3. BUSINESS LOGIC APPLICATION (usp_ins_upd_tbl_vw_ResevationReports)
   ├── Apply client-specific business rules
   ├── Classify reservations by report type
   ├── Filter by configuration restrictions
   ├── Calculate final optimization scores
   └── Populate tbl_vw_ResevationReports for API access

4. OPTIMIZATION EXECUTION (Background Jobs)
   ├── Process eligible reservations in batches
   ├── Execute same supplier optimizations (automatic)
   ├── Queue multi-supplier optimizations (manual approval)
   ├── Update BookingActionsTaken with results
   └── Send notifications and reports

5. INVOICING GENERATION (Monthly)
   ├── Identify completed optimizations past risk period
   ├── Calculate 50% commission on realized savings
   ├── Generate detailed invoice reports
   ├── Update billing status and send invoices
   └── Archive processed data for audit trail
```

#### **Real-Time Optimization Flow**
```
USER REQUEST → API VALIDATION → CONFIGURATION CHECK → OPTIMIZATION STRATEGY
     ↓              ↓                    ↓                      ↓
SEARCH EXECUTION → ROOM MATCHING → PRICE VALIDATION → CP VALIDATION
     ↓              ↓                    ↓                      ↓
PREBOOK CREATION → FINAL VALIDATION → EXECUTION DECISION → RESULT TRACKING
     ↓              ↓                    ↓                      ↓
STATUS UPDATE → NOTIFICATION → DASHBOARD UPDATE → AUDIT LOGGING
```

---

### 🔍 Performance & Monitoring

#### **Key Performance Indicators**
- **Optimization Success Rate**: % of eligible reservations successfully optimized
- **Average Savings Per Optimization**: Mean profit generated per successful optimization
- **Processing Time**: End-to-end time from reservation to optimization completion
- **API Response Times**: Performance of external API integrations
- **Error Rates**: Failed optimizations and system errors

#### **Monitoring & Alerting**
- **Real-time dashboards** for optimization metrics
- **Automated alerts** for system failures and performance degradation
- **Detailed logging** with structured error tracking
- **Performance profiling** for optimization bottlenecks
- **Business intelligence** reporting for client insights

---

### 🚀 Deployment & Scaling

#### **Infrastructure Requirements**
- **Database**: SQL Server with read replicas for reporting
- **Application**: .NET Core with horizontal scaling capability
- **Caching**: Redis cluster for distributed caching
- **Background Jobs**: HangFire with multiple worker instances
- **Monitoring**: Application Insights and custom dashboards

#### **Scaling Considerations**
- **Database partitioning** by RepricerId for large clients
- **API rate limiting** to prevent external service overload
- **Batch processing optimization** for high-volume periods
- **Caching strategies** for frequently accessed configuration data
- **Load balancing** across multiple application instances

---

### 📚 Key Stored Procedures

#### **Data Processing Pipeline**
- **`usp_upd_reservationreport`**: Data consolidation and currency conversion
- **`usp_ins_upd_tbl_vw_ResevationReports`**: Business logic application and report generation
- **`usp_get_ResevationReports_V1`**: API data access with caching and filtering

#### **Configuration Management**
- **`usp_ins_RepricerClientConfiguration`**: Client setup and configuration updates
- **`usp_GetRePricerDataByUserID`**: Load complete client configuration
- **`usp_ins_configuration`**: IRIX API configuration management

#### **Invoicing & Billing**
- **`usp_Get_Invoice`**: Generate monthly invoices with 50% commission calculation
- **`usp_upd_reservationreport`**: Track completed optimizations for billing

---

### 🔧 Development Guidelines

#### **Code Organization**
- **`Irix.Service/SearchService.cs`**: Same supplier optimization logic
- **`Irix.Service/SupplierSearchService.cs`**: Multi-supplier optimization logic
- **`Irix.Service/SearchServiceHelper.cs`**: Shared optimization utilities
- **`Irix.Entities/`**: Data models and configuration classes
- **`Database/`**: SQL Server stored procedures and schema definitions

#### **Testing Recommendations**
- **Use RepricerId = 99** for testing (never use RepricerId = 12 - production)
- **Test both optimization flows** with different configuration scenarios
- **Validate profit calculations** with various currency combinations
- **Test cancellation policy edge cases** with buffer configurations
- **Verify invoicing calculations** with completed optimization scenarios

---

### 🔄 COMPLETE DATABASE OBJECT INVENTORY: Both Optimization Methods

#### **📊 COMPREHENSIVE TABLE ANALYSIS**

**CRITICAL NOTE**: This inventory covers ALL database objects used in both optimization flows, including tables, stored procedures, views, and their exact relationships.

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- CORE DATA TABLES (Source of Truth)
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationMain                    -- Primary reservation data from client systems
├── Used by: usp_get_CreateSearch, usp_get_PreBookCriteria, usp_upd_reservationreport
├── Columns: ReservationId, RepricerId, ReservationStatus, CheckIn, CheckOut, supplierName, cpJSON
└── Purpose: Source of truth for all client reservations

ReservationRoom                    -- Room configuration details
├── Used by: usp_get_RoomInfo, usp_get_PreBookCriteria
├── Columns: ReservationId, RoomName, RoomBoard, PassengerCount, ChildAges, RoomInfo
└── Purpose: Room specifications for matching and validation

ReservationHotelInformation        -- Hotel property details
├── Used by: usp_get_PreBookCriteria, usp_upd_reservationreport
├── Columns: ReservationId, HotelId, HotelName, Destinations
└── Purpose: Property identification and location data

ReservationPrice                   -- Financial data for reservations
├── Used by: usp_get_PreBookCriteria, usp_upd_reservationreport
├── Columns: ReservationId, Currency, Total_Selling, Issue_Net
└── Purpose: Original pricing and currency information

Reservation_Supplier               -- Supplier relationship data
├── Used by: usp_get_CreateSearch, usp_upd_reservationreport
├── Columns: ReservationId, Supplier_System, SupplierName
└── Purpose: Original booking supplier identification

ReservationCancellationPolicy     -- Cancellation terms and conditions
├── Used by: usp_get_CancellationPolicy, usp_get_PreBookCriteria
├── Columns: ReservationId, CancellationDate, CancellationCharge, CancellationType
└── Purpose: Original booking cancellation policy details

-- ═══════════════════════════════════════════════════════════════════════════════
-- PROCESSING TABLES (Optimization Workflow)
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationTable                   -- QUALIFIED CANDIDATES FOR REBOOKING
├── Used by: usp_Ins_Prebooklog_V1, usp_get_prebookreservation
├── Columns: ReservationId, PreBookPrice, Profit, CPStatus, AvailabilityToken, PreBookCount
├── Purpose: Stores validated prebook attempts ready for optimization
└── CRITICAL: Only populated AFTER successful IRIX prebook + validation

BookingActionsTaken                -- FINAL OPTIMIZATION RESULTS
├── Used by: usp_upd_BookingActionsTaken, usp_upd_reservationreport
├── Columns: RepricerId, ReservationId, ActionId, NewBookingId, ProfitAmount, OptimizationMethod
├── Purpose: Record of all optimization attempts and results
└── CRITICAL: Source of truth for completed optimizations

-- ═══════════════════════════════════════════════════════════════════════════════
-- REPORTING AND BUSINESS INTELLIGENCE TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationReportDetails           -- Consolidated optimization data
├── Used by: usp_upd_reservationreport
├── Columns: ReservationId, ReservationPrice, PrebookPrice, Profit, CPStatus, ActionTakenGain
├── Purpose: Layer 1 - Data consolidation with currency conversion
└── CRITICAL: Bridge between raw data and business intelligence

tbl_vw_ResevationReports           -- Materialized business intelligence
├── Used by: usp_ins_upd_tbl_vw_ResevationReports, usp_get_ResevationReports_V1
├── Columns: RepricerId, ReservationId, ReportType, Profit, ProfitPercentage, OptimizationStatus
├── Purpose: Layer 2 - Business logic application and report classification
└── CRITICAL: Primary data source for GetRepricerReport API
```

#### **🎯 CRITICAL GATEWAY PROCEDURES (Missing from Original Data Flow)**

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- CRITICAL PROCEDURE #1: RESERVATIONTABLE POPULATION (GATEWAY TO OPTIMIZATION)
-- ═══════════════════════════════════════════════════════════════════════════════
usp_Ins_Prebooklog_V1 → THE CRITICAL GATEWAY PROCEDURE
├── ONLY CALLED AFTER: Successful IRIX prebook + ALL validations passed
├── POPULATES: ReservationTable (qualified candidates for optimization)
├── ALSO POPULATES: PreBook_CancellationPolicy, PreBook_PackageRooms, PreBook_ClientConfiguration, PreBookLog
├── CRITICAL ROLE: Acts as the gateway between search/validation and optimization execution
└── WITHOUT THIS: No reservations can proceed to optimization (ReservationTable remains empty)

-- ═══════════════════════════════════════════════════════════════════════════════
-- CRITICAL PROCEDURE #2: BOOKINGACTIONSTAKEN POPULATION (FINAL RESULTS)
-- ═══════════════════════════════════════════════════════════════════════════════
usp_upd_BookingActionsTaken → THE FINAL RESULTS PROCEDURE
├── ONLY CALLED AFTER: Successful IRIX optimization execution
├── POPULATES: BookingActionsTaken (final optimization results)
├── CRITICAL ROLE: Records the actual optimization outcome and new booking details
├── USED BY: Reporting pipeline (usp_upd_reservationreport) and invoicing (usp_Get_Invoice)
└── WITHOUT THIS: No optimization results recorded (no invoicing, no reporting)

-- ═══════════════════════════════════════════════════════════════════════════════
-- COMPLETE STEP-BY-STEP SEQUENCE WITH CRITICAL PROCEDURES
-- ═══════════════════════════════════════════════════════════════════════════════
STEP 1: Load Source Data
├── usp_get_CreateSearch → ReservationMain + related tables
├── usp_get_RoomInfo → ReservationRoom details
└── usp_get_PreBookCriteria → Complete criteria for IRIX API

STEP 2: IRIX API Search & Prebook
├── IRIX SearchForBooking() → External API call
└── IRIX PrebookResponse() → Availability token generation

STEP 3: Validation & Business Rules
├── Price threshold validation (IsPriceThreshold)
├── Cancellation policy comparison (CancellationCheck)
└── Business rule compliance checks

STEP 4: *** CRITICAL GATEWAY #1 *** (ACTUAL CODE ANALYSIS)
├── InsertPreBookReservation() → EXEC usp_Ins_Prebook_V1 → Populates ReservationTable
├── CONDITION: IF (isUpdateDB || isCurrentPrebookOptimized) from SearchService line 1085
├── ONLY IF: All validations passed + IRIX prebook successful + DB update needed
├── PARALLEL: InsertPreBookReservationLog() → usp_Ins_Prebooklog_V1 → ReservationTablelog
└── OUTPUT: Qualified candidates ready for optimization

STEP 5: Dry Run Optimization (Optional)
├── _2_DryRunOptimizationApiIRIX() → Feasibility test
└── usp_ins_dryrunoptimization → Store test results

STEP 6: Optimization Execution
├── _3_OptimizeBookingApiIRIX() → IRIX optimization call
└── usp_ins_optimizedbooking → Store execution details

STEP 7: *** CRITICAL GATEWAY #2 ***
├── usp_upd_BookingActionsTaken → Populates BookingActionsTaken
├── ONLY IF: IRIX optimization successful
└── OUTPUT: Final optimization results recorded

STEP 8: Reporting Pipeline (3-Layer)
├── usp_upd_reservationreport → Data consolidation
├── usp_ins_upd_tbl_vw_ResevationReports → Business logic
└── usp_get_ResevationReports_V1 → API access

CRITICAL FAILURE POINTS:
├── If Step 4 fails: ReservationTable empty → No optimization candidates
├── If Step 7 fails: BookingActionsTaken empty → No results recorded
└── If either fails: Complete optimization workflow breaks
```

#### **Tables & Procedures Used in _1_PrebookAndOptimize_MultiSupplier**

```sql
-- ADDITIONAL INPUT SOURCES (Multi-Supplier Specific)
1. GetAllowedProviders() → IrixConfiguration table
   └── AllowedProvidersForOptimization (supplier whitelist)

2. GetPreBookCriteriaDBAll() → Enhanced version with multi-supplier data
   ├── All same-supplier sources PLUS
   ├── MultiSupplierSearchRoom (cross-supplier room data)
   ├── MultiSupplierReservationRoom (room mappings)
   └── ReservationPrice (pricing data)

3. GiataApiCall() → MongoDB collections
   ├── GiataRoomMapping (room equivalency)
   ├── GiataBoardMapping (meal plan mapping)
   └── GiataHotelMapping (property mapping)

-- MULTI-SUPPLIER PROCESSING (WRITE OPERATIONS)
4. InsertMultiSupplierRoom() → usp_ins_multiSupplierRooms
   ├── INSERT INTO MultiSupplierSearchRoom (search results)
   └── INSERT INTO MultiSupplierReservationRoom (room mappings)

5. InsertRoomMapping() → usp_ins_roommapping
   ├── INSERT INTO MultiSupplierRoomMapping (Giata mappings)
   └── UPDATE existing mappings with new data

-- ENHANCED LOGGING (Multi-Supplier Specific)
6. InsertPreBookReservationlogSupplier() → usp_Ins_PrebookSupplierlog_V1
   ├── INSERT INTO PreBookSupplierLog (multi-supplier attempts)
   └── Enhanced tracking vs same-supplier logs
```

#### **🎯 Critical Understanding: ReservationTable Role**

```sql
-- IMPORTANT: ReservationTable is NOT a source table
-- It contains SELECTED CANDIDATES FOR REBOOKING after validation

SEQUENCE:
1. ReservationMain → Source reservations from client systems
2. IRIX API Search → Find potential better offers
3. IRIX API Prebook → Create temporary booking holds
4. Validation → Check profit thresholds, CP compatibility, business rules
5. ReservationTable ← INSERT qualified candidates for optimization
6. Dry Run → Test optimization feasibility
7. Optimization → Execute final rebooking
8. BookingActionsTaken ← Record final results

-- ReservationTable Purpose:
├── Stores prebook attempts that passed all validations
├── Contains AvailabilityTokens for optimization execution
├── Tracks PreBookCount (attempt numbers)
├── Holds calculated Profit and CPStatus
└── Serves as input for dry run and optimization phases

-- Key Fields in ReservationTable:
├── ReservationId (links to original ReservationMain)
├── PreBookPrice (validated price from IRIX prebook)
├── Profit (calculated savings amount)
├── CPStatus ("loose", "tight", "CancellationChargesApplicable")
├── AvailabilityToken (IRIX token for optimization)
├── PreBookCount (attempt sequence number)
└── CreateDate (when candidate was qualified)
```

---

### 🚨 CRITICAL DEVELOPMENT GUIDELINES

#### **⚠️ MANDATORY REQUIREMENTS FOR ANY CODE CHANGES**

```
🔴 STOP: This application implements a dual-architecture approach with business
logic distributed between database stored procedures and application code.
Both layers contain critical decision-making logic that must be understood
in detail before making any modifications.
```

#### **1. Code Analysis Protocol (REQUIRED BEFORE ANY CHANGES)**

```sql
-- STEP 1: Complete Method Analysis
├── Read EVERY line of code in the target method/procedure
├── Trace complete data flow: input tables → transformations → output tables
├── Identify ALL conditional logic branches and business rule implications
└── Map ALL database interactions (reads, writes, updates) and sequence dependencies

-- STEP 2: Database Procedure Understanding
├── Analyze complete stored procedure logic, not just the signature
├── Understand 3-layer architecture:
│   ├── Layer 1: usp_upd_reservationreport (data consolidation)
│   ├── Layer 2: usp_ins_upd_tbl_vw_ResevationReports (business logic)
│   └── Layer 3: usp_get_ResevationReports_V1 (API access)
├── Identify ALL table dependencies and join conditions
└── Understand currency conversion logic and business rule applications

-- STEP 3: Flow Integrity Verification
├── Maintain exact sequence: Search → Prebook → Validation → ReservationTable → Dry Run → Optimization
├── Preserve ALL validation checkpoints and their order
├── Ensure new code follows existing error handling patterns
└── Maintain consistency with parallel processing and locking mechanisms
```

#### **2. Risk Mitigation Requirements**

```
🔥 HIGH-RISK AREAS (Extreme Caution Required):
├── ReservationTable population logic (only after successful IRIX prebook + validation)
├── Same Supplier vs Multi-Supplier flow differences
├── 3-layer reporting pipeline triggers
├── Currency conversion and profit calculations
├── IRIX API integration points
└── Parallel processing and Redis locking mechanisms

⚡ TESTING REQUIREMENTS:
├── Test against COMPLETE optimization workflow, not isolated components
├── Verify Same Supplier AND Multi-Supplier flows
├── Validate reporting pipeline triggers correctly
├── Confirm data consistency across all tables
└── Test with actual IRIX API integration (not mocks)
```

#### **3. Code Placement Rules**

```csharp
// CRITICAL: Maintain exact operation sequence
// ❌ NEVER insert code that breaks this flow:

1. GetReservationsAsync() → Load source data
2. IRIX SearchForBooking() → Find offers
3. IRIX Prebookresponse() → Create holds
4. Validation Logic → Check all rules
5. InsertPreBookReservation() → Populate ReservationTable
6. _2_DryRunOptimizationApiIRIX() → Test feasibility
7. _3_OptimizeBookingApiIRIX() → Execute optimization
8. BookingActionsTaken → Record results
9. RefreshDbAndCachedReport() → Update reporting

// ✅ ALWAYS preserve validation checkpoints:
├── Price threshold validation
├── Cancellation policy compatibility
├── Supplier matching rules
├── Business rule compliance
└── IRIX API response validation
```

#### **4. Failure Consequences**

```
🚫 FAILURE TO FOLLOW THESE GUIDELINES WILL RESULT IN:
├── Broken optimization workflows
├── Incorrect profit calculations
├── Data inconsistencies between tables
├── Failed integrations with IRIX API
├── Corrupted reporting pipeline
├── Revenue loss due to failed optimizations
└── Client billing discrepancies
```

#### **5. Pre-Development Checklist**

```
✅ BEFORE WRITING ANY CODE, PROVIDE:
├── Detailed analysis of existing logic in target methods
├── Complete list of all affected components and tables
├── Explanation of how changes preserve workflow integrity
├── Risk assessment of potential breaking changes
├── Testing strategy for both Same Supplier and Multi-Supplier flows
└── Rollback plan if changes cause issues
```

#### **6. Architecture-Specific Warnings**

```sql
-- DATABASE LAYER WARNINGS
⚠️  usp_upd_reservationreport: Contains complex currency conversion logic
⚠️  usp_ins_upd_tbl_vw_ResevationReports: Business rule classification engine
⚠️  ReservationTable: Only populated after successful validation
⚠️  BookingActionsTaken: Final source of truth for optimizations

-- APPLICATION LAYER WARNINGS
⚠️  SearchService._1_PrebookAndOptimize_SameSupplier_Automatic: Core same-supplier logic
⚠️  SupplierSearchService._1_PrebookAndOptimize_MultiSupplier: Complex multi-supplier flow
⚠️  Parallel.ForEach processing: Redis locking and race condition management
⚠️  IRIX API calls: Rate limiting and error handling critical
```

---

### ✅ COMPREHENSIVE DATABASE COVERAGE VERIFICATION

#### **📊 Complete Object Inventory Summary**

```
TABLES COVERED: 35+ core tables including:
├── 6 Core Data Tables (ReservationMain, ReservationRoom, etc.)
├── 8 Processing Tables (ReservationTable, BookingActionsTaken, etc.)
├── 4 Multi-Supplier Tables (MultiSupplierSearchRoom, etc.)
├── 4 Optimization Tracking Tables (DryRunOptimization, etc.)
├── 2 Reporting Tables (ReservationReportDetails, tbl_vw_ResevationReports)
├── 3 Configuration Tables (RePricerDetail, clientconfiguration_ExtraCriteria, etc.)
├── 5 Search Sync Tables (SearchSync_Hotel, SupplierSearchSync_Hotel, etc.)
├── 3 Logging Tables (ReservationTableLog, PreBookLog, etc.)
└── 3 Master Data Tables (SupplierMaster, ResellerMaster, etc.)

STORED PROCEDURES COVERED: 25+ procedures including:
├── 8 Core Data Retrieval Procedures (usp_get_CreateSearch, etc.)
├── 6 Prebook/Optimization Procedures (usp_Ins_Prebooklog_V1, etc.)
├── 3 Multi-Supplier Procedures (usp_ins_multiSupplierRooms, etc.)
├── 3 Reporting Pipeline Procedures (3-layer architecture)
├── 5 Configuration Procedures (usp_GetRePricerDataByUserID, etc.)

VIEWS COVERED: 1 critical view:
└── vw_ResevationReports (foundation for materialized reporting)

MONGODB COLLECTIONS: 3 Giata collections:
├── GiataRoomMapping (room equivalency)
├── GiataBoardMapping (meal plan mapping)
└── GiataHotelMapping (property mapping)
```

#### **🎯 Data Flow Coverage Verification**

```
✅ SAME SUPPLIER FLOW (_1_PrebookAndOptimize_SameSupplier_Automatic):
├── ✅ All input tables and procedures identified
├── ✅ All processing operations mapped
├── ✅ All output tables and reporting pipeline covered
└── ✅ Complete sequence from source data to final reporting

✅ MULTI-SUPPLIER FLOW (_1_PrebookAndOptimize_MultiSupplier):
├── ✅ All additional multi-supplier tables identified
├── ✅ All Giata integration points covered
├── ✅ All enhanced logging mechanisms mapped
└── ✅ Complete cross-supplier workflow documented

✅ REPORTING PIPELINE (3-Layer Architecture):
├── ✅ Layer 1: usp_upd_reservationreport (data consolidation)
├── ✅ Layer 2: usp_ins_upd_tbl_vw_ResevationReports (business logic)
└── ✅ Layer 3: usp_get_ResevationReports_V1 (API access)

✅ OPTIMIZATION TRACKING:
├── ✅ All dry run and optimization execution tables covered
├── ✅ All status tracking and history tables identified
└── ✅ Complete audit trail from attempt to completion

✅ CONFIGURATION SYSTEM:
├── ✅ All client configuration tables and procedures covered
├── ✅ All business rule and threshold mechanisms identified
└── ✅ Complete decision-making infrastructure documented
```

This comprehensive technical reference provides complete documentation for understanding, maintaining, and extending the hotel reservation re-optimization platform with **COMPLETE DATABASE OBJECT COVERAGE** ensuring no gaps in data flow understanding.