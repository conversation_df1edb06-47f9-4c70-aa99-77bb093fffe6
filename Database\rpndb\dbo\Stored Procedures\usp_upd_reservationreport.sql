﻿--/*
CREATE PROCEDURE [dbo].[usp_upd_reservationreport]
(
		@Repricerid    INT 
      , @Reservationid INT  = null
)
AS
--*/

/*

Declare 
		@Repricerid    INT 
= 1
      , @Reservationid INT  
= 167709

--*/
BEGIN
    SET NOCOUNT ON; -- Turns off the message that shows the count of affected rows
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    -- Error handling setup
    BEGIN TRY
    Declare @CurrentDate DATE = GETUTCDATE()

    DROP TABLE IF EXISTS #temp_roomcounttable;
    DROP TABLE IF EXISTS #temp_RecentReservations;
    DROP TABLE IF EXISTS #temp_ClientConfig;
    DROP TABLE IF EXISTS #temp_RecentReservations_dateasc
    DROP TABLE IF EXISTS #temp_LatestResellerInfo
    DROP TABLE IF EXISTS #temp_ActiveTab
    DROP TABLE IF EXISTS #temp_CPEdge
    DROP TABLE IF EXISTS #temp_PriceEdge
    DROP TABLE IF EXISTS #temp_OrderedLogs


    CREATE TABLE #temp_roomcounttable
    (
        Roomcount INT
      , Reservationid INT
      , Repricerid INT
      , RoomType varchar(max)
    );

    CREATE TABLE #temp_RecentReservations_dateasc
    (
        id INT
      , ReservationId INT
      , createdate DATETIME
      , repricerid int
      , profit decimal(10, 2)
    );


    CREATE TABLE #temp_LatestResellerInfo
    (
        RowNum INT
      , Repricerid int
      , ReservationId INT
      , ResellerName varchar(128)
      , ResellerCode varchar(128)
      , ResellerType varchar(128)
    );
    CREATE NONCLUSTERED INDEX IX_temp_temp_LatestResellerInfo_Lookup
    ON #temp_LatestResellerInfo
    (
        RepricerId
      , ReservationId
    );

    ;WITH cte_LatestResellerInfo
    AS (SELECT RepricerId
             , ReservationId
             , ResellerName
             , ResellerCode
             , ResellerType
             , ROW_NUMBER() OVER (PARTITION BY RepricerId
                                             , ReservationId
                                  ORDER BY ISNULL(UpdatedDate, CreatedDate) DESC
                                 ) AS RowNum
        FROM [dbo].[Reservation_ResellerInfo]
        WHERE Repricerid = @Repricerid
              and (
                      ReservationId = @ReservationId
                      or @ReservationId is null
                  )
       )
    INSERT INTO #temp_LatestResellerInfo
    SELECT RowNum
         , RepricerId
         , ReservationId
         , ResellerName
         , ResellerCode
         , ResellerType
    FROM cte_LatestResellerInfo
    Where RowNum = 1

    CREATE TABLE #temp_ClientConfig
    (
        ID INT
      , ReservationID INT
      , DiffDays_Optimisation INT
      , PriceDifferenceValue DECIMAL(18, 5)
      , PriceDifferencePercentage DECIMAL(18, 5)
      , pricedifferencecurrency varchar(200)
      , IsUsePercentage bit
      , traveldaysmaxsearchindays int
      , traveldaysminsearchindays int
      , RepricerID INT
      , CreatedDate DATETIME
    );

    ;WITH cte_ClientConfig
    AS (SELECT ID
             , ReservationID
             , DiffDays_Optimisation
             , PriceDifferenceValue
             , PriceDifferencePercentage
             , pricedifferencecurrency
             , IsUsePercentage
             , traveldaysmaxsearchindays
             , traveldaysminsearchindays
             , RepricerID
             , CreatedDate
             , ROW_NUMBER() OVER (PARTITION BY RepricerID
                                             , ReservationID
                                  ORDER BY RepricerID
                                         , CreatedDate DESC
                                 ) AS RowNum
        FROM PreBook_ClientConfiguration
        WHERE RepricerID = @RepricerID
       )
    INSERT INTO #temp_ClientConfig
    (
        ID
      , ReservationID
      , DiffDays_Optimisation
      , PriceDifferenceValue
      , PriceDifferencePercentage
      , pricedifferencecurrency
      , IsUsePercentage
      , traveldaysmaxsearchindays
      , traveldaysminsearchindays
      , RepricerID
      , CreatedDate
    )
    SELECT ID
         , ReservationID
         , DiffDays_Optimisation
         , PriceDifferenceValue
         , PriceDifferencePercentage
         , pricedifferencecurrency
         , IsUsePercentage
         , traveldaysmaxsearchindays
         , traveldaysminsearchindays
         , RepricerID
         , CreatedDate
    FROM cte_ClientConfig
    WHERE RowNum = 1;

    SELECT CAST(ISNULL(t1.id, null)  AS INT)                                                        as Id
         , 1                                                                                        as TableType
         , t1.ReservationId
         , createdate
         , t1.RepricerID
         , Reservationadultcount
         , Prebookadultcount
         , Reservationchildages
         , Prebookchildages
         , Providers
         , BookingDate
         , (ReservationPrice * CurrencyFactortoEur)                                                 as ReservationPrice
         , (PreBookPrice * CurrencyFactortoEur)                                                     as PreBookPrice
         , (ProfitAfterCancellation * CurrencyFactortoEur)                                          as ProfitAfterCancellation
         , (Profit * CurrencyFactortoEur)                                                           as Profit
         , CurrencyFactortoEur
         , Reservationroomname
         , PreBookRoomName
         , ReservationRoomBoard
         , PreBookRoomBoard
         , ReservationRoomInfo
         , PrebookRoomInfo
         , PreBookRoomIndex
         , MatchedReservationCancellationDate
         , MatchedPreBookCancellationDate
         , MatchedReservationCancellationChargeByPolicy
         , MatchedPreBookCancellationChargeByPolicy
         , IsCancellationPolicyMatched
         , cPStatus
         , cpdaysgain
         , (matchedcancellationpolicygain * CurrencyFactortoEur)                                    as matchedcancellationpolicygain
         , t1.token
         , AvailabilityToken
         , PrebookProviders                                                                         as PrebookSupplier
         , ((dbo.ExtractValue(matchedreservationcancellationchargebypolicy)) * CurrencyFactortoEur) as MatchedReservationCancellationChargeByPolicyToEur
         , ((dbo.ExtractValue(MatchedPreBookCancellationChargeByPolicy)) * CurrencyFactortoEur)     as MatchedPreBookCancellationChargeByPolicytoEur
         , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                           WHEN CHARINDEX(',', ISNULL(ReservationGiataMappingId, '')) > 0 THEN
                                                               CHARINDEX(',', ISNULL(ReservationGiataMappingId, ''))
                                                               - 1
                                                           ELSE
                                                               LEN(ISNULL(ReservationGiataMappingId, ''))
                                                       END)                                         as ReservationGiataMappingId
         , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                      WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                          CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                      ELSE
                                                          LEN(ISNULL(SearchGiataMappingId, ''))
                                                  end)                                              as SearchGiataMappingId
         , PreBookGiataPropertyName
         , ReservationGiataPropertyName
         , ReservationRoomBoardGroup
         , PrebookRoomBoardGroup
         , ReservationCancellationType
         , PreBookCancellationType
         , CancellationPolicyRemark
         , ISNULL(IsOptimized, 0)                                                                   as IsOptimized
         , bat.createdOn                                                                            as bat_createdDate
         , bat.NewBookingId                                                                         as bat_NewBookingId
         , bat.NewBookingPrice                                                                      as bat_Profit
         , bat.ActionId                                                                             as bat_ActionId
    INTO #temp_ActiveTab
    FROM dbo.ReservationTable               t1
        LEFT JOIN dbo.[BookingActionsTaken] AS bat
            ON bat.RePricerID = t1.RePricerID
               AND bat.Reservationid = t1.Reservationid
               AND bat.ActionID = 1
    where 1 = 2;
	ALTER TABLE #temp_ActiveTab ALTER COLUMN Id INT NULL;

	IF OBJECT_ID('tempdb..#temp_ActiveTab') IS NOT NULL
    BEGIN
        --#temp_ActiveTab
        INSERT INTO #temp_ActiveTab
        (
            Id
          , TableType
          , ReservationId
          , createdate
          , RepricerID
          , Reservationadultcount
          , Prebookadultcount
          , Reservationchildages
          , Prebookchildages
          , Providers
          , BookingDate
          , ReservationPrice
          , PreBookPrice
          , ProfitAfterCancellation
          , Profit
          , CurrencyFactortoEur
          , Reservationroomname
          , PreBookRoomName
          , ReservationRoomBoard
          , PreBookRoomBoard
          , ReservationRoomInfo
          , PrebookRoomInfo
          , PreBookRoomIndex
          , MatchedReservationCancellationDate
          , MatchedPreBookCancellationDate
          , MatchedReservationCancellationChargeByPolicy
          , MatchedPreBookCancellationChargeByPolicy
          , IsCancellationPolicyMatched
          , cPStatus
          , cpdaysgain
          , matchedcancellationpolicygain
          , token
          , AvailabilityToken
          , PrebookSupplier
          , MatchedReservationCancellationChargeByPolicyToEur
          , MatchedPreBookCancellationChargeByPolicytoEur
          , ReservationGiataMappingId
          , SearchGiataMappingId
          , PreBookGiataPropertyName
          , ReservationGiataPropertyName
          , ReservationRoomBoardGroup
          , PrebookRoomBoardGroup
          , ReservationCancellationType
          , PreBookCancellationType
          , CancellationPolicyRemark
          , IsOptimized
          , bat_createdDate
          , bat_NewBookingId
          , bat_Profit
          , bat_ActionId
        )
        SELECT ISNULL(t1.id, null)                                                                      as Id
             , 1                                                                                        as TableType
             , t1.ReservationId
             , createdate
             , t1.RepricerID
             , Reservationadultcount
             , Prebookadultcount
             , Reservationchildages
             , Prebookchildages
             , Providers
             , BookingDate
             , (ReservationPrice * CurrencyFactortoEur)                                                 as ReservationPrice
             , (PreBookPrice * CurrencyFactortoEur)                                                     as PreBookPrice
             , (ProfitAfterCancellation * CurrencyFactortoEur)                                          as ProfitAfterCancellation
             , (Profit * CurrencyFactortoEur)                                                           as Profit
             , CurrencyFactortoEur
             , Reservationroomname
             , PreBookRoomName
             , ReservationRoomBoard
             , PreBookRoomBoard
             , ReservationRoomInfo
             , PrebookRoomInfo
             , PreBookRoomIndex
             , MatchedReservationCancellationDate
             , MatchedPreBookCancellationDate
             , MatchedReservationCancellationChargeByPolicy
             , MatchedPreBookCancellationChargeByPolicy
             , IsCancellationPolicyMatched
             , cPStatus
             , cpdaysgain
             , (matchedcancellationpolicygain * CurrencyFactortoEur)                                    as matchedcancellationpolicygain
             , t1.token
             , AvailabilityToken
             , PrebookProviders                                                                         as PrebookSupplier
             , ((dbo.ExtractValue(matchedreservationcancellationchargebypolicy)) * CurrencyFactortoEur) as MatchedReservationCancellationChargeByPolicyToEur
             , ((dbo.ExtractValue(MatchedPreBookCancellationChargeByPolicy)) * CurrencyFactortoEur)     as MatchedPreBookCancellationChargeByPolicytoEur
             , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                               WHEN CHARINDEX(
                                                                                 ','
                                                                               , ISNULL(ReservationGiataMappingId, '')
                                                                             ) > 0 THEN
                                                                   CHARINDEX(',', ISNULL(ReservationGiataMappingId, ''))
                                                                   - 1
                                                               ELSE
                                                                   LEN(ISNULL(ReservationGiataMappingId, ''))
                                                           END)                                         as ReservationGiataMappingId
             , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                          WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                              CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                          ELSE
                                                              LEN(ISNULL(SearchGiataMappingId, ''))
                                                      end)                                              as SearchGiataMappingId
             , PreBookGiataPropertyName
             , ReservationGiataPropertyName
             , ReservationRoomBoardGroup
             , PrebookRoomBoardGroup
             , ReservationCancellationType
             , PreBookCancellationType
             , CancellationPolicyRemark
             , ISNULL(IsOptimized, 0)                                                                   as IsOptimized
             , bat.createdOn                                                                            as bat_createdDate
             , bat.NewBookingId                                                                         as bat_NewBookingId
             , bat.NewBookingPrice                                                                      as bat_Profit
             , bat.ActionId                                                                             as bat_ActionId
        FROM dbo.ReservationTable               t1
            LEFT JOIN dbo.[BookingActionsTaken] AS bat
                ON bat.RePricerID = t1.RePricerID
                   AND bat.Reservationid = t1.Reservationid
                   AND bat.ActionID = 1
        where t1.Repricerid = @Repricerid
              and (
                      t1.ReservationId = @ReservationId
                      or @ReservationId is null
                  );

        INSERT INTO #temp_ActiveTab
        (
            TableType
          , ReservationId
          , createdate
          , RepricerID
          , Reservationadultcount
          , Prebookadultcount
          , Reservationchildages
          , Prebookchildages
          , Providers
          , BookingDate
          , ReservationPrice
          , PreBookPrice
          , ProfitAfterCancellation
          , Profit
          , CurrencyFactortoEur
          , Reservationroomname
          , PreBookRoomName
          , ReservationRoomBoard
          , PreBookRoomBoard
          , ReservationRoomInfo
          , PrebookRoomInfo
          , PreBookRoomIndex
          , MatchedReservationCancellationDate
          , MatchedPreBookCancellationDate
          , MatchedReservationCancellationChargeByPolicy
          , MatchedPreBookCancellationChargeByPolicy
          , IsCancellationPolicyMatched
          , cPStatus
          , cpdaysgain
          , matchedcancellationpolicygain
          , token
          , AvailabilityToken
          , PrebookSupplier
          , MatchedReservationCancellationChargeByPolicyToEur
          , MatchedPreBookCancellationChargeByPolicytoEur
          , ReservationGiataMappingId
          , SearchGiataMappingId
          , PreBookGiataPropertyName
          , ReservationGiataPropertyName
          , ReservationRoomBoardGroup
          , PrebookRoomBoardGroup
          , ReservationCancellationType
          , PreBookCancellationType
          , CancellationPolicyRemark
          , IsOptimized
          , bat_createdDate
          , bat_NewBookingId
          , bat_Profit
          , bat_ActionId
        )
        -- #temp_CPEdge
        SELECT 2                                                                                        as TableType
             , t1.ReservationId
             , createdate
             , t1.RepricerID
             , Reservationadultcount
             , Prebookadultcount
             , Reservationchildages
             , Prebookchildages
             , Providers
             , BookingDate
             , (ReservationPrice * CurrencyFactortoEur)                                                 as ReservationPrice
             , (PreBookPrice * CurrencyFactortoEur)                                                     as PreBookPrice
             , (ProfitAfterCancellation * CurrencyFactortoEur)                                          as ProfitAfterCancellation
             , (Profit * CurrencyFactortoEur)                                                           as Profit
             , CurrencyFactortoEur
             , Reservationroomname
             , PreBookRoomName
             , ReservationRoomBoard
             , PreBookRoomBoard
             , ReservationRoomInfo
             , PrebookRoomInfo
             , PreBookRoomIndex
             , MatchedReservationCancellationDate
             , MatchedPreBookCancellationDate
             , MatchedReservationCancellationChargeByPolicy
             , MatchedPreBookCancellationChargeByPolicy
             , IsCancellationPolicyMatched
             , cPStatus
             , cpdaysgain
             , (matchedcancellationpolicygain * CurrencyFactortoEur)                                    as matchedcancellationpolicygain
             , t1.token
             , AvailabilityToken
             , PrebookProviders                                                                         as PrebookSupplier
             , ((dbo.ExtractValue(matchedreservationcancellationchargebypolicy)) * CurrencyFactortoEur) as MatchedReservationCancellationChargeByPolicyToEur
             , ((dbo.ExtractValue(MatchedPreBookCancellationChargeByPolicy)) * CurrencyFactortoEur)     as MatchedPreBookCancellationChargeByPolicytoEur
             , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                               WHEN CHARINDEX(
                                                                                 ','
                                                                               , ISNULL(ReservationGiataMappingId, '')
                                                                             ) > 0 THEN
                                                                   CHARINDEX(',', ISNULL(ReservationGiataMappingId, ''))
                                                                   - 1
                                                               ELSE
                                                                   LEN(ISNULL(ReservationGiataMappingId, ''))
                                                           END)                                         as ReservationGiataMappingId
             , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                          WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                              CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                          ELSE
                                                              LEN(ISNULL(SearchGiataMappingId, ''))
                                                      end)                                              as SearchGiataMappingId
             , PreBookGiataPropertyName
             , ReservationGiataPropertyName
             , ReservationRoomBoardGroup
             , PrebookRoomBoardGroup
             , ReservationCancellationType
             , PreBookCancellationType
             , CancellationPolicyRemark
             , 0                                                                                        as IsOptimized
             , bat.createdOn                                                                            as bat_createdDate
             , ISNULL(bat.NewBookingId, 0)                                                              as bat_NewBookingId
             , bat.NewBookingPrice                                                                      as bat_Profit
             , bat.ActionId                                                                             as bat_ActionId
        FROM ReservationTablelog                t1
            LEFT JOIN dbo.[BookingActionsTaken] AS bat
                ON bat.RePricerID = t1.RePricerID
                   AND bat.Reservationid = t1.Reservationid
                   AND bat.ActionID = 1
        where t1.Repricerid = @Repricerid
              and (
                      t1.ReservationId = @ReservationId
                      or @ReservationId is null
                  )
              and t1.Profit > 5.0
              and bat.Reservationid is null
              and (
                      t1.ReservationId = @ReservationId
                      or @ReservationId is null
                  );

		/*
        INSERT INTO #temp_ActiveTab
        (
            TableType
          , ReservationId
          , createdate
          , RepricerID
          , Reservationadultcount
          , Prebookadultcount
          , Reservationchildages
          , Prebookchildages
          , Providers
          , BookingDate
          , ReservationPrice
          , PreBookPrice
          , ProfitAfterCancellation
          , Profit
          , CurrencyFactortoEur
          , Reservationroomname
          , PreBookRoomName
          , ReservationRoomBoard
          , PreBookRoomBoard
          , ReservationRoomInfo
          , PrebookRoomInfo
          , PreBookRoomIndex
          , MatchedReservationCancellationDate
          , MatchedPreBookCancellationDate
          , MatchedReservationCancellationChargeByPolicy
          , MatchedPreBookCancellationChargeByPolicy
          , IsCancellationPolicyMatched
          , cPStatus
          , cpdaysgain
          , matchedcancellationpolicygain
          , token
          , AvailabilityToken
          , PrebookSupplier
          , MatchedReservationCancellationChargeByPolicyToEur
          , MatchedPreBookCancellationChargeByPolicytoEur
          , ReservationGiataMappingId
          , SearchGiataMappingId
          , PreBookGiataPropertyName
          , ReservationGiataPropertyName
          , ReservationRoomBoardGroup
          , PrebookRoomBoardGroup
          , ReservationCancellationType
          , PreBookCancellationType
          , CancellationPolicyRemark
          , IsOptimized
          , bat_createdDate
          , bat_NewBookingId
          , bat_Profit
          , bat_ActionId
        )
        -- #temp_PriceEdge
        SELECT 3                                                as TableType
             , t1.ReservationId
             , t1.createdate
             , t1.RepricerID
             , t1.Reservationadultcount
             , t1.Prebookadultcount
             , t1.Reservationchildages
             , t1.Prebookchildages
             , t1.Providers
             , t1.BookingDate
             , (t1.ReservationPrice * t1.CurrencyFactortoEur)   as ReservationPrice
             , (t1.PreBookPrice * t1.CurrencyFactortoEur)       as PreBookPrice
             , null                                             as ProfitAfterCancellation
             , (t1.Profit * t1.CurrencyFactortoEur)             as Profit
             , t1.CurrencyFactortoEur
             , t1.Reservationroomname
             , t1.PreBookRoomName
             , t1.ReservationRoomBoard
             , t1.PreBookRoomBoard
             , t1.ReservationRoomInfo
             , t1.PrebookRoomInfo
             , t1.PreBookRoomIndex
             , null                                             as MatchedReservationCancellationDate
             , null                                             as MatchedPreBookCancellationDate
             , null                                             as MatchedReservationCancellationChargeByPolicy
             , null                                             as MatchedPreBookCancellationChargeByPolicy
             , null                                             as IsCancellationPolicyMatched
             , null                                             as cPStatus
             , 0                                                as cpdaysgain
             , 0                                                as matchedcancellationpolicygain
             , null                                             as token
             , null                                             as AvailabilityToken
             , t1.PrebookProviders                              as PrebookSupplier
             , 0                                                as MatchedReservationCancellationChargeByPolicyToEur
             , 0                                                as MatchedPreBookCancellationChargeByPolicytoEur
             , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                               WHEN CHARINDEX(
                                                                                 ','
                                                                               , ISNULL(ReservationGiataMappingId, '')
                                                                             ) > 0 THEN
                                                                   CHARINDEX(',', ISNULL(ReservationGiataMappingId, ''))
                                                                   - 1
                                                               ELSE
                                                                   LEN(ISNULL(ReservationGiataMappingId, ''))
                                                           END) as ReservationGiataMappingId
             , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                          WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                              CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                          ELSE
                                                              LEN(ISNULL(SearchGiataMappingId, ''))
                                                      end)      as SearchGiataMappingId
             , PreBookGiataPropertyName
             , ReservationGiataPropertyName
             , ReservationRoomBoardGroup
             , PrebookRoomBoardGroup
             , ''                                               ReservationCancellationType
             , ''                                               PreBookCancellationType
             , ''                                               CancellationPolicyRemark
             , 0                                                as IsOptimized
             , bat.createdOn                                    as bat_createdDate
             , ISNULL(bat.NewBookingId, 0)                      as bat_NewBookingId
             , bat.NewBookingPrice                              as bat_Profit
             , bat.ActionId                                     as bat_ActionId
        FROM prebooklog                         t1
            inner join #temp_ClientConfig       cc
                on cc.Repricerid = t1.Repricerid
            LEFT JOIN dbo.[BookingActionsTaken] AS bat
                ON bat.RePricerID = t1.RePricerID
                   AND bat.Reservationid = t1.Reservationid
                   AND bat.ActionID = 1
        where t1.Repricerid = @Repricerid
              and (
                      t1.ReservationId = @ReservationId
                      or @ReservationId is null
                  )
              and t1.Profit > 5.0
              and bat.Reservationid is null
              AND 1 = (case
                           when ISNULL(cc.IsUsePercentage, 0) = 1
                                AND Round(((t1.Profit / t1.ReservationPrice) * 100), 2) < cc.priceDifferencePercentage Then
                               1
                           else
                               case
                                   when ISNULL(cc.IsUsePercentage, 0) = 0
                                        AND t1.Profit < cc.priceDifferenceValue then
                                       1
                                   else
                                       0
                               end
                       end
                      );
		--*/
    END

    SELECT *
    into #temp_OrderedLogs
    From
    (
        SELECT ROW_NUMBER() OVER (PARTITION BY RepricerId
                                             , ReservationID
                                  ORDER BY ISNULL(IsOptimized, 0) desc
                                         , cast(CreateDate as date) DESC --, Profit Desc,
										 --, TableType
                                         , CASE
                                               WHEN (cpstatus = 'loose'
                                                    --or cpstatus = 'tightWithBuffer'
                                                    )
                                                    AND ReservationGiataMappingId IS NULL
                                                    and cast(CreateDate as date) = @CurrentDate THEN
                                                   1
                                               WHEN (cpstatus = 'loose'
                                                    --or cpstatus = 'tightWithBuffer'
                                                    )
                                                    AND ReservationGiataMappingId IS not NULL
                                                    and cast(CreateDate as date) = @CurrentDate THEN
                                                   2
                                               ELSE
                                                   3
                                           END
                                         , cast(CreateDate as date) DESC
                                         , Profit Desc
                                         , CreateDate DESC
                                 ) as rn
             , *
        FROM #temp_ActiveTab
    ) AS with_row_numbers
    WHERE rn = 1;

 /*

 Select * from #temp_OrderedLogs

 SELECT rn as RowNumber,
        TableType,
        id,
        t.createdate,
        ISNULL(t.IsOptimized,0) as IsOptimized,
   CASE
    WHEN t.Providers = t.PrebookSupplier THEN 1
    ELSE 0
   END AS ProvidersMatch,
   t.cPStatus,
        t.Profit,
        t.Providers AS ReservationSupplier,
        t.PrebookSupplier AS PrebookSupplier,
        t.ReservationRoomname,
        t.PrebookRoomname,

        t.ReservationId,
        t.ReservationPrice,
        t.PreBookPrice,
        t.CurrencyFactortoEur,
        t.MatchedReservationCancellationDate AS ResCP_Date,
        t.MatchedPreBookCancellationDate AS PreCP_Date,
        t.MatchedReservationCancellationChargeByPolicy AS ResCP_charge,
        t.MatchedPreBookCancellationChargeByPolicy AS PreCP_charge
  ,  t.bat_createdDate
  ,  t.bat_NewBookingId
  ,  t.bat_Profit
  ,  t.bat_ActionId
        from #temp_OrderedLogs as t
        Where RepricerId = @RepricerId
                and (
                      ReservationId = @ReservationId
                      or @ReservationId is null
                  )
    and cPStatus is not null
  Order by rn
  return;
  --*/

    INSERT INTO #temp_RecentReservations_dateasc
    SELECT id
         , ReservationId
         , createdate
         , RePricerId
         , Profit
    FROM #temp_OrderedLogs
    Where RePricerID = @RePricerID
          and (
                  ReservationId = @ReservationId
                  or @ReservationId is null
              )

    INSERT INTO #temp_roomcounttable
    (
        Roomcount
      , Reservationid
      , Repricerid
      , RoomType
    )
    SELECT Count(RR.Roomname)                                 AS RoomCount
         , RR.Reservationid
         , ISNULL(RR.Repricerid, @Repricerid)                 AS RepricerId
         , STRING_AGG(
				CASE
					WHEN RR.RoomType IS NOT NULL AND RR.RoomType != '' THEN CAST(RR.RoomType AS VARCHAR(MAX))
					ELSE NULL
				END,
				',') AS RoomType
    FROM dbo.RESERVATIONROOM RR
    Where Repricerid = @Repricerid
          and (
                  ReservationId = @ReservationId
                  or @ReservationId is null
              )
    GROUP BY RR.Reservationid
           , ISNULL(RR.Repricerid, @Repricerid)
    HAVING COUNT(RR.RoomType) <= 20;


    WITH cte_RecentReservations
    AS (SELECT id
             , ReservationId
             , createdate
             , RepricerID
             , Reservationadultcount
             , Prebookadultcount
             , Reservationchildages
             , Prebookchildages
             , Providers
             , BookingDate
             , ReservationPrice
             , PreBookPrice
             , ProfitAfterCancellation
             , Profit
             , CurrencyFactortoEur
             , Reservationroomname
             , PreBookRoomName
             , ReservationRoomBoard
             , PreBookRoomBoard
             , ReservationRoomInfo
             , PrebookRoomInfo
             , PreBookRoomIndex
             , MatchedReservationCancellationDate
             , MatchedPreBookCancellationDate
             , MatchedReservationCancellationChargeByPolicy
             , MatchedPreBookCancellationChargeByPolicy
             , IsCancellationPolicyMatched
             , cPStatus
             , cpdaysgain
             , matchedcancellationpolicygain
             , token
             , AvailabilityToken
             , PrebookSupplier
             , MatchedReservationCancellationChargeByPolicyToEur
             , MatchedPreBookCancellationChargeByPolicytoEur
             , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                               WHEN CHARINDEX(
                                                                                 ','
                                                                               , ISNULL(ReservationGiataMappingId, '')
                                                                             ) > 0 THEN
                                                                   CHARINDEX(',', ISNULL(ReservationGiataMappingId, ''))
                                                                   - 1
                                                               ELSE
                                                                   LEN(ISNULL(ReservationGiataMappingId, ''))
                                                           END) as ReservationGiataMappingId
             , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                          WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                              CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                          ELSE
                                                              LEN(ISNULL(SearchGiataMappingId, ''))
                                                      END)      as SearchGiataMappingId
             , PreBookGiataPropertyName
             , ReservationGiataPropertyName
             , ReservationRoomBoardGroup
             , PrebookRoomBoardGroup
             , ReservationCancellationType
             , PreBookCancellationType
             , CancellationPolicyRemark
        FROM #temp_OrderedLogs
       )
    MERGE INTO ReservationReportDetails AS target
    USING
    (
        SELECT DISTINCT
            rt.id
          , rt.Reservationid
          , rt.Repricerid
          , rt.Createdate
          , rt.Reservationadultcount
          , rt.Prebookadultcount
          , rt.Reservationchildages
          , rt.PrebookChildAges
          , rt.Providers
          , rt.BookingDate
          , rt.ReservationPrice
          , rt.PreBookPrice
          , rt.ProfitAfterCancellation
          , rt.Profit
          , rt.CurrencyFactorToEur
          , rt.ReservationRoomName
          , rt.PreBookRoomName
          , rt.ReservationRoomBoard
          , rt.PreBookRoomBoard
          , rt.ReservationRoomInfo
          , rt.PrebookRoomInfo
          , rt.PreBookRoomIndex
          , rt.MatchedReservationCancellationDate
          , rt.MatchedPreBookCancellationDate
          , rt.MatchedReservationCancellationChargeByPolicy
          , rt.MatchedPreBookCancellationChargeByPolicy
          , rt.IsCancellationPolicyMatched
          , rt.CPStatus
          , rt.CPDaysGain
          , rt.MatchedCancellationPolicyGain
          , rt.Token
          , rt.AvailabilityToken
          , TRC.Roomcount                                       AS NumberOfRooms
          , rm.ReservationStatus
          , rt.PrebookSupplier
          , rm.checkIn
          , rm.Checkout
          , rh.hotelname                                        AS reservationhotelname
          , rh.hotelname                                        AS prebookhotelname
          , Isnull(rm.destinations, '')                         AS prebookdestination
          , Isnull(rm.destinations, '')                         AS reservationdestination
          , ISNULL(trc.RoomType, '')                            as roomType
          , rrs.createdate                                      as firstcreatedate
          , ccr.DiffDays_Optimisation
          , ccr.PriceDifferenceValue
          , ccr.PriceDifferencePercentage
          , ccr.pricedifferencecurrency
          , ccr.IsUsePercentage
          , ccr.traveldaysmaxsearchindays
          , ccr.traveldaysminsearchindays
          , rt.MatchedReservationCancellationChargeByPolicyToEur
          , rt.MatchedPreBookCancellationChargeByPolicytoEur
          , LEFT(ISNULL(rt.ReservationGiataMappingId, ''), CASE
                                                               WHEN CHARINDEX(
                                                                                 ','
                                                                               , ISNULL(
                                                                                           rt.ReservationGiataMappingId
                                                                                         , ''
                                                                                       )
                                                                             ) > 0 THEN
                                                                   CHARINDEX(
                                                                                ','
                                                                              , ISNULL(rt.ReservationGiataMappingId, '')
                                                                            ) - 1
                                                               ELSE
                                                                   LEN(ISNULL(rt.ReservationGiataMappingId, ''))
                                                           END) as ReservationGiataMappingId
          , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                       WHEN CHARINDEX(',', ISNULL(rt.SearchGiataMappingId, '')) > 0 THEN
                                                           CHARINDEX(',', ISNULL(rt.SearchGiataMappingId, '')) - 1
                                                       ELSE
                                                           LEN(ISNULL(rt.SearchGiataMappingId, ''))
                                                   end)         as SearchGiataMappingId
          , PreBookGiataPropertyName
          , ReservationGiataPropertyName
          , ReservationRoomBoardGroup
          , PrebookRoomBoardGroup
          , ReservationCancellationType
          , PreBookCancellationType
          , CancellationPolicyRemark
          , resell.[ResellerName]
          , resell.[ResellerCode]
          , resell.[ResellerType]
        FROM cte_RecentReservations                    rt
            left JOIN dbo.RESERVATIONMAIN              RM
                ON RM.Reservationid = RT.Reservationid
                   AND RT.Repricerid = RM.Repricerid
            left JOIN #temp_roomcounttable             TRC
                ON TRC.Reservationid = RT.Reservationid
                   AND RT.Repricerid = trc.Repricerid
            left JOIN dbo.reservationhotelinformation  RH
                ON rh.reservationid = rt.reservationid
                   AND rh.repricerid = rt.repricerid
            left JOIN #temp_RecentReservations_dateasc rrs
                ON rrs.reservationid = rt.reservationid
                   AND rrs.repricerid = rt.repricerid
            left JOIN #temp_ClientConfig               ccr
                ON ccr.reservationid = rt.reservationid
                   AND ccr.repricerid = rt.repricerid
            left join #temp_LatestResellerInfo         as resell
                on rt.Repricerid = resell.RepricerId
                   and rt.Reservationid = resell.Reservationid
        where rt.Repricerid = @Repricerid
              and (
                      rt.ReservationId = @ReservationId
                      or @ReservationId is null
                  )
              and rm.checkin is not null
    ) AS source
    (id, Reservationid, Repricerid, Createdate, Reservationadultcount, Prebookadultcount, Reservationchildages, PrebookChildAges, Providers, BookingDate, ReservationPrice, PreBookPrice, ProfitAfterCancellation, Profit, CurrencyFactorToEur, ReservationRoomName, PreBookRoomName, ReservationRoomBoard, PreBookRoomBoard, ReservationRoomInfo, PrebookRoomInfo, PreBookRoomIndex, MatchedReservationCancellationDate, MatchedPreBookCancellationDate, MatchedReservationCancellationChargeByPolicy, MatchedPreBookCancellationChargeByPolicy, IsCancellationPolicyMatched, CPStatus, CPDaysGain, MatchedCancellationPolicyGain, Token, AvailabilityToken, NumberOfRooms, ReservationStatus, PrebookSupplier, checkin, checkout, reservationhotelname, prebookhotelname, prebookdestination, reservationdestination, roomType, firstcreatedate, DiffDays_Optimisation, PriceDifferenceValue, PriceDifferencePercentage, pricedifferencecurrency, IsUsePercentage, traveldaysmaxsearchindays, traveldaysminsearchindays, MatchedReservationCancellationChargeByPolicyToEur, MatchedPreBookCancellationChargeByPolicytoEur, ReservationGiataMappingId, SearchGiataMappingId, PreBookGiataPropertyName, ReservationGiataPropertyName, ReservationRoomBoardGroup, PrebookRoomBoardGroup, ReservationCancellationType, PreBookCancellationType, CancellationPolicyRemark, [ResellerName], [ResellerCode], [ResellerType])
    ON (
           target.Reservationid = source.Reservationid
           and target.Repricerid = source.Repricerid
       )
    WHEN MATCHED THEN
        UPDATE SET target.PreBookId = source.id
                 -- , target.Repricerid = source.Repricerid
                 , target.UpdatedOn = source.Createdate
                 , target.createdate = source.firstcreatedate
                 , target.Reservationadultcount = source.Reservationadultcount
                 , target.Prebookadultcount = source.Prebookadultcount
                 , target.Reservationchildages = source.Reservationchildages
                 , target.PrebookChildAges = source.PrebookChildAges
                 , target.Providers = source.Providers
                 , target.BookingDate = source.BookingDate
                 , target.ReservationPrice = source.ReservationPrice
                 , target.PreBookPrice = source.PreBookPrice
                 , target.ProfitAfterCancellation = source.ProfitAfterCancellation
                 , target.Profit = source.Profit
                 , target.CurrencyFactorToEur = source.CurrencyFactorToEur
                 , target.ReservationRoomName = source.ReservationRoomName
                 , target.PreBookRoomName = source.PreBookRoomName
                 , target.ReservationRoomBoard = source.ReservationRoomBoard
                 , target.PreBookRoomBoard = source.PreBookRoomBoard
                 , target.ReservationRoomInfo = source.ReservationRoomInfo
                 , target.PrebookRoomInfo = source.PrebookRoomInfo
                 , target.PreBookRoomIndex = source.PreBookRoomIndex
                 , target.MatchedReservationCancellationDate = source.MatchedReservationCancellationDate
                 , target.MatchedPreBookCancellationDate = source.MatchedPreBookCancellationDate
                 , target.MatchedReservationCancellationChargeByPolicy = source.MatchedReservationCancellationChargeByPolicy
                 , target.MatchedPreBookCancellationChargeByPolicy = source.MatchedPreBookCancellationChargeByPolicy
                 , target.IsCancellationPolicyMatched = source.IsCancellationPolicyMatched
                 , target.CPStatus = source.CPStatus
                 , target.CPDaysGain = source.CPDaysGain
                 , target.MatchedCancellationPolicyGain = source.MatchedCancellationPolicyGain
                 , target.Token = source.Token
                 , target.AvailabilityToken = source.AvailabilityToken
                 , target.NumberOfRooms = source.NumberOfRooms
                 , target.ReservationStatus = source.ReservationStatus
                 , target.prebooksupplier = isnull(source.prebooksupplier, source.Providers)
                 , target.checkin = source.checkin
                 , target.checkout = source.checkout
                 , target.reservationhotelname = source.reservationhotelname
                 , target.prebookhotelname = source.prebookhotelname
                 , target.prebookdestination = source.prebookdestination
                 , target.reservationdestination = source.reservationdestination
                 , target.roomType = source.roomType
                 , target.DiffDays_Optimisation = source.DiffDays_Optimisation
                 , target.PriceDifferenceValue = source.PriceDifferenceValue
                 , target.PriceDifferencePercentage = source.PriceDifferencePercentage
                 , target.pricedifferencecurrency = source.pricedifferencecurrency
                 , target.IsUsePercentage = source.IsUsePercentage
                 , target.traveldaysmaxsearchindays = source.traveldaysmaxsearchindays
                 , target.traveldaysminsearchindays = source.traveldaysminsearchindays
                 , target.MatchedReservationCancellationChargeByPolicyToEur = source.MatchedReservationCancellationChargeByPolicyToEur
                 , target.MatchedPreBookCancellationChargeByPolicytoEur = source.MatchedPreBookCancellationChargeByPolicytoEur
                 , target.ReservationGiataMappingId = source.ReservationGiataMappingId
                 , target.SearchGiataMappingId = source.SearchGiataMappingId
                 , target.PreBookGiataPropertyName = source.PreBookGiataPropertyName
                 , target.ReservationGiataPropertyName = source.ReservationGiataPropertyName
                 , target.ReservationRoomBoardGroup = source.ReservationRoomBoardGroup
                 , target.PrebookRoomBoardGroup = source.PrebookRoomBoardGroup
                 , target.ReservationCancellationType = source.ReservationCancellationType
                 , target.PreBookCancellationType = source.PreBookCancellationType
                 , target.CancellationPolicyRemark = source.CancellationPolicyRemark
                 , target.[ResellerName] = source.[ResellerName]
                 , target.[ResellerCode] = source.[ResellerCode]
                 , target.[ResellerType] = source.[ResellerType]
    WHEN NOT MATCHED BY TARGET THEN
        INSERT
        (
            PreBookId
          , Reservationid
          , Repricerid
          , Createdate
          , Reservationadultcount
          , Prebookadultcount
          , Reservationchildages
          , PrebookChildAges
          , Providers
          , BookingDate
          , ReservationPrice
          , PreBookPrice
          , ProfitAfterCancellation
          , Profit
          , CurrencyFactorToEur
          , ReservationRoomName
          , PreBookRoomName
          , ReservationRoomBoard
          , PreBookRoomBoard
          , ReservationRoomInfo
          , PrebookRoomInfo
          , PreBookRoomIndex
          , MatchedReservationCancellationDate
          , MatchedPreBookCancellationDate
          , MatchedReservationCancellationChargeByPolicy
          , MatchedPreBookCancellationChargeByPolicy
          , IsCancellationPolicyMatched
          , CPStatus
          , CPDaysGain
          , MatchedCancellationPolicyGain
          , Token
          , AvailabilityToken
          , NumberOfRooms
          , ReservationStatus
          , prebooksupplier
          , checkin
          , checkout
          , reservationhotelname
          , prebookhotelname
          , prebookdestination
          , reservationdestination
          , roomType
          , UpdatedOn
          , DiffDays_Optimisation
          , PriceDifferenceValue
          , PriceDifferencePercentage
          , pricedifferencecurrency
          , IsUsePercentage
          , traveldaysmaxsearchindays
          , traveldaysminsearchindays
          , MatchedReservationCancellationChargeByPolicyToEur
          , MatchedPreBookCancellationChargeByPolicytoEur
          , ReservationGiataMappingId
          , SearchGiataMappingId
          , PreBookGiataPropertyName
          , ReservationGiataPropertyName
          , ReservationRoomBoardGroup
          , PrebookRoomBoardGroup
          , ReservationCancellationType
          , PreBookCancellationType
          , CancellationPolicyRemark
          , [ResellerName]
          , [ResellerCode]
          , [ResellerType]
        )
        VALUES
        (source.id
       , source.Reservationid
       , source.Repricerid
       , source.firstcreatedate
       , source.Reservationadultcount
       , source.Prebookadultcount
       , source.Reservationchildages
       , source.PrebookChildAges
       , source.Providers
       , source.BookingDate
       , source.ReservationPrice
       , source.PreBookPrice
       , source.ProfitAfterCancellation
       , source.Profit
       , source.CurrencyFactorToEur
       , source.ReservationRoomName
       , source.PreBookRoomName
       , source.ReservationRoomBoard
       , source.PreBookRoomBoard
       , source.ReservationRoomInfo
       , source.PrebookRoomInfo
       , source.PreBookRoomIndex
       , source.MatchedReservationCancellationDate
       , source.MatchedPreBookCancellationDate
       , source.MatchedReservationCancellationChargeByPolicy
       , source.MatchedPreBookCancellationChargeByPolicy
       , source.IsCancellationPolicyMatched
       , source.CPStatus
       , source.CPDaysGain
       , source.MatchedCancellationPolicyGain
       , source.Token
       , source.AvailabilityToken
       , source.NumberOfRooms
       , source.ReservationStatus
       , isnull(source.prebooksupplier, source.Providers)
       , source.checkin
       , source.checkout
       , source.reservationhotelname
       , source.prebookhotelname
       , source.prebookdestination
       , source.reservationdestination
       , source.roomType
       , source.Createdate
       , source.DiffDays_Optimisation
       , source.PriceDifferenceValue
       , source.PriceDifferencePercentage
       , source.pricedifferencecurrency
       , source.IsUsePercentage
       , source.traveldaysmaxsearchindays
       , source.traveldaysminsearchindays
       , source.MatchedReservationCancellationChargeByPolicyToEur
       , source.MatchedPreBookCancellationChargeByPolicytoEur
       , source.ReservationGiataMappingId
       , source.SearchGiataMappingId
       , source.PreBookGiataPropertyName
       , source.ReservationGiataPropertyName
       , source.ReservationRoomBoardGroup
       , source.PrebookRoomBoardGroup
       , source.ReservationCancellationType
       , source.PreBookCancellationType
       , source.CancellationPolicyRemark
       , source.[ResellerName]
       , source.[ResellerCode]
       , source.[ResellerType]
        );

    Select @Repricerid    as Repricerid
         , @ReservationId as ReservationId
         , @@ROWCOUNT     [insertedOrUpdated]

    IF EXISTS (Select top 1 1 from #temp_RecentReservations_dateasc)
    BEGIN
        IF EXISTS
        (
            Select top 1
                1
            from dbo.SummaryView
            Where RepricerId = @RepricerId
        )
        BEGIN

            MERGE INTO dbo.SummaryView AS target
            USING
            (
                SELECT RepricerId
                     , CreateDate
                FROM dbo.SummaryView
                WHERE Repricerid = @RepricerId
            ) AS source
            ON target.RepricerId = source.RepricerId
            WHEN MATCHED THEN
                DELETE;
        END
    END
    -- remove tomorrow kepts as data are there in reservationtable
    --   Select * from dbo.ReservationReportDetails where ReservationGiataMappingId like '%,%'
    --Select * from dbo.ReservationTable where ReservationGiataMappingId like '%,%'  order by createDate desc

	IF(ISNULL(@Reservationid ,0) > 0)
	BEGIN

    SELECT t.RepricerId
         , t.ReservationId
         , TableType
         , id
         , t.createdate
         , ISNULL(t.IsOptimized, 0)                       as IsOptimized
         , CASE
               WHEN t.Providers = t.PrebookSupplier THEN
                   1
               ELSE
                   0
           END                                            AS ProvidersMatch
         , t.cPStatus
         , t.Profit
         , t.Providers                                    AS ReservationSupplier
         , t.PrebookSupplier                              AS PrebookSupplier
         , t.ReservationRoomname
         , t.PrebookRoomname
         , t.ReservationPrice
         , t.PreBookPrice
         , t.CurrencyFactortoEur
         , t.MatchedReservationCancellationDate           AS ResCP_Date
         , t.MatchedPreBookCancellationDate               AS PreCP_Date
         , t.MatchedReservationCancellationChargeByPolicy AS ResCP_charge
         , t.MatchedPreBookCancellationChargeByPolicy     AS PreCP_charge
         , t.bat_createdDate
         , t.bat_NewBookingId
         , t.bat_Profit
         , t.bat_ActionId
    from #temp_OrderedLogs as t
    Where RepricerId = @RepricerId
          and (
                  ReservationId = @ReservationId
                  or @ReservationId is null
              )

		/*
        Select DISTINCT top 3
            'ReservationReportDetails' as tbl
          , rrd.*
        from ReservationReportDetails              as rrd
            inner join #temp_RecentReservations_dateasc as t1
                on t1.RepricerId = rrd.RepricerId
                   And t1.Reservationid = rrd.Reservationid
        Where rrd.RepricerId = @RepricerId
        order by rrd.Createdate desc
		--*/
	END


    exec dbo.usp_Insert_DailyOptimizationReport @RepricerId = @RepricerId;

    -- ENHANCEMENT: Populate additional prebook options (ranks 2-3) for multiple prebook functionality
    BEGIN TRY
        exec dbo.usp_upd_reservationreport_AdditionalPrebook @Repricerid = @Repricerid, @Reservationid = @Reservationid;
    END TRY
    BEGIN CATCH
        -- Log error but don't fail the main procedure
        PRINT 'Error in usp_upd_reservationreport_AdditionalPrebook: ' + ERROR_MESSAGE();
    END CATCH

    /*
 Select DISTINCT top 1 'vw_ResevationReports' as tbl, rrd.* from vw_ResevationReports    as rrd
 inner join #temp_RecentReservations_dateasc as t1
 on t1.RepricerId = rrd.RepricerId
 And t1.Reservationid = rrd.Reservationid
    Where rrd.RepricerId = @RepricerId
    order by rrd.Createdate desc
 */

    END TRY
    BEGIN CATCH
        -- Handle the error
        SELECT ERROR_NUMBER()  AS ErrorNumber
             , ERROR_MESSAGE() AS ErrorMessage;
    END CATCH

    DROP TABLE IF EXISTS #temp_roomcounttable;
    DROP TABLE IF EXISTS #temp_RecentReservations;
    DROP TABLE IF EXISTS #temp_ClientConfig;
    DROP TABLE IF EXISTS #temp_RecentReservations_dateasc
    DROP TABLE IF EXISTS #temp_LatestResellerInfo
    DROP TABLE IF EXISTS #temp_ActiveTab
    DROP TABLE IF EXISTS #temp_CPEdge
    DROP TABLE IF EXISTS #temp_PriceEdge
    DROP TABLE IF EXISTS #temp_OrderedLogs
END