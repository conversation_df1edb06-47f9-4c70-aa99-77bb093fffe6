﻿--/*
CREATE Procedure dbo.usp_get_CancellationPolicy
    @RePricerId Int
  , @ReservationId int = null
As
begin

    DECLARE @CurrentDate DATE = GETUTCDATE();


    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	;with cte as (
	Select cp.RepricerId,cp.ReservationId,cp.cancellation_type,cp.cancellation_charge, MAX(cp.id) as id
	from [dbo].[ReservationCancellationPolicy] cp
	where cp.RePricerId = @RePricerId
          AND (
				  @ReservationId is null
                  or cp.ReservationId = @ReservationId
                  OR @ReservationId = 0
			)
	group by cp.RepricerId,cp.ReservationId,cp.cancellation_type,cp.cancellation_charge
	)
    SELECT DISTINCT rcp.ReservationId
         , rcp.Cancellation_type
         , DATEDIFF(DAY, GETUTCDATE(), rcp.date) AS DaysDifference
         , rcp.date                              as cancellationdate
         , rcp.Cancellation_Charge
         , rcp.Currency
         , rm.cancellation_date                  as maincancellationdate
    from cte cp
        inner join dbo.ReservationMain         rm
            on rm.ReservationId = cp.reservationId
               and rm.repricerid = cp.repricerid
		inner join [dbo].[ReservationCancellationPolicy] rcp
		on cp.id = rcp.id
		 and rm.ReservationId = rcp.reservationId
               and rm.repricerid = rcp.repricerid

    where rm.RePricerId = @RePricerId
          AND Cast(rm.checkIn AS DATE) >= @CurrentDate
          AND rm.ReservationStatus = 'OK'
         
          AND cast(rm.cancellation_date as date) > @CurrentDate
          AND (
				  @ReservationId is null
                  or rm.ReservationId = @ReservationId
                  OR @ReservationId = 0
			)	
                     

               
    order by rm.ReservationId
           , DaysDifference

End