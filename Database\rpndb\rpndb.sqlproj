﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>rpndb</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{f7566042-8d11-4be6-8d92-a1578cbfdb95}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql160DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>rpndb</RootNamespace>
    <AssemblyName>rpndb</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="dbo\Functions\" />
    <Folder Include="dbo\Stored Procedures\" />
    <Folder Include="dbo\User Defined Types\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\Reservation_Supplier.sql" />
    <Build Include="dbo\Tables\MultiSupplierRoomMapping.sql" />
    <Build Include="dbo\Tables\SummaryView.sql" />
    <Build Include="dbo\Tables\MultiSupplierRooms.sql" />
    <Build Include="dbo\Tables\ReservationStatus.sql" />
    <Build Include="dbo\Tables\tempInvoiceResultOld.sql" />
    <Build Include="dbo\Tables\ActionDetail.sql" />
    <Build Include="dbo\Tables\tempInvoiceResultOldSummary.sql" />
    <Build Include="dbo\Tables\MultiSupplierRoomsTemp.sql" />
    <Build Include="dbo\Tables\BookingActionsTaken.sql" />
    <Build Include="dbo\Tables\ClientTable.sql" />
    <Build Include="dbo\Tables\CURRENCY.sql" />
    <Build Include="dbo\Tables\SummaryViewAggregate.sql" />
    <Build Include="dbo\Tables\EXCHANGE_RATE.sql" />
    <Build Include="dbo\Tables\ExchangeRateData.sql" />
    <Build Include="dbo\Tables\ExtraServices.sql" />
    <Build Include="dbo\Tables\Occupancies.sql" />
    <Build Include="dbo\Tables\Periods.sql" />
    <Build Include="dbo\Tables\MultiRoomMapping.sql" />
    <Build Include="dbo\Tables\ReservationReportDetails.sql" />
    <Build Include="dbo\Tables\PreBook_CancellationPolicy.sql" />
    <Build Include="dbo\Tables\PreBook_ClientConfiguration.sql" />
    <Build Include="dbo\Tables\MultiRoomSupplierMapping.sql" />
    <Build Include="dbo\Tables\PreBook_PackageRooms.sql" />
    <Build Include="dbo\Tables\PreBookLog.sql" />
    <Build Include="dbo\Tables\PreBookRoom.sql" />
    <Build Include="dbo\Tables\PreBookSupplierLog.sql" />
    <Build Include="dbo\Tables\RepricerReservationCountTable.sql" />
    <Build Include="dbo\Tables\Reservation_Passengers.sql" />
    <Build Include="dbo\Tables\ReservationCancellationPolicy.sql" />
    <Build Include="dbo\Tables\_temp_roomboard.sql" />
    <Build Include="dbo\Tables\ReservationHotelInformation.sql" />
    <Build Include="dbo\Tables\MasterProvider.sql" />
    <Build Include="dbo\Tables\ReservationMain.sql" />
    <Build Include="dbo\Tables\RepricerJobLogging.sql" />
    <Build Include="dbo\Tables\ReservationWebhookStatus.sql" />
    <Build Include="dbo\Tables\ReservationPrice.sql" />
    <Build Include="dbo\Tables\MultiSupplierReservationRoom.sql" />
    <Build Include="dbo\Tables\ReservationRoom.sql" />
    <Build Include="dbo\Tables\MultiSupplierSearchRoom.sql" />
    <Build Include="dbo\Tables\ReservationTable_Temp.sql" />
    <Build Include="dbo\Tables\MultiSupplierRoomImage.sql" />
    <Build Include="dbo\Tables\SupplierMasterExclusion.sql" />
    <Build Include="dbo\Tables\GiataRooms.sql" />
    <Build Include="dbo\Tables\ReservationTableSupplier.sql" />
    <Build Include="dbo\Tables\MultiSupplierSearchRoomImage.sql" />
    <Build Include="dbo\Tables\clientconfiguration_ExtraCriteria.sql" />
    <Build Include="dbo\Tables\SupplierSearchSync_RoomReference.sql" />
    <Build Include="dbo\Tables\test2.sql" />
    <Build Include="dbo\Tables\UsersMapping.sql" />
    <Build Include="dbo\Tables\SearchSync_Offer.sql" />
    <Build Include="dbo\Tables\SearchSync_Hotel.sql" />
    <Build Include="dbo\Tables\SearchSync_Package.sql" />
    <Build Include="dbo\Tables\SearchSync_PackageRoom.sql" />
    <Build Include="dbo\Tables\SearchSync_Room.sql" />
    <Build Include="dbo\Tables\SearchSync_RoomReference.sql" />
    <Build Include="dbo\Tables\SupplierSearchSync_Hotel.sql" />
    <Build Include="dbo\Tables\DailyOptimizationReport.sql" />
    <Build Include="dbo\Tables\SupplierSearchSync_Offer.sql" />
    <Build Include="dbo\Tables\SupplierSearchSync_Package.sql" />
    <Build Include="dbo\Tables\SupplierSearchSync_PackageRoom.sql" />
    <Build Include="dbo\Tables\SupplierSearchSync_Room.sql" />
    <Build Include="dbo\Tables\OptimizationReservationStatus.sql" />
    <Build Include="dbo\Tables\OptimizationRestriction.sql" />
    <Build Include="dbo\Tables\HotelMaster.sql" />
    <Build Include="dbo\Tables\OptimizationRecords.sql" />
    <Build Include="dbo\Tables\ExternalApi.sql" />
    <Build Include="dbo\Tables\OptimizationWarnings.sql" />
    <Build Include="dbo\Tables\CountryMaster.sql" />
    <Build Include="dbo\Tables\DryRunOptimization.sql" />
    <Build Include="dbo\Tables\Properties.sql" />
    <Build Include="dbo\Tables\OptimizationBooking.sql" />
    <Build Include="dbo\Tables\Groups.sql" />
    <Build Include="dbo\Tables\RoomDetails.sql" />
    <Build Include="dbo\Tables\RoomTypes.sql" />
    <Build Include="dbo\Tables\Reservation_ResellerInfo.sql" />
    <Build Include="dbo\Tables\BedDetails.sql" />
    <Build Include="dbo\Tables\Reservation_AgentInfo.sql" />
    <Build Include="dbo\Tables\GiataRoomDetails.sql" />
    <Build Include="dbo\Tables\tbl_vw_ResevationReports.sql" />
    <Build Include="dbo\Tables\SupplierMaster.sql" />
    <Build Include="dbo\Tables\ReservationTable.sql" />
    <Build Include="dbo\Tables\BookingActionsTaken_BKUP_20250402.sql" />
    <Build Include="dbo\Tables\ResellerMaster.sql" />
    <Build Include="dbo\Tables\MultiSupplierLogs.sql" />
    <Build Include="dbo\Tables\IrixConfiguration.sql" />
    <Build Include="dbo\Tables\ExchangeRateData_bkup_10_20250408.sql" />
    <Build Include="dbo\Tables\ReservationTablelog.sql" />
    <Build Include="dbo\Tables\BoardMapping.sql" />
    <Build Include="dbo\Views\vw_GainDueToCancellation.sql" />
    <Build Include="dbo\Views\VW_GiataRoomMapping.sql" />
    <Build Include="dbo\Views\vw_DailyProfit.sql" />
    <Build Include="dbo\Views\vw_LatestResellerInfo.sql" />
    <Build Include="dbo\Views\vw_ResevationReports.sql" />
    <Build Include="dbo\Views\vw_NewReservationDetail.sql" />
    <Build Include="dbo\Functions\fn_GetBoardGroupName.sql" />
    <Build Include="dbo\Functions\fn_GetBoardGroupNameComma.sql" />
    <Build Include="dbo\Functions\ExtractCurrencyCode.sql" />
    <Build Include="dbo\Functions\ExtractValue.sql" />
    <Build Include="dbo\Functions\UDF_GetCheckInRange.sql" />
    <Build Include="dbo\Functions\SplitString.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_Invoce.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_PreBookCriteria.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_ResellerMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_Invoice.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_NewPrebooks.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_Prebooks.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_ExchangeRatesForChangeCurrency.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_SupplierSearchSync.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_AllowedProvidersByRepricerId.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_exchangeratedata.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_prebookresultV1.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_optimizationstatuswithoutrestriction.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_PrebookResult_V2.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_dataforPricing.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_Prebooklog_V2.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_maxprofitCP.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_CreateSearch.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_prebookresult.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_Invoice_BKUP20250417.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_maxprofitPR.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_client_detail_by_name.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_SearchSync.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_prebookreservation.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_BookedActionData.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_prevoptimizationwithrestriction.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_prebooklogresult.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_multiSupplierRooms.sql" />
    <Build Include="dbo\Stored Procedures\usp_delete_prebook.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_PrebookSupplierlog_V2.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_ReservationStatus.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_multiSupplierRooms.sql" />
    <Build Include="dbo\Stored Procedures\usp_delete_client_by_name.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_BookingActionsTaken.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_currencybyRepricerId.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_optimizationstatus.sql" />
    <Build Include="dbo\Stored Procedures\usp_GetReservationIdByRePricerId.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_Prebook_Supplier_V2.sql" />
    <Build Include="dbo\Stored Procedures\usp_GenerateMultiSupplierRepricerReport.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_ReservationStatusAndConnection.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_ReservationData.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_prevoptimization.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_PrebookSupplierlog_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_prevoptimizationwithoutrestriction.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_PrebooklogsbelowThresholdSupplier_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_ReservationTable.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_reservationreport.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_InvoiceTemp.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_PrebooklogsbelowThreshold_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_ReservationMain_MarkCancel.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_Prebooklog_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_upd_ClientConfigurationExtraCriteria.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_Prebook_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_Optimization.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ReservationReportCalc.sql" />
    <Build Include="dbo\Stored Procedures\usp_delete_unnecessary_SummaryViewAggregate.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_Prebook_Supplier_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ResevationReports_V1.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_BookingActionsTaken.sql" />
    <Build Include="dbo\Stored Procedures\usp_GetPreBookData.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_edcgecasepr.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_resevationreports.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_roommapping.sql" />
    <Build Include="dbo\Stored Procedures\usp_Getdata_forreport.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_ReservationDatav1.sql" />
    <Build Include="dbo\Stored Procedures\usp_insert_exchangeratedata.sql" />
    <Build Include="dbo\Stored Procedures\TruncateAllTables.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_reservationreport_byId_MainVersion.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_RoomInfo.sql" />
    <Build Include="dbo\Stored Procedures\usp_del_TruncateAllTables.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_PreBookCriteria_V0.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_supplierAgentMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_SearchByResevationIds.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_differentSupplierRoomInfo.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ResevationReports_V1Temp.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_CancellationPolicy.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_roommapping.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_usermapping.sql" />
    <Build Include="dbo\Stored Procedures\usp_deactivate_roommapping.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_RecommendationEdgeCasePR.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_Invoice_BKUP20250228.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_PreBookCriteriaTest.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_accomdation.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_CreateSearchTest.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_reservationReport_byId.sql" />
    <Build Include="dbo\Stored Procedures\USP_GET_RECOMMENDATIONEDGECASECP.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ReservationCountByDate.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_totalbookingCount.sql" />
    <Build Include="dbo\Stored Procedures\usp_Ins_Prebook_V2.sql" />
    <Build Include="dbo\Stored Procedures\usp_insert_repricerjoblogging.sql" />
    <Build Include="dbo\Stored Procedures\KillIdleSessions.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_MultiSupplierRoomImage.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_MultiSupplierReservationRoom.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_MultiSupplierSearchRoom.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_MultiSupplierRoomDetailsByRepricerId.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_bulkgiataroom.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_ReservationWebhookStatus.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_repricerjoblogging.sql" />
    <Build Include="dbo\Stored Procedures\usp_GetSupplierCountByRepricerId.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_DailyOptimizationReport.sql" />
    <Build Include="dbo\Stored Procedures\usp_Insert_DailyOptimizationReport.sql" />
    <Build Include="dbo\Stored Procedures\usp_getExternalApiByService.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_newbookingreservation.sql" />
    <Build Include="dbo\Stored Procedures\usp_InsertPreBookClientConfiguration.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_optimizedbooking.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_dryrunoptimization.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_hotelMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_insert_InsertPreBookData.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_countryMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_InsertPropertyData.sql" />
    <Build Include="dbo\Stored Procedures\usp_insert_client_detail.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_CreateSearch_V0.sql" />
    <Build Include="dbo\Stored Procedures\usp_delete_logging.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_searchcriteria.sql" />
    <Build Include="dbo\Stored Procedures\usp_GiataInsertRoomDetails.sql" />
    <Build Include="dbo\Stored Procedures\usp_GetRoomDetails.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_CancellationPolicy_ForTesting.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_cancelledReservationById.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_reservationreport_bkup_20241018.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_upd_tbl_vw_ResevationReports.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_usermapping.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ReportData_V3.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_CancellationPolicyV0.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ReportData_SupplierV3.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_ResellerMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_Get_PrebooksEmailContent.sql" />
    <Build Include="dbo\Stored Procedures\InsertOrUpdateMultiSupplierLog.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_SupplierMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_MultiSupplierLog.sql" />
    <Build Include="dbo\Stored Procedures\usp_ins_configuration.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_SupplierMaster.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_dataforPricingV1.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_realizedgain.sql" />
    <Build Include="dbo\Stored Procedures\GetAllBoardMappings.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_maxprofit.sql" />
    <Build Include="dbo\User Defined Types\ReservationIdList.sql" />
    <Build Include="dbo\User Defined Types\RestrictionType.sql" />
    <Build Include="dbo\User Defined Types\WarningType.sql" />
    <Build Include="dbo\User Defined Types\ImageListType.sql" />
    <Build Include="dbo\User Defined Types\GiataRoomsType.sql" />
    <Build Include="dbo\User Defined Types\HotelMaster.sql" />
    <Build Include="dbo\User Defined Types\CountryMaster.sql" />
    <Build Include="dbo\Tables\ReservationReportDetailsAdditionalPrebook_1.sql" />
    <Build Include="dbo\Stored Procedures\usp_upd_reservationreport_AdditionalPrebook_1.sql" />
    <Build Include="dbo\Stored Procedures\usp_get_AdditionalPrebookOptions_1.sql" />
  </ItemGroup>
</Project>