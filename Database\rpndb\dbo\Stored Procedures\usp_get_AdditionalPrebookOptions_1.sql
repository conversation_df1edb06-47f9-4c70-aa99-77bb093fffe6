﻿
--/*
CREATE PROCEDURE [dbo].[usp_get_AdditionalPrebookOptions]
    @RepricerId INT = null,
    @ReservationId VARCHAR(MAX) = NULL,
    @FromDate VARCHAR(20) = NULL,
    @ToDate VARCHAR(20) = NULL
AS
--*/
/*
-- exec [dbo].[usp_get_AdditionalPrebookOptions] null

Declare @RepricerId INT= NULL,
    @ReservationId VARCHAR(MAX) = NULL,
    @FromDate VARCHAR(20) = NULL,
    @ToDate VARCHAR(20) = NULL
--*/
BEGIN  
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;  
    SET NOCOUNT ON;  

    declare @currentDate DATE = GETUTCDATE();  
	DECLARE @fromDateVar DATE;
	DECLARE @ToDateVar DATE;

	-- Parse date parameters
	IF @FromDate IS NOT NULL AND @FromDate != ''
	BEGIN
	SET @fromDateVar = CAST(@FromDate AS DATE);
	END

	IF @ToDate IS NOT NULL AND @ToDate != ''
	BEGIN
	SET @ToDateVar = CAST(@ToDate AS DATE);
	END

	-- Create temp table for reservation IDs if provided
	CREATE TABLE #TempReservationIds (ReservationId INT);

	IF @ReservationId IS NOT NULL AND @ReservationId != ''
	BEGIN
	INSERT INTO #TempReservationIds (ReservationId)
	SELECT CAST(value AS INT)
	FROM STRING_SPLIT(@ReservationId, ',')
	WHERE value != '';
	END
  
    ;WITH Variables  
    AS (select GETUTCDATE() AS CurrentUTCDate)  
        , LatestBookingUpdate  
    AS (Select rrd1.repricerid  
             , cast(dateadd(day, -1, v.CurrentUTCDate) AS DATE) AS Createdate  
             , v.CurrentUTCDate  
        FROM dbo.ReservationReportDetailsAdditionalPrebook rrd1  
            cross apply Variables         AS v  
        WHERE (  
                  rrd1.RepricerId = @RepricerId  
                  or @RepricerId is null  
              )  
        group by rrd1.repricerid  
               , v.CurrentUTCDate  
       )  
        , cte_MainRes  
    as (Select rm.RePricerId  
             , rm.ReservationId  
             , rm.lastActivity  
             , rm.CancelledDate  
             , rm.ReservationStatus  
             , rm.ConnectionStatus  
             , rm.ConnectionDescription  
             , rm.PaymentType  
             , rm.cpJSON  
        from dbo.[ReservationMain]                  rm  
            INNER JOIN dbo.ReservationReportDetailsAdditionalPrebook rrd  
                ON rm.repricerid = rrd.repricerid  
                   AND rm.reservationid = rrd.reservationid  
       )  
        , cte_NewRes  
    as (Select rm.RePricerId  
             , rm.ReservationId  
             , rm.lastActivity  
             , rm.CancelledDate  
             , rm.ReservationStatus  
             , rm.ConnectionStatus  
             , rm.ConnectionDescription  
             , rm.PaymentType  
             , rm.cpJSON  
             , bat.NewBookingId  
        from dbo.[ReservationMain]             rm  
            INNER JOIN dbo.bookingactionstaken bat  
                ON rm.repricerid = bat.repricerid  
                   AND rm.reservationid = bat.NewBookingId  
                   AND bat.ActionId = 1  
       )  
	   , cte_rooms as 
		(
			SELECT
				RePricerID,
				Reservationid,
				roomid,
				TRIM(roomname) as roomname 
				, Count(DISTINCT roomid) as roomid_count
			FROM dbo.ReservationRoom
			GROUP BY RePricerID, Reservationid, roomid,roomname
		)
        , RRDSimplified  
    AS (Select DISTINCT  
            rrdCalc.prebookTableId  
          , rrdCalc.RepricerId  
          , rrdCalc.ReservationId  
          , ISNULL(  
                      relaized.NewBookingPrice  
                    , (CAST(rrdCalc.reservationprice AS DECIMAL(18, 2))  
                       - CAST(ISNULL(rrdCalc.prebookprice, 0.0) AS DECIMAL(18, 2))  
                       - (case  
                              when (  
                                       rrdCalc.matchedreservationcancellationdate < cast(DATEADD(  
                                                                                                    day  
                                                                                                  , 1  
                                                                                                  , v.CurrentUTCDate  
                                                                                                ) AS DATE)  
                                       OR rrdCalc.checkin < cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE)  
                                   )  
                                   and ISNULL(relaized.ActionId, 0) <> 1 then  
                                  cast(ISNULL(rrdCalc.MatchedReservationCancellationChargeByPolicyToEur, 0.0) AS decimal)  
                              else  
                                  0.0  
                          end  
                         )  
                      )  
                  )                                 AS CalcProfitAfterCancellation  
          , case  
                when (case  
                          when CAST(rrdCalc.Createdate AS DATE) < CAST(v.CurrentUTCDate AS DATE)  
                               AND ISNULL(relaized.ActionId, 0) <> 1  
                               AND CAST(rrdCalc.Createdate AS DATE) >= cast(dateadd(day, -1, v.CurrentUTCDate) AS DATE) then  
                              dateadd(SECOND, 5, dateadd(minute, 221, CAST(CAST(v.CurrentUTCDate AS DATE) AS DATETIME)))  
                          else  
                              rrdCalc.Createdate  
                      END  
                     ) < relaized.createdOn then  
                    relaized.createdOn  
                else  
          (case  
               when CAST(rrdCalc.Createdate AS DATE) < CAST(v.CurrentUTCDate AS DATE)  
                    AND ISNULL(relaized.ActionId, 0) <> 1  
                    AND CAST(rrdCalc.Createdate AS DATE) >= cast(dateadd(day, -1, v.CurrentUTCDate) AS DATE) then  
                   -- dateadd(SECOND, 5, dateadd(minute, 221, CAST(CAST(v.CurrentUTCDate AS DATE) AS DATETIME)))    
                   rrdCalc.Createdate  
               else  
                   rrdCalc.Createdate  
           END  
          )  
            end                                                                                                           AS Createdate  
          , LEFT(rrdCalc.ReservationGiataMappingId, CASE  
                                                        WHEN CHARINDEX(',', rrdCalc.ReservationGiataMappingId) > 0 THEN  
                                                            CHARINDEX(',', rrdCalc.ReservationGiataMappingId) - 1  
                                                        ELSE  
                                                            LEN(rrdCalc.ReservationGiataMappingId)  
                                                    END)                                                                  AS ReservationGiataMappingId  
          , rrdCalc.ReservationGiataPropertyName                                                                          AS ReservationGiataPropertyName  
          , LEFT(rrdCalc.SearchGiataMappingId, CASE  
                                                   WHEN CHARINDEX(',', rrdCalc.SearchGiataMappingId) > 0 THEN  
                                                       CHARINDEX(',', rrdCalc.SearchGiataMappingId) - 1  
                                                   ELSE  
                                                       LEN(rrdCalc.SearchGiataMappingId)  
                                               END)                                                                       AS SearchGiataMappingId  
          , rrdCalc.PreBookGiataPropertyName                                                                              AS SearchGiataPropertyName  
          , trim(replace(replace(rrdCalc.ReservationRoomBoard, ', , ', ''), ', ', ''))                                    AS ReservationRoomBoard  
          , trim(replace(replace(rrdCalc.PrebookRoomBoard, ', , ', ''), ', ', ''))                                        AS PrebookRoomBoard  
          , trim(replace(  
                            replace(ISNULL(rrdCalc.ReservationRoomBoardGroup, rrdCalc.ReservationRoomBoard), ', , ', '')  
                          , ', '  
                          , ''  
                        )  
                )                                                                                                         AS ReservationRoomBoardGroup  
          , trim(replace(replace(ISNULL(rrdCalc.PrebookRoomBoardGroup, rrdCalc.PrebookRoomBoard), ', , ', ''), ', ', '')) AS PrebookRoomBoardGroup  
          , rrdCalc.ReservationCancellationType                                                                           AS ReservationCancellationType  
          , rrdCalc.PreBookCancellationType                                                                               AS PreBookCancellationType  
          , rrdCalc.CancellationPolicyRemark                                                                              AS CancellationPolicyRemark  
          , trim(replace(replace(rrdCalc.Providers, ', , ', ''), ', ', ''))                                               AS Providers  
          , trim(replace(replace(rrdCalc.prebooksupplier, ', , ', ''), ', ', ''))                                         AS prebooksupplier  
          , rrdCalc.[ResellerName]                                                                                        AS [ResellerName]  
          , rrdCalc.[ResellerCode]                                                                                        AS [ResellerCode]  
          , rrdCalc.[ResellerType]                                                                                        AS [ResellerType]  
        From dbo.ReservationReportDetailsAdditionalPrebook     AS rrdCalc  
            Left join dbo.BookingActionsTaken AS relaized  
                on relaized.RepricerId = rrdCalc.RepricerId  
                   and relaized.ReservationId = rrdCalc.ReservationId  
                   and relaized.ActionId = 1  
            cross apply Variables             AS v  
       )  
        , ctemain  
    as (SELECT DISTINCT  
            rrd.ReservationId  
          , rrd.RepricerId  
          , Case  
                when ISNULL(rb.ActionId, 0) = 1 THEN  
                    'Optimized'  
                ELSE  
                    CASE  
                        WHEN --rt1.id > 0         AND     
          rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
          AND (  
                  rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                  or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
              )  
          AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                     day  
                                                                                   , cce.traveldaysminsearchindays  
                                                                                   , (v.CurrentUTCDate)  
                                                                                 ) AS DATE)  
          AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                 day  
                                                                               , cce.traveldaysminsearchindays  
                                                                               , (v.CurrentUTCDate)  
                                                                             ) AS DATE)  
          AND rrd.MatchedPrebookCancellationDate >= rrd.MatchedReservationCancellationDate  
          AND ISNULL(rrd.cpstatus, '') = 'loose'  
          AND rrd.Profit > 5.0  
          AND 1 = (case  
                       when ISNULL(cce.IsUsePercentage, 0) = 1  
                            AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) >= cce.priceDifferencePercentage Then  
                           1  
                       ELSE  
                           case  
                               when ISNULL(cce.IsUsePercentage, 0) = 0  
                                    AND rrd.Profit * ISNULL(ER.Factor, 1) >= cce.priceDifferenceValue then  
                                   1  
                               ELSE  
                                   0  
                           END  
                   END  
                  )  
          AND rm.ReservationStatus = 'OK'  
          AND ISNULL(   CASE  
                            WHEN rb.NewBookingPrice > 0 then  
                                1  
                            ELSE  
                                0  
                        END  
                      , 0  
                    ) = 0 
		  AND newBat.NewBookingId is null
		  and 1 = (case when cce.optimizationType = 3 then case when batDryRun.ReservationId is not null then 1 else 0 end else 1 end) 
          AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'Prebook'  
                        WHEN rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
                             AND (  
                                     rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                                     or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
                                 )  
                             AND (  
                                     rrd.cpstatus = 'tightWithBuffer'  
                                     or (  
                                            rrd.cpstatus = 'loose'  
                                            and rrd.MatchedReservationCancellationDate > rrd.MatchedPrebookCancellationDate  
                                        )  
                                 )  
                             AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                                        day  
                                                                                                      , cce.traveldaysminsearchindays  
                                                                                                      , (v.CurrentUTCDate)  
                                                                                                    ) AS DATE)  
                             AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                                    day  
                                                                                                  , cce.traveldaysminsearchindays  
                                                                                                  , (v.CurrentUTCDate)  
                                                                                                ) AS DATE)  
                             AND rrd.Profit > 5.0  
                             AND 1 = (case  
                                          when ISNULL(cce.IsUsePercentage, 0) = 1  
                                               AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) >= cce.priceDifferencePercentage Then  
                                              1  
                                          ELSE  
                                              case  
                                                  when ISNULL(cce.IsUsePercentage, 0) = 0  
                                                       AND rrd.Profit * ISNULL(ER.Factor, 1) >= cce.priceDifferenceValue then  
                                                      1  
                                                  ELSE  
                                                      0  
                                              END  
                                      END  
                                     )  
                             AND rm.ReservationStatus = 'OK'  
                             AND CAST(ISnull(rrd.CPDaysGain, 0) AS INT) >= (-1  
                                                                            * CAST(cce.DaysLimitCancellationPolicyEdgeCase AS INT)  
                                                                           )  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
								AND newBat.NewBookingId is null
								-- and 1 = (case when cce.optimizationType = 3 then case when batDryRun.ReservationId is not null then 1 else 0 end else 1 end) 
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'CancellationEdgeCase'  
                        WHEN rrd.cpstatus = 'CancellationChargesApplicable'  
                             AND (  
                                  rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                                     or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
                                 )  
                             AND rrd.MatchedReservationCancellationDate > v.CurrentUTCDate  
                             AND rrd.Profit > 5.0  
                             AND 1 = (case  
                                          when ISNULL(cce.IsUsePercentage, 0) = 1  
                                               AND Round(  
                                                            ((rrds.CalcProfitAfterCancellation / rrd.ReservationPrice)  
                                                             * 100  
                                                            )  
                                                          , 2  
                                                        ) >= cce.priceDifferencePercentage Then  
                                              1  
                                          ELSE  
                                              case  
                                                  when ISNULL(cce.IsUsePercentage, 0) = 0  
                                                       AND rrds.CalcProfitAfterCancellation >= cce.priceDifferenceValue then  
                                                      1  
                                                  ELSE  
                                                      0  
                                              END  
                                      END  
                                     )  
                             AND rm.ReservationStatus = 'OK'  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
							AND newBat.NewBookingId is null
							-- and 1 = (case when cce.optimizationType = 3 then case when batDryRun.ReservationId is not null then 1 else 0 end else 1 end) 
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'CancellationChargesApplicable'  
                        WHEN rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
                             AND (  
                                     rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                                     or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
                                 )  
                             AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                                        day  
                                                                                                      , cce.traveldaysminsearchindays  
                                                                                                      , (v.CurrentUTCDate)  
                                                                                                    ) AS DATE)  
                             AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                                    day  
                                                                                                  , cce.traveldaysminsearchindays  
                                                                                                  , (v.CurrentUTCDate)  
                                                                                                ) AS DATE)  
                             AND rrd.cpstatus = 'tight'  
                             AND rrd.Profit > 5.0  
                             AND 1 = (case  
                                          when ISNULL(cce.IsUsePercentage, 0) = 1  
                                               AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) >= cce.priceDifferencePercentage Then  
                                              1  
                                          ELSE  
                                              case  
                                                  when ISNULL(cce.IsUsePercentage, 0) = 0  
                                                       AND rrd.Profit * ISNULL(ER.Factor, 1) >= cce.priceDifferenceValue then  
                                                      1  
                                                  ELSE  
                                                      0  
                                              END  
                                      END  
                                     )  
                             AND rm.ReservationStatus = 'OK'  
                             AND CAST(ISnull(rrd.CPDaysGain, 0) AS INT) >= (-1  
                                                                            * CAST(cce.DaysLimitCancellationPolicyEdgeCase AS INT)  
                                                                           )  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
							AND newBat.NewBookingId is null
							-- and 1 = (case when cce.optimizationType = 3 then case when batDryRun.ReservationId is not null then 1 else 0 end else 1 end) 
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'CancellationAmountCase'  
                        WHEN (  
                                 rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                                 or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
                             )  
                             AND rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
                             AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                                        day  
                                                                                                      , cce.traveldaysminsearchindays  
                                                                                                      , (v.CurrentUTCDate)  
                                                                                                    ) AS DATE)  
                             AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                                    day  
                                                                                                  , cce.traveldaysminsearchindays  
                                                                                                  , (v.CurrentUTCDate)  
                                                                                                ) AS DATE)  
                             -- AND ISNULL(rrd.cpstatus,rt.cPStatus) = 'loose'     
                             AND rrd.MatchedPrebookCancellationDate > rrd.MatchedReservationCancellationDate  
                             AND rrd.Profit > -1.0  
                             AND 1 = (case  
                                          when ISNULL(cce.IsUsePercentage, 0) = 1  
                                               AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) < cce.priceDifferencePercentage Then  
                                              1  
                                          ELSE  
                                              case  
                                                  when ISNULL(cce.IsUsePercentage, 0) = 0  
                                                       AND rrd.Profit < cce.priceDifferenceValue then  
                                                      1  
                                                  ELSE  
                                                      0  
                                              END  
                                      END  
                                     )  
                             AND rm.ReservationStatus = 'OK'  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
								AND newBat.NewBookingId is null
								-- and 1 = (case when cce.optimizationType = 3 then case when batDryRun.ReservationId is not null then 1 else 0 end else 1 end) 
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'NoOrLessGainButBetterCancellation'  
                        WHEN rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
                             AND (  
                                     rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                                     or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
                                 )  
                             AND rrd.Profit > 5.0  
                             AND 1 = (case  
                                          when ISNULL(cce.IsUsePercentage, 0) = 1  
                                               AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) < cce.priceDifferencePercentage Then  
                                              1  
                                          ELSE  
                                              case  
                                                  when ISNULL(cce.IsUsePercentage, 0) = 0  
                                                       AND rrd.Profit < cce.priceDifferenceValue then  
                                                      1  
                                                  ELSE  
                                                      0  
                                              END  
                                      END  
                                     )  
                             AND rm.ReservationStatus = 'OK'  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
                             AND CAST(ISnull(rrd.CPDaysGain, 0) AS INT) >= (-1  
                                                                            * CAST(cce.DaysLimitCancellationPolicyEdgeCase AS INT)  
                                                                           )  
                             AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                                        day  
                                                                                                      , cce.traveldaysminsearchindays  
                                                                                                      , (v.CurrentUTCDate)  
                                                                                                    ) AS DATE)  
                             AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                                    day  
                                                  , cce.traveldaysminsearchindays  
                                                                                                  , (v.CurrentUTCDate)  
                                                                                                ) AS DATE) 
							AND newBat.NewBookingId is null	
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'PriceEdgeCase'  
                        WHEN rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
                             AND rrds.Providers <> rrds.prebooksupplier  
                             --  AND (rrd.ReservationGiataMappingId > 0 and rrd.SearchGiataMappingId > 0)    
                             AND (  
                                     LEN(rrd.ReservationGiataMappingId) > 0  
                                     AND LEN(rrd.SearchGiataMappingId) > 0  
                                 )  
                             AND (  
                                     rrds.ReservationRoomBoardGroup = rrds.prebookRoomBoardGroup  
                                     or rrds.ReservationRoomBoard = rrds.prebookRoomBoard  
                                 )  
                             AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                                        day  
                                                                                                      , cce.traveldaysminsearchindays  
                                                                                                      , (v.CurrentUTCDate)  
                                                                                                    ) AS DATE)  
                             AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                                    day  
                                                                                                  , cce.traveldaysminsearchindays  
                                                                                                  , (v.CurrentUTCDate)  
                                                                                                ) AS DATE)  
                             AND rrd.cpstatus = 'loose'  
                             AND rrd.Profit > 5.0  
                             AND 1 = (case  
                                          when ISNULL(cce.IsUsePercentage, 0) = 1  
                                               AND Round(((rrd.Profit / rrd.ReservationPrice) * 100), 2) >= cce.priceDifferencePercentage Then  
                                              1  
                                          ELSE  
                                              case  
                                                  when ISNULL(cce.IsUsePercentage, 0) = 0  
                                                       AND rrd.Profit * ISNULL(ER.Factor, 1) >= cce.priceDifferenceValue then  
                                                      1  
                                                  ELSE  
                                                      0  
                                              END  
                                      END  
                                     )  
                             AND rm.ReservationStatus = 'OK'  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
							AND newBat.NewBookingId is null
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'UsingRoomMapping'  
                        WHEN rrd.checkin > cast((v.CurrentUTCDate) AS DATE)  
                             AND (rrds.ReservationRoomBoardGroup <> rrds.prebookRoomBoardGroup)  
                             AND rrd.Profit > 5.0  
                             AND rm.ReservationStatus = 'OK'  
                             AND ISNULL(   CASE  
                                               WHEN rb.NewBookingPrice > 0 then  
                                                   1  
                                               ELSE  
                                                   0  
                                           END  
                                         , 0  
                                       ) = 0  
                             AND CAST(ISnull(rrd.CPDaysGain, 0) AS INT) >= (-1  
                                                                            * CAST(cce.DaysLimitCancellationPolicyEdgeCase AS INT)  
                                                                           )  
                             AND cast(rrd.matchedreservationcancellationdate AS DATE) > cast(DATEADD(  
                                                                                                        day  
                                                                                                      , cce.traveldaysminsearchindays  
                                                                                                      , (v.CurrentUTCDate)  
                                                                                                    ) AS DATE)  
                             AND cast(rrd.matchedPrebookCancellationDate AS DATE) > cast(DATEADD(  
                                                                                                    day  
                                                                                                  , cce.traveldaysminsearchindays  
                                                                                                  , (v.CurrentUTCDate)  
                                                                                                ) AS DATE)
							AND newBat.NewBookingId is null																								
                             AND CAST(rrds.CreateDate AS DATE) >= CAST(lastUpdated.Createdate AS DATE) THEN  
                            'UsingRoomMapping1'  
                        ELSE  
                            'Other'  
                    END  
            END                                                                           AS ReportType  
          , rrd.ReservationGiataMappingId  
          , rrd.SearchGiataMappingId  
          , rrd.cpstatus                                                                  AS CPStatus  
          , CAST(ISnull(rrd.CPDaysGain, 0) AS INT)                                        AS CPDaysGain  
          , CAST(rrd.matchedcancellationpolicygain AS DECIMAL(18, 2))                     AS MatchedCancellationPolicyGain  
          , rm.ReservationStatus  
          , ISNULL(rb.NewBookingId, 0)                                                    AS NewReservationID  
          , case  
                when ISNULL(rb.ActionId, 0) = 1 then  
                    COALESCE(newRes.ReservationStatus, rm.ReservationStatus, 'OK')  
                else  
                    null  
            end                                                                           AS NewReservationStatus  
          , CAST(rrd.reservationprice AS DECIMAL(18, 2))                                  AS ReservationPrice  
          , CAST(ISNULL(rrd.prebookprice, 0.0) AS DECIMAL(18, 2))                         AS PrebookPrice  
          , CAST(CASE  
                     WHEN ISNULL(rb.NewBookingPrice, 0.0) > 0 THEN  
                         ISNULL(rb.NewBookingPrice, 0.0)  
                     ELSE  
                         ISNULL(rrd.Profit, 0.0)  
                 END AS DECIMAL(18, 2))                                                   AS Profit  
          , rrds.CalcProfitAfterCancellation                                              AS ProfitAfterCancellation  
          , CAST(ROUND(   (CAST(CASE  
                                    WHEN ISNULL(rb.NewBookingPrice, 0.0) > 0 THEN  
                                        ISNULL(rb.NewBookingPrice, 0.0)  
                                    ELSE  
                                        ISNULL(rrd.Profit, 0.0)  
                                END AS DECIMAL(18, 2)) / rrd.reservationPrice  
                          ) * 100  
                        , 2  
                      ) AS decimal(10, 2))                                                AS ProfitPercentage  
          , CAST(rrd.MatchedReservationCancellationChargeByPolicyToEur AS DECIMAL(18, 2)) AS MatchedReservationCancellationChargeByPolicy  
          , CAST(rrd.MatchedPreBookCancellationChargeByPolicytoEur AS DECIMAL(18, 2))     AS MatchedPrebookCancellationChargeByPolicy  
          , rrd.MatchedReservationCancellationDate  
          , rrd.MatchedPrebookCancellationDate  
          , CASE  
                WHEN rrd.matchedreservationcancellationdate > cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE)  
                     and rrd.checkin > cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE) then  
                    1  
                ELSE  
                    0  
            END                                                                           AS IsFutureDate_RES_CP  
          , CASE  
                WHEN rrd.matchedprebookcancellationdate > cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE)  
                     and rrd.checkin > cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE) then  
                    1  
                ELSE  
                    0  
            END                                                                           AS IsFutureDate_PRE_CP  
          , CASE  
                WHEN rrd.matchedreservationcancellationdate > cast(DATEADD(  
                                                                              day  
                                                                            , cce.traveldaysminsearchindays  
                                                                            , (v.CurrentUTCDate)  
                                                                          ) AS DATE)  
                     and rrd.checkin > cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE) then  
                    1  
                ELSE  
                    0  
            END                                                                           AS IsWithInBuffer_RES_CP  
          , CASE  
                WHEN rrd.matchedprebookcancellationdate > cast(DATEADD(  
                                                                          day  
                                                                        , cce.traveldaysminsearchindays  
                                                                        , (v.CurrentUTCDate)  
                                                                      ) AS DATE)  
                     and rrd.checkin > cast(DATEADD(day, 1, v.CurrentUTCDate) AS DATE) then  
                    1  
                ELSE  
                    0  
            END                                                                           AS IsWithInBuffer_PRE_CP  
          , rrd.BookingDate  
          , rrd.CheckIn                                                                   AS ReservationCheckin  
          , rrd.CheckOut                                                                  AS ReservationCheckout  
          , rrd.CreateDate                                                                AS FirstPreBookCreateDate  
          , rrds.CreateDate                                                               AS Createdate  
          , CASE  
                WHEN rrd.updatedOn < CAST(v.CurrentUTCDate AS DATE)  
                     AND ISNULL(rb.ActionId, 0) <> 1 then  
                    dateadd(SECOND, 5, dateadd(minute, 221, CAST(CAST(v.CurrentUTCDate AS DATE) AS DATETIME)))  
                ELSE  
                    rrds.Createdate  
            END                                                                           AS PrebookLastUpdateDate  
          , rrd.ReservationAdultCount  
          , rrd.PrebookAdultCount  
          , rrd.ReservationChildAges  
          , rrd.PrebookChildAges  
          , rrd.Providers                                                                 AS ReservationSupplier  
          , ISNULL(rrd.prebooksupplier, rrd.providers)                                    AS PrebookSupplier  
          , rrd.CurrencyfactortoEUR  
          , rrd.ReservationRoomName  
          , ISNULL(newRooms.newRoomNames, rrd.PrebookRoomName)							  as PrebookRoomName  
          , rrd.ReservationRoomBoard  
          , case  
                when rb.ActionId = 1  
                     AND rrd.PrebookRoomBoard <> rrd.ReservationRoomBoard then  
                    rrd.ReservationRoomBoard  
                else  
                    rrd.PrebookRoomBoard  
            end                                                                           AS PrebookRoomBoard  
          , case  
                when rb.ActionId = 1 then  
                    ''  
                else  
                    rrds.ReservationRoomBoardGroup  
            end                                                                           AS ReservationRoomBoardGroup  
          , case  
                when rb.ActionId = 1 then  
                    ''  
                else  
                    rrds.PrebookRoomBoardGroup  
            end                                                                           AS PrebookRoomBoardGroup  
          --, rrd.ReservationRoomInfo  AS SavedReservationRoomInfo        
          --, rrd.PrebookRoomInfo      AS SavedPrebookRoomInfo    
          , trim(replace(replace(rrd.ReservationRoomInfo, ', , ', ''), ', ', ''))         AS ReservationRoomInfo  
          , trim(replace(replace(rrd.PrebookRoomInfo, ', , ', ''), ', ', ''))             AS PrebookRoomInfo  
          , rrd.PrebookRoomIndex  
          , rrd.IsCancellationPolicyMatched  
          , rrd.RoomType  
          , ''                                                                            AS Token  
          , ''                                                                            AS Availabilitytoken  
          , rrd.Numberofrooms  
          , ISNULL(rrd.reservationdestination, '')                                        AS ReservationDestination  
          , ISNULL(rrd.prebookdestination, '')                                            AS PrebookDestination  
          , rrd.reservationhotelname                                                      AS ReservationHotelName  
          , rrd.prebookhotelname                                                          AS PrebookHotelName  
          , CASE  
                WHEN rb.ActionId IS NOT NULL  
                     OR rb.ActionId > 1 THEN  
                    1  
                ELSE  
                    0  
            END                                                                           AS IsBookingActionTaken  
          , COALESCE(rb.CreatedOn, batDryRun.CreatedOn)									  AS LastActionTakenDate
		  , COALESCE(rb.ActionId, batDryRun.ActionId)									  AS ActionId
          , CAST(ISNULL(rb.NewBookingPrice, 0.0) AS DECIMAL(18, 2))                       AS ActionTakenGain  
          , 'EUR'                                                                         AS PreBookCurrency  
          , 'EUR'                                                                         AS ReservationCurrency  
          , rrds.prebookTableId                                                           AS prebookTableId  
          , DATEDIFF(day, rrd.updatedon, (v.CurrentUTCDate))                              AS DiffDayFromLastUpdate  
          , 1                                                                             AS IsLivePreBook  
          , null     AS ReservationStatus_For_IsLivePreBook  
          , cce.ClientConfig_DaysDifferenceInPreBookCreation                              AS DiffDays_Optimisation  
          , CAST(cce.priceDifferenceValue AS DECIMAL(18, 2))                              AS PriceDifferenceValue  
          , cce.PriceDifferencePercentage  
          , cce.PriceDifferenceCurrency  
          , cce.IsUsePercentage  
          , cce.TravelDaysMaxSearchInDays  
          , cce.TravelDaysMinSearchInDays  
          , (-1 * CAST(cce.DaysLimitCancellationPolicyEdgeCase AS INT))                   AS DaysLimitCancellationPolicyEdgeCase  
          , cast(rrd.matchedreservationcancellationdate AS DATE)                          AS CPDate  
          , cast(DATEADD(day, cce.traveldaysminsearchindays, (v.CurrentUTCDate)) AS DATE) AS CPBufferDate  
          , isnull(rrds.ReservationCancellationType, 'standard')                          AS ReservationCancellationType  
          , isnull(rrds.PreBookCancellationType, 'standard')                              AS PreBookCancellationType  
          , isnull(rrds.CancellationPolicyRemark, '')                                     AS CancellationPolicyRemark  
          , isnull(rm.ConnectionStatus, 0)                                                AS ConnectionStatus  
          , isnull(rm.ConnectionDescription, '')                                          AS ConnectionStatusDescription  
          , rrds.ReservationGiataPropertyName  
          , rrds.SearchGiataPropertyName  
          , rm.lastActivity                                                               AS ReservationLastActivity  
          , ISNULL(newRes.lastActivity, rm.lastActivity)                                  AS PrebookLastActivity  
          , case  
                when rm.ReservationStatus = 'XX' then  
                    rm.lastActivity  
                else  
                    null  
            end                                                                           AS ReservationCancelledOnDate  
          , case  
                when newRes.ReservationStatus = 'XX' then  
                    newRes.lastActivity  
                else  
                    null  
            end                                                                           AS PrebookCancelledOnDate  
          , ER.FromCurrency  
          , ER.ToCurrency  
          , ISNULL(ER.Factor, 1)                                                          Factor  
          , rrd.Profit * ISNULL(ER.Factor, 1)                                             AS ProfitInOriginalCurrency  
          , rrds.[ResellerName]  
          , rrds.[ResellerCode]  
          , rrds.[ResellerType]  
          , rrd.updatedon                                                                 AS rrd_updatedDate  
          , rm.cpJSON                                                                     AS reservationCpJSON  
          , newRes.cpJSON                                                                 AS prebookCpJSON  
        FROM dbo.ReservationReportDetailsAdditionalPrebook                    AS rrd  
            INNER JOIN cte_MainRes                           AS rm  
                ON rrd.repricerid = rm.repricerid  
                   AND rm.ReservationID = rrd.ReservationID  
            INNER JOIN dbo.clientconfiguration_ExtraCriteria AS cce  
                ON rrd.repricerid = cce.repricerid  
            INNER JOIN LatestBookingUpdate                   AS lastUpdated  
                on lastUpdated.repricerid = rrd.repricerid  
            CROSS APPLY Variables                            AS v  
            LEFT JOIN RRDSimplified            AS rrds  
                ON rrd.reservationid = rrds.ReservationId  
                   AND rrd.repricerid = rrds.RePricerId  
            --LEFT JOIN MaxReservationTable     rt1    
            -- ON rrd.reservationid = rt1.ReservationId    
            --AND rrd.repricerid = rt1.RePricerId    
            --LEFT JOIN dbo.reservationtable    rt    
            -- ON rrd.reservationid = rt.ReservationId    
            --    AND rrd.repricerid = rt.RePricerId    
            --    AND rt.id = rt1.id    
  
            LEFT JOIN dbo.bookingactionstaken  AS rb  
                ON rb.reservationid = rrd.reservationid  
                   AND rb.repricerid = rrd.repricerid  
                   AND rb.ActionId = 1  
            left join cte_NewRES               AS newRes  
                on rb.RepricerId = newRes.RePricerId  
                   and rb.NewBookingId = newRes.ReservationId  
            left join [dbo].[ExchangeRateData] AS ER  
                on rrd.repricerid = ER.RepricerID  
                   and ER.repricerid = cce.repricerid  
                   and ER.FromCurrency = rrd.pricedifferencecurrency  
                   and ER.ToCurrency = 'EUR'  
                   and cast(ER.CurrentDate AS date) = cast(rrd.BookingDate AS date)  
			LEFT JOIN dbo.bookingactionstaken  AS newBat
            ON rm.reservationid = newBat.NewBookingId
               AND rm.repricerid = newBat.repricerid
               AND newBat.ActionId = 1
			LEFT JOIN dbo.bookingactionstaken  AS batDryRun
            ON rm.reservationid = batDryRun.reservationid
               AND rm.repricerid = batDryRun.repricerid
               AND batDryRun.ActionId = 8
			   AND batDryRun.ExtraData = 'DryRunOptimizationSucess_ExpectedGainAboveThreshold'
			LEFT JOIN (
				SELECT
					RePricerID,
					Reservationid,
					STRING_AGG(CAST(roomid AS VARCHAR), '## ') 
						WITHIN GROUP (ORDER BY roomname) AS newRoomIds,
					STRING_AGG(TRIM(roomname), '## ') 
						WITHIN GROUP (ORDER BY roomname) AS newRoomNames
				FROM cte_rooms
				GROUP BY RePricerID, Reservationid
			) AS newRooms
				ON newRes.RePricerID = newRooms.RePricerID
				AND newRes.ReservationId = newRooms.Reservationid
        WHERE (  
                  rrd.isActive = 1  
                  AND (  
                          1 = 1  
                          and rrd.checkin IS NOT NULL  
                          AND CAST(ISnull(rrd.CPDaysGain, 0) AS INT) >= (-1  
                                                                         * CAST(cce.DaysLimitCancellationPolicyEdgeCase AS INT)  
                                                                        )  
                          AND rm.ReservationStatus = 'OK'  
                          AND CAST(rrd.Createdate AS DATE) >= CAST(lastUpdated.Createdate AS DATE)  
                          AND isnull(rm.ConnectionStatus, 0) = 0  
                          AND rrd.CPStatus is not null  
                          AND rrd.Profit > 1  
                          AND (  
                                  rrd.ReservationGiataMappingId <> 276834  
                                  or rrd.SearchGiataMappingId <> 276834  
                              )  
                      )  
                  AND (ISNULL(rb.ActionId, 0) = 0)  
              )  
       )  
    
    Select V.ReservationId  
         , v.RepricerId  
         , v.ReportType  
         , v.ReservationGiataMappingId  
         , v.SearchGiataMappingId  
         , v.CPStatus  
         , v.CPDaysGain  
         , v.MatchedCancellationPolicyGain  
         , v.ReservationStatus  
         , v.NewReservationID  
         , v.NewReservationStatus  
         , v.ReservationPrice  
         , v.PrebookPrice  
         , v.Profit  
         , v.ProfitAfterCancellation  
         , v.ProfitPercentage  
         , v.MatchedReservationCancellationChargeByPolicy  
         , v.MatchedPrebookCancellationChargeByPolicy  
         , v.MatchedReservationCancellationDate  
         , v.MatchedPrebookCancellationDate  
         , v.IsFutureDate_RES_CP  
         , v.IsFutureDate_PRE_CP  
         , v.IsWithInBuffer_RES_CP  
         , v.IsWithInBuffer_PRE_CP  
         , v.BookingDate  
         , v.ReservationCheckin  
         , v.ReservationCheckout  
         , v.FirstPreBookCreateDate  
         , v.Createdate  
         , v.PrebookLastUpdateDate  
         , v.ReservationAdultCount  
         , v.PrebookAdultCount  
         , v.ReservationChildAges  
         , v.PrebookChildAges  
         , v.ReservationSupplier  
         , v.PrebookSupplier  
         , v.CurrencyfactortoEUR  
         , v.ReservationRoomName  
         , v.PrebookRoomName  
         , v.ReservationRoomBoard  
         , v.PrebookRoomBoard  
         , v.ReservationRoomBoardGroup  
         , v.PrebookRoomBoardGroup  
         , v.ReservationRoomInfo  
         , v.PrebookRoomInfo  
         , v.PrebookRoomIndex  
         , v.IsCancellationPolicyMatched  
         , v.RoomType  
         , v.Token  
         , v.Availabilitytoken  
         , v.Numberofrooms  
         , v.ReservationDestination  
         , v.PrebookDestination  
         , v.ReservationHotelName  
         , v.PrebookHotelName  
         , v.IsBookingActionTaken  
         , v.LastActionTakenDate  
         , v.ActionId  
         , v.ActionTakenGain  
         , v.PreBookCurrency  
         , v.ReservationCurrency  
         , v.prebookTableId  
         , v.DiffDayFromLastUpdate  
         , v.IsLivePreBook  
         , v.ReservationStatus_For_IsLivePreBook  
         , v.DiffDays_Optimisation  
         , v.PriceDifferenceValue  
         , v.PriceDifferencePercentage  
         , v.PriceDifferenceCurrency  
         , v.IsUsePercentage  
         , v.TravelDaysMaxSearchInDays  
         , v.TravelDaysMinSearchInDays  
         , v.DaysLimitCancellationPolicyEdgeCase  
         , v.CPDate  
         , v.CPBufferDate  
         , v.ReservationCancellationType  
         , v.PreBookCancellationType  
         , v.CancellationPolicyRemark  
         , v.ConnectionStatus  
         , v.ConnectionStatusDescription  
         , v.ReservationGiataPropertyName  
         , v.SearchGiataPropertyName  
         , v.ReservationLastActivity  
         , v.PrebookLastActivity  
         , v.ReservationCancelledOnDate  
         , v.PrebookCancelledOnDate  
         , v.FromCurrency  
         , v.ToCurrency  
         , v.Factor  
         , v.ProfitInOriginalCurrency  
         , v.ResellerName  
         , v.ResellerCode  
         , v.ResellerType  
         , v.rrd_updatedDate  
         , v.reservationCpJSON  
         , v.prebookCpJSON  
         , ROW_NUMBER() Over (
								PARTITION BY v.RepricerId, v.ReservationId
								ORDER BY v.RepricerId  
                                     , Case  
                                           when reportType = 'optimized' then  
                                               1  
                                           when reportType = 'prebook' then  
                                               2  
                                           ELSE  
                                               3  
                                       END  
                                     , v.CreateDate DESC  
                             )+1 AS PrebookRank  
    FROM ctemain v  
	LEFT JOIN dbo.BookingActionsTaken as bat
			on bat.RepricerId = v.RePricerId
			AND bat.ReservationId = v.ReservationId
			AND bat.ActionId = v.ActionId
    WHERE (  
              v.RepricerId = @RepricerId  
              or @RepricerId is null  
          )  
	AND V.reportType = 'prebook'
	AND (@ReservationId IS NULL OR v.Reservationid IN (SELECT ReservationId FROM #TempReservationIds))
	AND (@fromDateVar IS NULL OR CAST(v.Createdate AS DATE) >= @fromDateVar)
	AND (@ToDateVar IS NULL OR CAST(v.Createdate AS DATE) <= @ToDateVar)

    PRINT CAST(@@ROWCOUNT as VARCHAR) + ' Rows INSERTED For RepricerId = ' + CAST(@RepricerId as VARCHAR) + ' )';  
  
END