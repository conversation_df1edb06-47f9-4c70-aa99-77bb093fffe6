﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34003.232
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Re-Pricer", "Re-Pricer\Re-Pricer.csproj", "{F9153A3E-9A05-40FB-9F60-E9479D46728B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RePricer.Register", "RePricer.Register", "{C22DD43F-6D37-4742-8AB8-AB827442EE0C}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RePricer.ServiceAdapter", "RePricer.ServiceAdapter", "{B4895F43-53E2-4778-8C1A-2E5128281FC0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.ServiceAdapter", "Irix.ServiceAdapter\Irix.ServiceAdapter.csproj", "{25A3E30F-CC52-46B7-A3F3-8DB3B9E1E17F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RePricer.Util", "RePricer.Util", "{5392DD9C-AEE4-4191-BA91-616266481434}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RePricer.Util", "RePricer.Util\RePricer.Util.csproj", "{7F96A483-7246-47B8-AC7B-3135ABBB0F05}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RePricer.Register", "RePricer.Register\RePricer.Register.csproj", "{165CCAD1-7926-4860-A033-6F28B541BB1C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RePricer.Entities", "RePricer.Entities", "{F28BB248-0DB4-4D00-91EE-E75AC9FD90D0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.Entities", "Irix.Entities\Irix.Entities.csproj", "{B9FAB75D-A67D-48AE-9065-0BFD3E65A8AE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTests", "UnitTests", "{34B9FB52-96BA-4CFA-AF2D-C0C5DE2159C1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.TestApi", "Irix.TestApi\Irix.TestApi.csproj", "{8F77F35D-0850-4F5E-A9AC-342945D34B9C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RePricer.Service", "RePricer.Service", "{0267BD83-AAC4-4C47-A221-59C197847DEF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.Service", "Irix.Service\Irix.Service.csproj", "{0111EA4D-0A11-4BF8-97AB-8C77AEBD2892}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.Service.Contract", "Irix.Service.Contract\Irix.Service.Contract.csproj", "{59D943FB-93EF-428F-B959-9F931DAABECF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Logger", "Logger", "{46660F20-D119-4B78-8D57-690F05ECE0AA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Logger", "Logger\Logger.csproj", "{9A780779-8BED-4026-A793-DCD8C6FA32B5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Logger.Contract", "Logger.Contract\Logger.Contract.csproj", "{9980C92E-1659-4A77-BFBB-006C9FE6A5D3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RePricer.Persistence", "RePricer.Persistence", "{BC20D2A2-E502-4348-91EE-CFCA42A99EEA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.Persistence", "Irix.Persistence\Irix.Persistence.csproj", "{99913AFD-03BF-4951-8784-4298183E86C5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Irix.Persistence.Contract", "Irix.Persistence.Contract\Irix.Persistence.Contract.csproj", "{8EC9E0AF-4E0C-489F-A116-BB8AE3A7A3F1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AzureBatches", "AzureBatches", "{5A647FC2-3B0D-4BC5-90C8-5BB478D38643}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AzureBatches", "AzureBatches\AzureBatches.csproj", "{68D5E1B2-F3B4-4780-B8D2-69AABBD1C01F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ReservationAzureBatches", "ReservationAzureBatches\ReservationAzureBatches.csproj", "{97B79EFD-2F51-4326-B14C-D634EE8022F1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "HangFire", "HangFire", "{3F360518-23E0-440D-B22F-186333ED62B5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RePricerHangFire", "RePricerHangFire\RePricerHangFire.csproj", "{02BEB44A-4484-402C-AA4C-92142C3C258B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{FEA6E362-40CC-44D3-9F7B-D5D647F7187C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RePricer.Constants", "RePricer.Constants\RePricer.Constants.csproj", "{A0686472-440C-4395-A938-5FF53B855A2A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Repricer.Cache", "Repricer.Cache\Repricer.Cache.csproj", "{FCF1AEB0-0E6D-4716-A71B-79A417FC5FE1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Database", "Database", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		Database\repricer db to proejct.scmp = Database\repricer db to proejct.scmp
		Database\rpndb db to proejct.scmp = Database\rpndb db to proejct.scmp
		Database\rpndb project to db.scmp = Database\rpndb project to db.scmp
	EndProjectSection
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "repricer", "Database\repricer\repricer.sqlproj", "{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "authhangfire", "Database\authhangfire\authhangfire.sqlproj", "{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "hangfiredb", "Database\hangfiredb\hangfiredb.sqlproj", "{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "rpndb", "Database\rpndb\rpndb.sqlproj", "{F7566042-8D11-4BE6-8D92-A1578CBFDB95}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Re-Pricer-Frontend", "Re-Pricer-Frontend", "{BAB55DF9-E461-4176-975C-CB6D394AF98E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F9153A3E-9A05-40FB-9F60-E9479D46728B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9153A3E-9A05-40FB-9F60-E9479D46728B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9153A3E-9A05-40FB-9F60-E9479D46728B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9153A3E-9A05-40FB-9F60-E9479D46728B}.Release|Any CPU.Build.0 = Release|Any CPU
		{25A3E30F-CC52-46B7-A3F3-8DB3B9E1E17F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25A3E30F-CC52-46B7-A3F3-8DB3B9E1E17F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25A3E30F-CC52-46B7-A3F3-8DB3B9E1E17F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25A3E30F-CC52-46B7-A3F3-8DB3B9E1E17F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F96A483-7246-47B8-AC7B-3135ABBB0F05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F96A483-7246-47B8-AC7B-3135ABBB0F05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F96A483-7246-47B8-AC7B-3135ABBB0F05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F96A483-7246-47B8-AC7B-3135ABBB0F05}.Release|Any CPU.Build.0 = Release|Any CPU
		{165CCAD1-7926-4860-A033-6F28B541BB1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{165CCAD1-7926-4860-A033-6F28B541BB1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{165CCAD1-7926-4860-A033-6F28B541BB1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{165CCAD1-7926-4860-A033-6F28B541BB1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9FAB75D-A67D-48AE-9065-0BFD3E65A8AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9FAB75D-A67D-48AE-9065-0BFD3E65A8AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9FAB75D-A67D-48AE-9065-0BFD3E65A8AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9FAB75D-A67D-48AE-9065-0BFD3E65A8AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F77F35D-0850-4F5E-A9AC-342945D34B9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F77F35D-0850-4F5E-A9AC-342945D34B9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F77F35D-0850-4F5E-A9AC-342945D34B9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F77F35D-0850-4F5E-A9AC-342945D34B9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{0111EA4D-0A11-4BF8-97AB-8C77AEBD2892}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0111EA4D-0A11-4BF8-97AB-8C77AEBD2892}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0111EA4D-0A11-4BF8-97AB-8C77AEBD2892}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0111EA4D-0A11-4BF8-97AB-8C77AEBD2892}.Release|Any CPU.Build.0 = Release|Any CPU
		{59D943FB-93EF-428F-B959-9F931DAABECF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59D943FB-93EF-428F-B959-9F931DAABECF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59D943FB-93EF-428F-B959-9F931DAABECF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59D943FB-93EF-428F-B959-9F931DAABECF}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A780779-8BED-4026-A793-DCD8C6FA32B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A780779-8BED-4026-A793-DCD8C6FA32B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A780779-8BED-4026-A793-DCD8C6FA32B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A780779-8BED-4026-A793-DCD8C6FA32B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{9980C92E-1659-4A77-BFBB-006C9FE6A5D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9980C92E-1659-4A77-BFBB-006C9FE6A5D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9980C92E-1659-4A77-BFBB-006C9FE6A5D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9980C92E-1659-4A77-BFBB-006C9FE6A5D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{99913AFD-03BF-4951-8784-4298183E86C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99913AFD-03BF-4951-8784-4298183E86C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99913AFD-03BF-4951-8784-4298183E86C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99913AFD-03BF-4951-8784-4298183E86C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EC9E0AF-4E0C-489F-A116-BB8AE3A7A3F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EC9E0AF-4E0C-489F-A116-BB8AE3A7A3F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EC9E0AF-4E0C-489F-A116-BB8AE3A7A3F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EC9E0AF-4E0C-489F-A116-BB8AE3A7A3F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{68D5E1B2-F3B4-4780-B8D2-69AABBD1C01F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68D5E1B2-F3B4-4780-B8D2-69AABBD1C01F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68D5E1B2-F3B4-4780-B8D2-69AABBD1C01F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68D5E1B2-F3B4-4780-B8D2-69AABBD1C01F}.Release|Any CPU.Build.0 = Release|Any CPU
		{97B79EFD-2F51-4326-B14C-D634EE8022F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97B79EFD-2F51-4326-B14C-D634EE8022F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97B79EFD-2F51-4326-B14C-D634EE8022F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97B79EFD-2F51-4326-B14C-D634EE8022F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{02BEB44A-4484-402C-AA4C-92142C3C258B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02BEB44A-4484-402C-AA4C-92142C3C258B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02BEB44A-4484-402C-AA4C-92142C3C258B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02BEB44A-4484-402C-AA4C-92142C3C258B}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0686472-440C-4395-A938-5FF53B855A2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0686472-440C-4395-A938-5FF53B855A2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0686472-440C-4395-A938-5FF53B855A2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0686472-440C-4395-A938-5FF53B855A2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCF1AEB0-0E6D-4716-A71B-79A417FC5FE1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCF1AEB0-0E6D-4716-A71B-79A417FC5FE1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCF1AEB0-0E6D-4716-A71B-79A417FC5FE1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCF1AEB0-0E6D-4716-A71B-79A417FC5FE1}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}.Release|Any CPU.Build.0 = Release|Any CPU
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95}.Release|Any CPU.Deploy.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{25A3E30F-CC52-46B7-A3F3-8DB3B9E1E17F} = {B4895F43-53E2-4778-8C1A-2E5128281FC0}
		{5392DD9C-AEE4-4191-BA91-616266481434} = {FEA6E362-40CC-44D3-9F7B-D5D647F7187C}
		{7F96A483-7246-47B8-AC7B-3135ABBB0F05} = {5392DD9C-AEE4-4191-BA91-616266481434}
		{165CCAD1-7926-4860-A033-6F28B541BB1C} = {C22DD43F-6D37-4742-8AB8-AB827442EE0C}
		{B9FAB75D-A67D-48AE-9065-0BFD3E65A8AE} = {F28BB248-0DB4-4D00-91EE-E75AC9FD90D0}
		{8F77F35D-0850-4F5E-A9AC-342945D34B9C} = {34B9FB52-96BA-4CFA-AF2D-C0C5DE2159C1}
		{0111EA4D-0A11-4BF8-97AB-8C77AEBD2892} = {0267BD83-AAC4-4C47-A221-59C197847DEF}
		{59D943FB-93EF-428F-B959-9F931DAABECF} = {0267BD83-AAC4-4C47-A221-59C197847DEF}
		{9A780779-8BED-4026-A793-DCD8C6FA32B5} = {46660F20-D119-4B78-8D57-690F05ECE0AA}
		{9980C92E-1659-4A77-BFBB-006C9FE6A5D3} = {46660F20-D119-4B78-8D57-690F05ECE0AA}
		{99913AFD-03BF-4951-8784-4298183E86C5} = {BC20D2A2-E502-4348-91EE-CFCA42A99EEA}
		{8EC9E0AF-4E0C-489F-A116-BB8AE3A7A3F1} = {BC20D2A2-E502-4348-91EE-CFCA42A99EEA}
		{68D5E1B2-F3B4-4780-B8D2-69AABBD1C01F} = {5A647FC2-3B0D-4BC5-90C8-5BB478D38643}
		{97B79EFD-2F51-4326-B14C-D634EE8022F1} = {5A647FC2-3B0D-4BC5-90C8-5BB478D38643}
		{02BEB44A-4484-402C-AA4C-92142C3C258B} = {3F360518-23E0-440D-B22F-186333ED62B5}
		{A0686472-440C-4395-A938-5FF53B855A2A} = {FEA6E362-40CC-44D3-9F7B-D5D647F7187C}
		{FCF1AEB0-0E6D-4716-A71B-79A417FC5FE1} = {FEA6E362-40CC-44D3-9F7B-D5D647F7187C}
		{3BDEBA7B-9CBB-4A32-9C60-BCA4712F758A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{1B38033F-5DE9-44B5-B89A-C34BE1F7115F} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{9BF02C19-8645-4DD7-A2EA-6895AE8F2879} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F7566042-8D11-4BE6-8D92-A1578CBFDB95} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C70F6015-BE4F-449D-BE27-79C2813D11F7}
	EndGlobalSection
EndGlobal
