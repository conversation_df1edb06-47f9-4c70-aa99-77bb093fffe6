﻿--/*
CREATE PROCEDURE [dbo].[usp_upd_reservationreport_AdditionalPrebook]
(
    @Repricerid INT
  , @Reservationid INT = null
)
AS
--*/

/*
Declare
        @Repricerid    INT = 2
      , @Reservationid INT = null
--*/
BEGIN
    SET NOCOUNT ON; -- Turns off the message that shows the count of affected rows
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    -- Error handling setup
    BEGIN TRY
        Declare @CurrentDate DATE = GETUTCDATE()

        DROP TABLE IF EXISTS #temp_roomcounttable;
        DROP TABLE IF EXISTS #temp_RecentReservations;
        DROP TABLE IF EXISTS #temp_ClientConfig;
        DROP TABLE IF EXISTS #temp_RecentReservations_dateasc
        DROP TABLE IF EXISTS #temp_LatestResellerInfo
        DROP TABLE IF EXISTS #temp_ActiveTab
        DROP TABLE IF EXISTS #temp_OrderedLogs
        DROP TABLE IF EXISTS #temp_AdditionalPrebooks
        DROP TABLE IF EXISTS #temp_PrimaryPrebooks_Characteristics
        DROP TABLE IF EXISTS #temp_PrimaryPrebooks

        CREATE TABLE #temp_roomcounttable
        (
            Roomcount INT
          , Reservationid INT
          , Repricerid INT
          , RoomType varchar(max)
        );

        CREATE TABLE #temp_RecentReservations_dateasc
        (
            id INT
          , ReservationId INT
          , createdate DATETIME
          , repricerid int
          , profit decimal(10, 2)
        );

        CREATE TABLE #temp_LatestResellerInfo
        (
            RowNum INT
          , Repricerid int
          , ReservationId INT
          , ResellerName varchar(128)
          , ResellerCode varchar(128)
          , ResellerType varchar(128)
        );
        CREATE NONCLUSTERED INDEX IX_temp_temp_LatestResellerInfo_Lookup
        ON #temp_LatestResellerInfo
        (
            RepricerId
          , ReservationId
        );

        ;WITH cte_LatestResellerInfo
        AS (SELECT RepricerId
                 , ReservationId
                 , ResellerName
                 , ResellerCode
                 , ResellerType
                 , ROW_NUMBER() OVER (PARTITION BY RepricerId
                                                 , ReservationId
                                      ORDER BY ISNULL(UpdatedDate, CreatedDate) DESC
                                     ) AS RowNum
            FROM [dbo].[Reservation_ResellerInfo]
            WHERE Repricerid = @Repricerid
                  and (
                          ReservationId = @ReservationId
                          or @ReservationId is null
                      )
           )
        INSERT INTO #temp_LatestResellerInfo
        SELECT RowNum
             , RepricerId
             , ReservationId
             , ResellerName
             , ResellerCode
             , ResellerType
        FROM cte_LatestResellerInfo
        Where RowNum = 1

        CREATE TABLE #temp_ClientConfig
        (
            ID INT
          , ReservationID INT
          , DiffDays_Optimisation INT
          , PriceDifferenceValue DECIMAL(18, 5)
          , PriceDifferencePercentage DECIMAL(18, 5)
          , pricedifferencecurrency varchar(200)
          , IsUsePercentage bit
          , traveldaysmaxsearchindays int
          , traveldaysminsearchindays int
          , RepricerID INT
          , CreatedDate DATETIME
        );

        ;WITH cte_ClientConfig
        AS (SELECT ID
                 , ReservationID
                 , DiffDays_Optimisation
                 , PriceDifferenceValue
                 , PriceDifferencePercentage
                 , pricedifferencecurrency
                 , IsUsePercentage
                 , traveldaysmaxsearchindays
                 , traveldaysminsearchindays
                 , RepricerID
                 , CreatedDate
                 , ROW_NUMBER() OVER (PARTITION BY RepricerID
                                                 , ReservationID
                                      ORDER BY RepricerID
                                             , CreatedDate DESC
                                     ) AS RowNum
            FROM PreBook_ClientConfiguration
            WHERE RepricerID = @RepricerID
           )
        INSERT INTO #temp_ClientConfig
        (
            ID
          , ReservationID
          , DiffDays_Optimisation
          , PriceDifferenceValue
          , PriceDifferencePercentage
          , pricedifferencecurrency
          , IsUsePercentage
          , traveldaysmaxsearchindays
          , traveldaysminsearchindays
          , RepricerID
          , CreatedDate
        )
        SELECT ID
             , ReservationID
             , DiffDays_Optimisation
             , PriceDifferenceValue
             , PriceDifferencePercentage
             , pricedifferencecurrency
             , IsUsePercentage
             , traveldaysmaxsearchindays
             , traveldaysminsearchindays
             , RepricerID
             , CreatedDate
        FROM cte_ClientConfig
        WHERE RowNum = 1;

        -- Create temp table for all active prebook data (same as original procedure)
        SELECT CAST(ISNULL(t1.id, null) AS INT)                                                         as Id
             , 1                                                                                        as TableType
             , t1.ReservationId
             , createdate
             , t1.RepricerID
             , Reservationadultcount
             , Prebookadultcount
             , Reservationchildages
             , Prebookchildages
             , Providers
             , BookingDate
             , (ReservationPrice * CurrencyFactortoEur)                                                 as ReservationPrice
             , (PreBookPrice * CurrencyFactortoEur)                                                     as PreBookPrice
             , (ProfitAfterCancellation * CurrencyFactortoEur)                                          as ProfitAfterCancellation
             , (Profit * CurrencyFactortoEur)                                                           as Profit
             , CurrencyFactortoEur
             , Reservationroomname
             , PreBookRoomName
             , ReservationRoomBoard
             , PreBookRoomBoard
             , ReservationRoomInfo
             , PrebookRoomInfo
             , PreBookRoomIndex
             , MatchedReservationCancellationDate
             , MatchedPreBookCancellationDate
             , MatchedReservationCancellationChargeByPolicy
             , MatchedPreBookCancellationChargeByPolicy
             , IsCancellationPolicyMatched
             , cPStatus
             , cpdaysgain
             , (matchedcancellationpolicygain * CurrencyFactortoEur)                                    as matchedcancellationpolicygain
             , t1.token
             , AvailabilityToken
             , PrebookProviders                                                                         as PrebookSupplier
             , ((dbo.ExtractValue(matchedreservationcancellationchargebypolicy)) * CurrencyFactortoEur) as MatchedReservationCancellationChargeByPolicyToEur
             , ((dbo.ExtractValue(MatchedPreBookCancellationChargeByPolicy)) * CurrencyFactortoEur)     as MatchedPreBookCancellationChargeByPolicytoEur
             , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                               WHEN CHARINDEX(
                                                                                 ','
                                                                               , ISNULL(ReservationGiataMappingId, '')
                                                                             ) > 0 THEN
                                                                   CHARINDEX(',', ISNULL(ReservationGiataMappingId, ''))
                                                                   - 1
                                                               ELSE
                                                                   LEN(ISNULL(ReservationGiataMappingId, ''))
                                                           END)                                         as ReservationGiataMappingId
             , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                          WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                              CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                          ELSE
                                                              LEN(ISNULL(SearchGiataMappingId, ''))
                                                      end)                                              as SearchGiataMappingId
             , PreBookGiataPropertyName
             , ReservationGiataPropertyName
             , ReservationRoomBoardGroup
             , PrebookRoomBoardGroup
             , ReservationCancellationType
             , PreBookCancellationType
             , CancellationPolicyRemark
             , ISNULL(IsOptimized, 0)                                                                   as IsOptimized
             , bat.createdOn                                                                            as bat_createdDate
             , bat.NewBookingId                                                                         as bat_NewBookingId
             , bat.NewBookingPrice                                                                      as bat_Profit
             , bat.ActionId                                                                             as bat_ActionId
        INTO #temp_ActiveTab
        FROM dbo.ReservationTable               t1
            LEFT JOIN dbo.[BookingActionsTaken] AS bat
                ON bat.RePricerID = t1.RePricerID
                   AND bat.Reservationid = t1.Reservationid
                   AND bat.ActionID = 1
        where 1 = 2;
        ALTER TABLE #temp_ActiveTab ALTER COLUMN Id INT NULL;

        -- Populate data from ReservationTable (same as original procedure)
        IF OBJECT_ID('tempdb..#temp_ActiveTab') IS NOT NULL
        BEGIN
            INSERT INTO #temp_ActiveTab
            (
                Id
              , TableType
              , ReservationId
              , createdate
              , RepricerID
              , Reservationadultcount
              , Prebookadultcount
              , Reservationchildages
              , Prebookchildages
              , Providers
              , BookingDate
              , ReservationPrice
              , PreBookPrice
              , ProfitAfterCancellation
              , Profit
              , CurrencyFactortoEur
              , Reservationroomname
              , PreBookRoomName
              , ReservationRoomBoard
              , PreBookRoomBoard
              , ReservationRoomInfo
              , PrebookRoomInfo
              , PreBookRoomIndex
              , MatchedReservationCancellationDate
              , MatchedPreBookCancellationDate
              , MatchedReservationCancellationChargeByPolicy
              , MatchedPreBookCancellationChargeByPolicy
              , IsCancellationPolicyMatched
              , cPStatus
              , cpdaysgain
              , matchedcancellationpolicygain
              , token
              , AvailabilityToken
              , PrebookSupplier
              , MatchedReservationCancellationChargeByPolicyToEur
              , MatchedPreBookCancellationChargeByPolicytoEur
              , ReservationGiataMappingId
              , SearchGiataMappingId
              , PreBookGiataPropertyName
              , ReservationGiataPropertyName
              , ReservationRoomBoardGroup
              , PrebookRoomBoardGroup
              , ReservationCancellationType
              , PreBookCancellationType
              , CancellationPolicyRemark
              , IsOptimized
              , bat_createdDate
              , bat_NewBookingId
              , bat_Profit
              , bat_ActionId
            )
            SELECT ISNULL(t1.id, null)                                                                      as Id
                 , 1                                                                                        as TableType
                 , t1.ReservationId
                 , createdate
                 , t1.RepricerID
                 , Reservationadultcount
                 , Prebookadultcount
                 , Reservationchildages
                 , Prebookchildages
                 , Providers
                 , BookingDate
                 , (ReservationPrice * CurrencyFactortoEur)                                                 as ReservationPrice
                 , (PreBookPrice * CurrencyFactortoEur)                                                     as PreBookPrice
                 , (ProfitAfterCancellation * CurrencyFactortoEur)                                          as ProfitAfterCancellation
                 , (Profit * CurrencyFactortoEur)                                                           as Profit
                 , CurrencyFactortoEur
                 , Reservationroomname
                 , PreBookRoomName
                 , ReservationRoomBoard
                 , PreBookRoomBoard
                 , ReservationRoomInfo
                 , PrebookRoomInfo
                 , PreBookRoomIndex
                 , MatchedReservationCancellationDate
                 , MatchedPreBookCancellationDate
                 , MatchedReservationCancellationChargeByPolicy
                 , MatchedPreBookCancellationChargeByPolicy
                 , IsCancellationPolicyMatched
                 , cPStatus
                 , cpdaysgain
                 , (matchedcancellationpolicygain * CurrencyFactortoEur)                                    as matchedcancellationpolicygain
                 , t1.token
                 , AvailabilityToken
                 , PrebookProviders                                                                         as PrebookSupplier
                 , ((dbo.ExtractValue(matchedreservationcancellationchargebypolicy)) * CurrencyFactortoEur) as MatchedReservationCancellationChargeByPolicyToEur
                 , ((dbo.ExtractValue(MatchedPreBookCancellationChargeByPolicy)) * CurrencyFactortoEur)     as MatchedPreBookCancellationChargeByPolicytoEur
                 , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                                   WHEN CHARINDEX(
                                                                                     ','
                                                                                   , ISNULL(
                                                                                               ReservationGiataMappingId
                                                                                             , ''
                                                                                           )
                                                                                 ) > 0 THEN
                                                                       CHARINDEX(
                                                                                    ','
                                                                                  , ISNULL(
                                                                                              ReservationGiataMappingId
                                                                                            , ''
                                                                                          )
                                                                                ) - 1
                                                                   ELSE
                                                                       LEN(ISNULL(ReservationGiataMappingId, ''))
                                                               END)                                         as ReservationGiataMappingId
                 , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                              WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                                  CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                              ELSE
                                                                  LEN(ISNULL(SearchGiataMappingId, ''))
                                                          end)                                              as SearchGiataMappingId
                 , PreBookGiataPropertyName
                 , ReservationGiataPropertyName
                 , ReservationRoomBoardGroup
                 , PrebookRoomBoardGroup
                 , ReservationCancellationType
                 , PreBookCancellationType
                 , CancellationPolicyRemark
                 , ISNULL(IsOptimized, 0)                                                                   as IsOptimized
                 , bat.createdOn                                                                            as bat_createdDate
                 , bat.NewBookingId                                                                         as bat_NewBookingId
                 , bat.NewBookingPrice                                                                      as bat_Profit
                 , bat.ActionId                                                                             as bat_ActionId
            FROM dbo.ReservationTable               t1
                LEFT JOIN dbo.[BookingActionsTaken] AS bat
                    ON bat.RePricerID = t1.RePricerID
                       AND bat.Reservationid = t1.Reservationid
                       AND bat.ActionID = 1
            where t1.Repricerid = @Repricerid
                  and (
                          t1.ReservationId = @ReservationId
                          or @ReservationId is null
                      );
        END

        -- CRITICAL: Select ADDITIONAL prebooks (ranks 2 and 3) ONLY for NON-OPTIMIZED reservations
        -- STEP 1: Get primary prebook characteristics for diversity filtering
        CREATE TABLE #temp_PrimaryPrebooks_Characteristics
        (
            ReservationId INT
          , RepricerId INT
          , PrimarySupplier VARCHAR(255)
          , PrimaryProfit DECIMAL(18, 5)
          , PrimaryCancellationDate DATETIME
          , PrimaryPrice DECIMAL(18, 5)
        );

        INSERT INTO #temp_PrimaryPrebooks_Characteristics
        SELECT DISTINCT
            t1.ReservationId
          , t1.RepricerId
          , t1.PrebookSupplier                as PrimarySupplier
          , t1.Profit                         as PrimaryProfit
          , t1.MatchedPreBookCancellationDate as PrimaryCancellationDate
          , t1.PreBookPrice                   as PrimaryPrice
        FROM
        (
            SELECT ROW_NUMBER() OVER (PARTITION BY t.RepricerId
                                                 , t.ReservationID
                                      ORDER BY ISNULL(t.IsOptimized, 0) DESC   -- 1st Priority: Already optimized
                                             , CAST(t.CreateDate AS DATE) DESC -- 2nd Priority: Most recent date
                                             , CASE
                                                   WHEN (t.cpstatus = 'loose')
                                                        AND t.ReservationGiataMappingId IS NULL
                                                        AND CAST(t.CreateDate AS DATE) = @CurrentDate THEN
                                                       1
                                                   WHEN (t.cpstatus = 'loose')
                                                        AND t.ReservationGiataMappingId IS NOT NULL
                                                        AND CAST(t.CreateDate AS DATE) = @CurrentDate THEN
                                                       2
                                                   ELSE
                                                       3
                                               END                             -- 3rd Priority: CP status + mapping
                                             , CAST(t.CreateDate AS DATE) DESC -- 4th Priority: Date again
                                             , t.Profit DESC                   -- 5th Priority: Highest profit
                                                                               -- 6th Priority: Most recent time
                                             , t.MatchedPreBookCancellationDate DESC
                                             , t.MatchedReservationCancellationChargeByPolicy
                                             , t.CreateDate DESC
                                     ) as rn
                 , t.*
            FROM #temp_ActiveTab t
            WHERE CAST(t.CreateDate AS DATE) = @CurrentDate -- SAME DAY REQUIREMENT
                  AND ISNULL(t.IsOptimized, 0) = 0 -- EXCLUDE ALREADY OPTIMIZED BOOKINGS
                  AND ISNULL(t.bat_NewBookingId, 0) = 0 -- EXCLUDE OPTIMIZED: BookingActionsTaken.ActionId=1 means optimized
                  AND t.cPStatus = 'loose'
                  AND t.Providers <> t.PrebookSupplier
        ) t1
        WHERE t1.rn = 1; -- GET RANK 1 CHARACTERISTICS

        -- STEP 2: ONE ROW PER SUPPLIER with MAX PROFIT and BEST CANCELLATION
        SELECT *
        INTO #temp_OrderedLogs
        FROM
        (
            SELECT ROW_NUMBER() OVER (PARTITION BY RepricerId
                                                 , ReservationID
                                      ORDER BY Profit DESC
                                             , MatchedPreBookCancellationDate DESC
                                     ) + 1 as PrebookRank
                 , *
            FROM
            (
                SELECT t.*
                     , ROW_NUMBER() OVER (PARTITION BY t.RepricerId
                                                     , t.ReservationID
                                                     , PrebookSupplier
                                                     , Profit
                                          ORDER BY t.Profit DESC
                                                 , t.MatchedPreBookCancellationDate DESC
                                                 , t.CreateDate DESC
                                         ) as rn
                FROM #temp_ActiveTab                                 t
                    INNER JOIN #temp_PrimaryPrebooks_Characteristics p
                        ON t.RepricerId = p.RepricerId
                           AND t.ReservationId = p.ReservationId
                WHERE CAST(t.CreateDate AS DATE) = @CurrentDate
                      AND ISNULL(t.IsOptimized, 0) = 0
                      AND ISNULL(t.bat_NewBookingId, 0) = 0
                      AND t.cPStatus = 'loose'
                      AND t.Providers <> t.PrebookSupplier
                      AND t.PrebookSupplier != p.PrimarySupplier
            ) best_per_supplier
            WHERE rn = 1 -- ONLY BEST OPTION PER SUPPLIER
        ) ranked_suppliers
        --    WHERE PrebookRank BETWEEN 2 AND 3;  -- ONLY RANKS 2 AND 3




        -- Get primary prebook IDs for reference
        CREATE TABLE #temp_PrimaryPrebooks
        (
            ReservationId INT
          , RepricerId INT
          , PrimaryPrebookId BIGINT
        );

        INSERT INTO #temp_PrimaryPrebooks
        SELECT rrd.Reservationid
             , rrd.Repricerid
             , rrd.PreBookId
        FROM ReservationReportDetails rrd
        WHERE rrd.Repricerid = @Repricerid
              AND (
                      rrd.Reservationid = @ReservationId
                      OR @ReservationId IS NULL
                  );

        INSERT INTO #temp_RecentReservations_dateasc
        SELECT id
             , ReservationId
             , createdate
             , RePricerId
             , Profit
        FROM #temp_OrderedLogs
        Where RePricerID = @RePricerID
              and (
                      ReservationId = @ReservationId
                      or @ReservationId is null
                  )

        INSERT INTO #temp_roomcounttable
        (
            Roomcount
          , Reservationid
          , Repricerid
          , RoomType
        )
        SELECT Count(RR.Roomname)                 AS RoomCount
             , RR.Reservationid
             , ISNULL(RR.Repricerid, @Repricerid) AS RepricerId
             , STRING_AGG(   CASE
                                 WHEN RR.RoomType IS NOT NULL
                                      AND RR.RoomType != '' THEN
                                     CAST(RR.RoomType AS VARCHAR(MAX))
                                 ELSE
                                     NULL
                             END
                           , ','
                         )                        AS RoomType
        FROM dbo.RESERVATIONROOM RR
        Where Repricerid = @Repricerid
              and (
                      ReservationId = @ReservationId
                      or @ReservationId is null
                  )
        GROUP BY RR.Reservationid
               , ISNULL(RR.Repricerid, @Repricerid)
        HAVING COUNT(RR.RoomType) <= 20;

        -- PREVENT DUPLICATES: Clear existing additional prebooks for this repricer before inserting new ones
        DELETE FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @Repricerid
              AND (
                      Reservationid = @ReservationId
                      OR @ReservationId IS NULL
                  );

        -- MERGE additional prebooks into ReservationReportDetailsAdditionalPrebook table
        WITH cte_AdditionalPrebooks
        AS (SELECT id
                 , ReservationId
                 , createdate
                 , RepricerID
                 , Reservationadultcount
                 , Prebookadultcount
                 , Reservationchildages
                 , Prebookchildages
                 , Providers
                 , BookingDate
                 , ReservationPrice
                 , PreBookPrice
                 , ProfitAfterCancellation
                 , Profit
                 , CurrencyFactortoEur
                 , Reservationroomname
                 , PreBookRoomName
                 , ReservationRoomBoard
                 , PreBookRoomBoard
                 , ReservationRoomInfo
                 , PrebookRoomInfo
                 , PreBookRoomIndex
                 , MatchedReservationCancellationDate
                 , MatchedPreBookCancellationDate
                 , MatchedReservationCancellationChargeByPolicy
                 , MatchedPreBookCancellationChargeByPolicy
                 , IsCancellationPolicyMatched
                 , cPStatus
                 , cpdaysgain                                       as CPDaysGain
                 , matchedcancellationpolicygain                    as MatchedCancellationPolicyGain
                 , token
                 , AvailabilityToken
                 , PrebookSupplier
                 , MatchedReservationCancellationChargeByPolicyToEur
                 , MatchedPreBookCancellationChargeByPolicytoEur
                 , LEFT(ISNULL(ReservationGiataMappingId, ''), CASE
                                                                   WHEN CHARINDEX(
                                                                                     ','
                                                                                   , ISNULL(
                                                                                               ReservationGiataMappingId
                                                                                             , ''
                                                                                           )
                                                                                 ) > 0 THEN
                                                                       CHARINDEX(
                                                                                    ','
                                                                                  , ISNULL(
                                                                                              ReservationGiataMappingId
                                                                                            , ''
                                                                                          )
                                                                                ) - 1
                                                                   ELSE
                                                                       LEN(ISNULL(ReservationGiataMappingId, ''))
                                                               END) as ReservationGiataMappingId
                 , LEFT(ISNULL(SearchGiataMappingId, ''), CASE
                                                              WHEN CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) > 0 THEN
                                                                  CHARINDEX(',', ISNULL(SearchGiataMappingId, '')) - 1
                                                              ELSE
                                                                  LEN(ISNULL(SearchGiataMappingId, ''))
                                                          end)      as SearchGiataMappingId
                 , PreBookGiataPropertyName
                 , ReservationGiataPropertyName
                 , ReservationRoomBoardGroup
                 , PrebookRoomBoardGroup
                 , ReservationCancellationType
                 , PreBookCancellationType
                 , CancellationPolicyRemark
                 , PrebookRank
            FROM #temp_OrderedLogs
           )
        MERGE INTO ReservationReportDetailsAdditionalPrebook AS target
        USING
        (
            SELECT DISTINCT
                rt.id
              , rt.Reservationid
              , rt.Repricerid
              , rt.Createdate
              , rt.Reservationadultcount
              , rt.Prebookadultcount
              , rt.Reservationchildages
              , rt.Prebookchildages
              , rt.Providers
              , rt.BookingDate
              , rt.ReservationPrice
              , rt.PreBookPrice
              , rt.ProfitAfterCancellation
              , rt.Profit
              , rt.CurrencyFactorToEur
              , rt.ReservationRoomName
              , rt.PreBookRoomName
              , rt.ReservationRoomBoard
              , rt.PreBookRoomBoard
              , rt.ReservationRoomInfo
              , rt.PrebookRoomInfo
              , rt.PreBookRoomIndex
              , rt.MatchedReservationCancellationDate
              , rt.MatchedPreBookCancellationDate
              , rt.MatchedReservationCancellationChargeByPolicy
              , rt.MatchedPreBookCancellationChargeByPolicy
              , rt.IsCancellationPolicyMatched
              , rt.CPStatus
              , rt.CPDaysGain
              , rt.MatchedCancellationPolicyGain
              , rt.Token
              , rt.AvailabilityToken
              , TRC.Roomcount               AS NumberOfRooms
              , rm.ReservationStatus
              , rt.PrebookSupplier
              , rm.checkIn
              , rm.Checkout
              , rh.hotelname                AS reservationhotelname
              , rh.hotelname                AS prebookhotelname
              , Isnull(rm.destinations, '') AS prebookdestination
              , Isnull(rm.destinations, '') AS reservationdestination
              , ISNULL(trc.RoomType, '')    as roomType
              , rt.Createdate               as firstcreatedate
              , ccr.DiffDays_Optimisation
              , ccr.PriceDifferenceValue
              , ccr.PriceDifferencePercentage
              , ccr.pricedifferencecurrency
              , ccr.IsUsePercentage
              , ccr.traveldaysmaxsearchindays
              , ccr.traveldaysminsearchindays
              , rt.MatchedReservationCancellationChargeByPolicyToEur
              , rt.MatchedPreBookCancellationChargeByPolicytoEur
              , rt.ReservationGiataMappingId
              , rt.SearchGiataMappingId
              , PreBookGiataPropertyName
              , ReservationGiataPropertyName
              , ReservationRoomBoardGroup
              , PrebookRoomBoardGroup
              , ReservationCancellationType
              , PreBookCancellationType
              , CancellationPolicyRemark
              , resell.[ResellerName]
              , resell.[ResellerCode]
              , resell.[ResellerType]
              , rt.PrebookRank
              , rt.id                       as PrimaryPrebookId
            FROM cte_AdditionalPrebooks                    rt
                left JOIN dbo.RESERVATIONMAIN              RM
                    ON RM.Reservationid = RT.Reservationid
                       AND RT.Repricerid = RM.Repricerid
                left JOIN #temp_roomcounttable             TRC
                    ON TRC.Reservationid = RT.Reservationid
                       AND RT.Repricerid = trc.Repricerid
                left JOIN dbo.reservationhotelinformation  RH
                    ON rh.reservationid = rt.reservationid
                       AND rh.repricerid = rt.repricerid
                left JOIN #temp_RecentReservations_dateasc rrs
                    ON rrs.reservationid = rt.reservationid
                       AND rrs.repricerid = rt.repricerid
                left JOIN #temp_ClientConfig               ccr
                    ON ccr.reservationid = rt.reservationid
                       AND ccr.repricerid = rt.repricerid
                left join #temp_LatestResellerInfo         as resell
                    on rt.Repricerid = resell.RepricerId
                       AND rt.Reservationid = resell.Reservationid
            where rt.Repricerid = @Repricerid
                  and (
                          rt.ReservationId = @ReservationId
                          or @ReservationId is null
                      )
                  and rm.checkin is not null
        ) AS source
        (id, Reservationid, Repricerid, Createdate, Reservationadultcount, Prebookadultcount, Reservationchildages, Prebookchildages, Providers, BookingDate, ReservationPrice, PreBookPrice, ProfitAfterCancellation, Profit, CurrencyFactorToEur, ReservationRoomName, PreBookRoomName, ReservationRoomBoard, PreBookRoomBoard, ReservationRoomInfo, PrebookRoomInfo, PreBookRoomIndex, MatchedReservationCancellationDate, MatchedPreBookCancellationDate, MatchedReservationCancellationChargeByPolicy, MatchedPreBookCancellationChargeByPolicy, IsCancellationPolicyMatched, CPStatus, CPDaysGain, MatchedCancellationPolicyGain, Token, AvailabilityToken, NumberOfRooms, ReservationStatus, PrebookSupplier, checkin, checkout, reservationhotelname, prebookhotelname, prebookdestination, reservationdestination, roomType, firstcreatedate, DiffDays_Optimisation, PriceDifferenceValue, PriceDifferencePercentage, pricedifferencecurrency, IsUsePercentage, traveldaysmaxsearchindays, traveldaysminsearchindays, MatchedReservationCancellationChargeByPolicyToEur, MatchedPreBookCancellationChargeByPolicytoEur, ReservationGiataMappingId, SearchGiataMappingId, PreBookGiataPropertyName, ReservationGiataPropertyName, ReservationRoomBoardGroup, PrebookRoomBoardGroup, ReservationCancellationType, PreBookCancellationType, CancellationPolicyRemark, [ResellerName], [ResellerCode], [ResellerType], PrebookRank, PrimaryPrebookId)
        ON (
               target.Reservationid = source.Reservationid
               and target.Repricerid = source.Repricerid
               and target.PrebookSupplier = source.PrebookSupplier
           )
        WHEN MATCHED THEN
            UPDATE SET target.PreBookId = source.id
                     -- , target.Repricerid = source.Repricerid
                     , target.UpdatedOn = source.Createdate
                     , target.createdate = source.firstcreatedate
                     , target.Reservationadultcount = source.Reservationadultcount
                     , target.Prebookadultcount = source.Prebookadultcount
                     , target.Reservationchildages = source.Reservationchildages
                     , target.PrebookChildAges = source.Prebookchildages
                     , target.Providers = source.Providers
                     , target.BookingDate = source.BookingDate
                     , target.ReservationPrice = source.ReservationPrice
                     , target.PreBookPrice = source.PreBookPrice
                     , target.ProfitAfterCancellation = source.ProfitAfterCancellation
                     , target.Profit = source.Profit
                     , target.CurrencyFactorToEur = source.CurrencyFactorToEur
                     , target.ReservationRoomName = source.ReservationRoomName
                     , target.PreBookRoomName = source.PreBookRoomName
                     , target.ReservationRoomBoard = source.ReservationRoomBoard
                     , target.PreBookRoomBoard = source.PreBookRoomBoard
                     , target.ReservationRoomInfo = source.ReservationRoomInfo
                     , target.PrebookRoomInfo = source.PrebookRoomInfo
                     , target.PreBookRoomIndex = source.PreBookRoomIndex
                     , target.MatchedReservationCancellationDate = source.MatchedReservationCancellationDate
                     , target.MatchedPreBookCancellationDate = source.MatchedPreBookCancellationDate
                     , target.MatchedReservationCancellationChargeByPolicy = source.MatchedReservationCancellationChargeByPolicy
                     , target.MatchedPreBookCancellationChargeByPolicy = source.MatchedPreBookCancellationChargeByPolicy
                     , target.IsCancellationPolicyMatched = source.IsCancellationPolicyMatched
                     , target.CPStatus = source.CPStatus
                     , target.CPDaysGain = source.CPDaysGain
                     , target.MatchedCancellationPolicyGain = source.MatchedCancellationPolicyGain
                     , target.Token = source.Token
                     , target.AvailabilityToken = source.AvailabilityToken
                     , target.NumberOfRooms = source.NumberOfRooms
                     , target.ReservationStatus = source.ReservationStatus
                     , target.prebooksupplier = isnull(source.prebooksupplier, source.Providers)
                     , target.checkin = source.checkin
                     , target.checkout = source.checkout
                     , target.reservationhotelname = source.reservationhotelname
                     , target.prebookhotelname = source.prebookhotelname
                     , target.prebookdestination = source.prebookdestination
                     , target.reservationdestination = source.reservationdestination
                     , target.roomType = source.roomType
                     , target.DiffDays_Optimisation = source.DiffDays_Optimisation
                     , target.PriceDifferenceValue = source.PriceDifferenceValue
                     , target.PriceDifferencePercentage = source.PriceDifferencePercentage
                     , target.pricedifferencecurrency = source.pricedifferencecurrency
                     , target.IsUsePercentage = source.IsUsePercentage
                     , target.traveldaysmaxsearchindays = source.traveldaysmaxsearchindays
                     , target.traveldaysminsearchindays = source.traveldaysminsearchindays
                     , target.MatchedReservationCancellationChargeByPolicyToEur = source.MatchedReservationCancellationChargeByPolicyToEur
                     , target.MatchedPreBookCancellationChargeByPolicytoEur = source.MatchedPreBookCancellationChargeByPolicytoEur
                     , target.ReservationGiataMappingId = source.ReservationGiataMappingId
                     , target.SearchGiataMappingId = source.SearchGiataMappingId
                     , target.PreBookGiataPropertyName = source.PreBookGiataPropertyName
                     , target.ReservationGiataPropertyName = source.ReservationGiataPropertyName
                     , target.ReservationRoomBoardGroup = source.ReservationRoomBoardGroup
                     , target.PrebookRoomBoardGroup = source.PrebookRoomBoardGroup
                     , target.ReservationCancellationType = source.ReservationCancellationType
                     , target.PreBookCancellationType = source.PreBookCancellationType
                     , target.CancellationPolicyRemark = source.CancellationPolicyRemark
                     , target.[ResellerName] = source.[ResellerName]
                     , target.[ResellerCode] = source.[ResellerCode]
                     , target.[ResellerType] = source.[ResellerType]
        WHEN NOT MATCHED BY TARGET THEN
            INSERT
            (
                PreBookId
              , Reservationid
              , Repricerid
              , Createdate
              , Reservationadultcount
              , Prebookadultcount
              , Reservationchildages
              , PrebookChildAges
              , Providers
              , BookingDate
              , ReservationPrice
              , PreBookPrice
              , ProfitAfterCancellation
              , Profit
              , CurrencyFactorToEur
              , ReservationRoomName
              , PreBookRoomName
              , ReservationRoomBoard
              , PreBookRoomBoard
              , ReservationRoomInfo
              , PrebookRoomInfo
              , PreBookRoomIndex
              , MatchedReservationCancellationDate
              , MatchedPreBookCancellationDate
              , MatchedReservationCancellationChargeByPolicy
              , MatchedPreBookCancellationChargeByPolicy
              , IsCancellationPolicyMatched
              , CPStatus
              , CPDaysGain
              , MatchedCancellationPolicyGain
              , Token
              , AvailabilityToken
              , NumberOfRooms
              , ReservationStatus
              , prebooksupplier
              , checkin
              , checkout
              , reservationhotelname
              , prebookhotelname
              , prebookdestination
              , reservationdestination
              , roomType
              , UpdatedOn
              , DiffDays_Optimisation
              , PriceDifferenceValue
              , PriceDifferencePercentage
              , pricedifferencecurrency
              , IsUsePercentage
              , traveldaysmaxsearchindays
              , traveldaysminsearchindays
              , MatchedReservationCancellationChargeByPolicyToEur
              , MatchedPreBookCancellationChargeByPolicytoEur
              , ReservationGiataMappingId
              , SearchGiataMappingId
              , PreBookGiataPropertyName
              , ReservationGiataPropertyName
              , ReservationRoomBoardGroup
              , PrebookRoomBoardGroup
              , ReservationCancellationType
              , PreBookCancellationType
              , CancellationPolicyRemark
              , [ResellerName]
              , [ResellerCode]
              , [ResellerType]
              , PrebookRank
              , PrimaryPrebookId
            )
            VALUES
            (source.id
           , source.Reservationid
           , source.Repricerid
           , source.firstcreatedate
           , source.Reservationadultcount
           , source.Prebookadultcount
           , source.Reservationchildages
           , source.Prebookchildages
           , source.Providers
           , source.BookingDate
           , source.ReservationPrice
           , source.PreBookPrice
           , source.ProfitAfterCancellation
           , source.Profit
           , source.CurrencyFactorToEur
           , source.ReservationRoomName
           , source.PreBookRoomName
           , source.ReservationRoomBoard
           , source.PreBookRoomBoard
           , source.ReservationRoomInfo
           , source.PrebookRoomInfo
           , source.PreBookRoomIndex
           , source.MatchedReservationCancellationDate
           , source.MatchedPreBookCancellationDate
           , source.MatchedReservationCancellationChargeByPolicy
           , source.MatchedPreBookCancellationChargeByPolicy
           , source.IsCancellationPolicyMatched
           , source.CPStatus
           , source.CPDaysGain
           , source.MatchedCancellationPolicyGain
           , source.Token
           , source.AvailabilityToken
           , source.NumberOfRooms
           , source.ReservationStatus
           , isnull(source.prebooksupplier, source.Providers)
           , source.checkin
           , source.checkout
           , source.reservationhotelname
           , source.prebookhotelname
           , source.prebookdestination
           , source.reservationdestination
           , source.roomType
           , source.Createdate
           , source.DiffDays_Optimisation
           , source.PriceDifferenceValue
           , source.PriceDifferencePercentage
           , source.pricedifferencecurrency
           , source.IsUsePercentage
           , source.traveldaysmaxsearchindays
           , source.traveldaysminsearchindays
           , source.MatchedReservationCancellationChargeByPolicyToEur
           , source.MatchedPreBookCancellationChargeByPolicytoEur
           , source.ReservationGiataMappingId
           , source.SearchGiataMappingId
           , source.PreBookGiataPropertyName
           , source.ReservationGiataPropertyName
           , source.ReservationRoomBoardGroup
           , source.PrebookRoomBoardGroup
           , source.ReservationCancellationType
           , source.PreBookCancellationType
           , source.CancellationPolicyRemark
           , source.[ResellerName]
           , source.[ResellerCode]
           , source.[ResellerType]
           , source.PrebookRank
           , source.PrimaryPrebookId
            );

        Select @Repricerid    as Repricerid
             , @ReservationId as ReservationId
             , @@ROWCOUNT     Updated

    --	Select  * from #temp_OrderedLogs
    --Where ReservationId = 597341
    --order by ReservationId

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;

        SELECT @ErrorMessage  = ERROR_MESSAGE()
             , @ErrorSeverity = ERROR_SEVERITY()
             , @ErrorState    = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END