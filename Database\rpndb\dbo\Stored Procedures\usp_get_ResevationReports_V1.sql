﻿/*        
exec dbo.usp_get_ResevationReports_V1            
@RepricerId     = 28 ,        
@ReservationId    = null,        
@ReservationStatus   = null,        
@PreBookFromDate   = null,        
@PreBookToDate    = null,        
@BookingFromDate   = null,        
@BookingToDate    = null,        
@CheckInFromDate   = null,        
@CheckInToDate    = null,        
@ProfitFrom     = null,        
@ProfitTo     = null,        
@PageNumber     = null,        
@PageSize     = null,        
@Providers     = null,        
@CPDaysGain     = null,        
        
@Reservationlive   = null,        
@IsLivePrebook    = null,        
@IsReservationActionTaken   = null,        
@ReportType     = NULL -- 'Prebook','PriceEdgeCase','CancellationEdgeCase','Other'        


--*/
 
--/*
CREATE PROCEDURE [dbo].[usp_get_ResevationReports_V1] 
       
    @RepricerId INT
  , @ReservationId INT = NULL
  , @ReservationStatus VARCHAR(100) = NULL
  , @PreBookFromDate DATETIME = NULL
  , @PreBookToDate DATETIME = NULL
  , @BookingFromDate DATETIME = NULL
  , @BookingToDate DATETIME = NULL
  , @CheckInFromDate DATETIME = NULL
  , @CheckInToDate DATETIME = NULL
  , @ProfitFrom DECIMAL(18, 2) = NULL --1,            
  , @ProfitTo DECIMAL(18, 2) = NULL   --20,            
  , @PageNumber INT = NULL
  , @PageSize INT = NULL
  , @Providers VARCHAR(100) = NULL
  , @CPDaysGain INT = NULL
  , @Reservationlive BIT = NULL
  , @IsLivePrebook BIT = NULL         --,          
  , @IsReservationActionTaken BIT = NULL
  , @ReportType VARCHAR(50) = NULL    -- 'Prebook','PriceEdgeCase','CancellationEdgeCase','Other'        

AS
--*/
/*
	DECLARE        
		@RepricerId INT		= 36
	  , @ReservationId INT = NULL
	  , @ReservationStatus VARCHAR(100) = NULL
	  , @PreBookFromDate DATETIME = NULL
	  , @PreBookToDate DATETIME = NULL
	  , @BookingFromDate DATETIME = NULL
	  , @BookingToDate DATETIME = NULL
	  , @CheckInFromDate DATETIME = NULL
	  , @CheckInToDate DATETIME = NULL
	  , @ProfitFrom DECIMAL(18, 2) = NULL --1,            
	  , @ProfitTo DECIMAL(18, 2) = NULL   --20,            
	  , @PageNumber INT = NULL
	  , @PageSize INT = NULL
	  , @Providers VARCHAR(100) = NULL
	  , @CPDaysGain INT = NULL
	  , @Reservationlive BIT = NULL
	  , @IsLivePrebook BIT = NULL         --,          
	  , @IsReservationActionTaken BIT = NULL
	  , @ReportType VARCHAR(50) = NULL    -- 'Prebook','PriceEdgeCase','CancellationEdgeCase','Other'        
 
--*/
BEGIN

    DECLARE @GETDATE DATETIME = getutcdate()
    SET NOCOUNT ON;
    SET Transaction isolation level read uncommitted;
	PRINT CONVERT(VARCHAR(20), @GETDATE, 113);

    drop table if exists #TempResult;

	CREATE TABLE #TempResult (
	[Id] [bigint] IDENTITY(1,1),
	[RowNumber] [bigint] NULL,
	[rowTimeStamp] [datetime] DEFAULT (getutcdate()),
	[RepricerId] [int] NOT NULL,
	[ReservationId] [int] NOT NULL,
	[ReportType] [varchar](33) NULL,
	[ReservationGiataMappingId] [varchar](200) NULL,
	[SearchGiataMappingId] [varchar](200) NULL,
	[CPStatus] [varchar](50) NULL,
	[CPDaysGain] [int] NULL,
	[MatchedCancellationPolicyGain] [decimal](9, 2) NULL,
	[ReservationStatus] [varchar](200) NULL,
	[NewReservationID] [int] NULL,
	[NewReservationStatus] [varchar](200) NULL,
	[ReservationPrice] [decimal](9, 2) NULL,
	[PrebookPrice] [decimal](9, 2) NULL,
	[Profit] [decimal](9, 2) NULL,
	[ProfitAfterCancellation] [decimal](9, 2) NULL,
	[ProfitPercentage] [decimal](9, 2) NULL,
	[MatchedReservationCancellationChargeByPolicy] [decimal](9, 2) NULL,
	[MatchedPrebookCancellationChargeByPolicy] [decimal](9, 2) NULL,
	[MatchedReservationCancellationDate] [datetime] NULL,
	[MatchedPrebookCancellationDate] [datetime] NULL,
	[IsFutureDate_RES_CP] [int] NULL,
	[IsFutureDate_PRE_CP] [int] NULL,
	[IsWithInBuffer_RES_CP] [int] NULL,
	[IsWithInBuffer_PRE_CP] [int] NULL,
	[BookingDate] [datetime] NULL,
	[ReservationCheckin] [datetime] NULL,
	[ReservationCheckout] [datetime] NULL,
	[FirstPreBookCreateDate] [datetime] NULL,
	[Createdate] [datetime] NULL,
	[PrebookLastUpdateDate] [datetime] NULL,
	[ReservationAdultCount] [int] NULL,
	[PrebookAdultCount] [int] NULL,
	[ReservationChildAges] [varchar](255) NULL,
	[PrebookChildAges] [varchar](255) NULL,
	[ReservationSupplier] [varchar](255) NULL,
	[PrebookSupplier] [varchar](255) NULL,
	[CurrencyfactortoEUR] [decimal](9, 2) NULL,
	[ReservationRoomName] [varchar](MAX) NULL,
	[PrebookRoomName] [varchar](MAX) NULL,
	[ReservationRoomBoard] [varchar](MAX) NULL,
	[PrebookRoomBoard] [varchar](MAX) NULL,
	[ReservationRoomBoardGroup] [varchar](MAX) NULL,
	[PrebookRoomBoardGroup] [varchar](MAX) NULL,
	[ReservationRoomInfo] [varchar](MAX) NULL,
	[PrebookRoomInfo] [varchar](MAX) NULL,
	[PrebookRoomIndex] [varchar](256) NULL,
	[IsCancellationPolicyMatched] [bit] NULL,
	[RoomType] [varchar](256) NULL,
	[Token] [varchar](1) NULL,
	[Availabilitytoken] [varchar](1) NULL,
	[Numberofrooms] [int]  NULL,
	[ReservationDestination] [varchar](256)  NULL,
	[PrebookDestination] [varchar](256)  NULL,
	[ReservationHotelName] [varchar](256)  NULL,
	[PrebookHotelName] [varchar](256)  NULL,
	[IsBookingActionTaken] [int]  NULL,
	[LastActionTakenDate] [datetime]  NULL,
	[ActionId] [int]  NULL,
	[ActionTakenGain] [decimal](9, 2)  NULL,
	[PreBookCurrency] [varchar](3)  NULL,
	[ReservationCurrency] [varchar](3)  NULL,
	[prebookTableId] [bigint] NULL,
	[DiffDayFromLastUpdate] [int]  NULL,
	[IsLivePreBook] [int]  NULL,
	[ReservationStatus_For_IsLivePreBook] [int] NULL,
	[DiffDays_Optimisation] [int]  NULL,
	[PriceDifferenceValue] [decimal](9, 2)  NULL,
	[PriceDifferencePercentage] [decimal](9, 2)  NULL,
	[PriceDifferenceCurrency] [varchar](5)  NULL,
	[IsUsePercentage] [bit]  NULL,
	[TravelDaysMaxSearchInDays] [int] NULL,
	[TravelDaysMinSearchInDays] [int] NULL,
	[DaysLimitCancellationPolicyEdgeCase] [int] NULL,
	[CPDate] [date] NULL,
	[CPBufferDate] [date] NULL,
	[ReservationCancellationType] [varchar](100) NULL,
	[PreBookCancellationType] [varchar](100) NULL,
	[CancellationPolicyRemark] [varchar](100) NULL,
	[ConnectionStatus] [int] NULL,
	[ConnectionStatusDescription] [varchar](500) NULL,
	[ReservationGiataPropertyName] [varchar](256) NULL,
	[SearchGiataPropertyName] [varchar](1000) NULL,
	[ReservationLastActivity] [datetime] NULL,
	[PrebookLastActivity] [datetime] NULL,
	[ReservationCancelledOnDate] [datetime] NULL,
	[PrebookCancelledOnDate] [datetime] NULL,
	[FromCurrency] [nvarchar](6) NULL,
	[ToCurrency] [nvarchar](6) NULL,
	[Factor] [decimal](9, 2) NULL,
	[ProfitInOriginalCurrency] [decimal](17, 2) NULL,
	[ResellerName] [varchar](256) NULL,
	[ResellerCode] [varchar](128) NULL,
	[ResellerType] [varchar](128) NULL,
	[rrd_updatedDate] [datetime] NULL,
	[reservationCpJSON] [nvarchar](max) NULL,
	[prebookCpJSON] [nvarchar](max) NULL

	PRIMARY KEY CLUSTERED 
	(
	[Id] ASC
	)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
	) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]



    BEGIN try
        IF @PageNumber IS NOT NULL
           AND @PageSize IS NOT NULL
        BEGIN
            DECLARE @RowStart INT = (@PageNumber - 1) * @PageSize;
            IF @RowStart < 0
                SET @RowStart = 0;

            INSERT INTO #TempResult
			(
				[ReservationId]
			  , [RepricerId]
			  , [ReportType]
			  , [ReservationGiataMappingId]
			  , [SearchGiataMappingId]
			  , [CPStatus]
			  , [CPDaysGain]
			  , [MatchedCancellationPolicyGain]
			  , [ReservationStatus]
			  , [NewReservationID]
			  , [NewReservationStatus]
			  , [ReservationPrice]
			  , [PrebookPrice]
			  , [Profit]
			  , [ProfitAfterCancellation]
			  , [ProfitPercentage]
			  , [MatchedReservationCancellationChargeByPolicy]
			  , [MatchedPrebookCancellationChargeByPolicy]
			  , [MatchedReservationCancellationDate]
			  , [MatchedPrebookCancellationDate]
			  , [IsFutureDate_RES_CP]
			  , [IsFutureDate_PRE_CP]
			  , [IsWithInBuffer_RES_CP]
			  , [IsWithInBuffer_PRE_CP]
			  , [BookingDate]
			  , [ReservationCheckin]
			  , [ReservationCheckout]
			  , [FirstPreBookCreateDate]
			  , [Createdate]
			  , [PrebookLastUpdateDate]
			  , [ReservationAdultCount]
			  , [PrebookAdultCount]
			  , [ReservationChildAges]
			  , [PrebookChildAges]
			  , [ReservationSupplier]
			  , [PrebookSupplier]
			  , [CurrencyfactortoEUR]
			  , [ReservationRoomName]
			  , [PrebookRoomName]
			  , [ReservationRoomBoard]
			  , [PrebookRoomBoard]
			  , [ReservationRoomBoardGroup]
			  , [PrebookRoomBoardGroup]
			  , [ReservationRoomInfo]
			  , [PrebookRoomInfo]
			  , [PrebookRoomIndex]
			  , [IsCancellationPolicyMatched]
			  , [RoomType]
			  , [Token]
			  , [Availabilitytoken]
			  , [Numberofrooms]
			  , [ReservationDestination]
			  , [PrebookDestination]
			  , [ReservationHotelName]
			  , [PrebookHotelName]
			  , [IsBookingActionTaken]
			  , [LastActionTakenDate]
			  , [ActionId]
			  , [ActionTakenGain]
			  , [PreBookCurrency]
			  , [ReservationCurrency]
			  , [prebookTableId]
			  , [DiffDayFromLastUpdate]
			  , [IsLivePreBook]
			  , [ReservationStatus_For_IsLivePreBook]
			  , [DiffDays_Optimisation]
			  , [PriceDifferenceValue]
			  , [PriceDifferencePercentage]
			  , [PriceDifferenceCurrency]
			  , [IsUsePercentage]
			  , [TravelDaysMaxSearchInDays]
			  , [TravelDaysMinSearchInDays]
			  , [DaysLimitCancellationPolicyEdgeCase]
			  , [CPDate]
			  , [CPBufferDate]
			  , [ReservationCancellationType]
			  , [PreBookCancellationType]
			  , [CancellationPolicyRemark]
			  , [ConnectionStatus]
			  , [ConnectionStatusDescription]
			  , [ReservationGiataPropertyName]
			  , [SearchGiataPropertyName]
			  , [ReservationLastActivity]
			  , [PrebookLastActivity]
			  , [ReservationCancelledOnDate]
			  , [PrebookCancelledOnDate]
			  , [FromCurrency]
			  , [ToCurrency]
			  , [Factor]
			  , [ProfitInOriginalCurrency]
			  , [ResellerName]
			  , [ResellerCode]
			  , [ResellerType]
			  , [rrd_updatedDate]
			  , [reservationCpJSON]
			  , [prebookCpJSON]
			  , RowNumber
    )
            SELECT DISTINCT
                	[ReservationId]
			  , [RepricerId]
			  , [ReportType]
			  , [ReservationGiataMappingId]
			  , [SearchGiataMappingId]
			  , [CPStatus]
			  , [CPDaysGain]
			  , [MatchedCancellationPolicyGain]
			  , [ReservationStatus]
			  , [NewReservationID]
			  , [NewReservationStatus]
			  , [ReservationPrice]
			  , [PrebookPrice]
			  , [Profit]
			  , [ProfitAfterCancellation]
			  , [ProfitPercentage]
			  , [MatchedReservationCancellationChargeByPolicy]
			  , [MatchedPrebookCancellationChargeByPolicy]
			  , [MatchedReservationCancellationDate]
			  , [MatchedPrebookCancellationDate]
			  , [IsFutureDate_RES_CP]
			  , [IsFutureDate_PRE_CP]
			  , [IsWithInBuffer_RES_CP]
			  , [IsWithInBuffer_PRE_CP]
			  , [BookingDate]
			  , [ReservationCheckin]
			  , [ReservationCheckout]
			  , [FirstPreBookCreateDate]
			  , [Createdate]
			  , [PrebookLastUpdateDate]
			  , [ReservationAdultCount]
			  , [PrebookAdultCount]
			  , [ReservationChildAges]
			  , [PrebookChildAges]
			  , [ReservationSupplier]
			  , [PrebookSupplier]
			  , [CurrencyfactortoEUR]
			  , [ReservationRoomName]
			  , [PrebookRoomName]
			  , [ReservationRoomBoard]
			  , [PrebookRoomBoard]
			  , [ReservationRoomBoardGroup]
			  , [PrebookRoomBoardGroup]
			  , [ReservationRoomInfo]
			  , [PrebookRoomInfo]
			  , [PrebookRoomIndex]
			  , [IsCancellationPolicyMatched]
			  , [RoomType]
			  , [Token]
			  , [Availabilitytoken]
			  , [Numberofrooms]
			  , [ReservationDestination]
			  , [PrebookDestination]
			  , [ReservationHotelName]
			  , [PrebookHotelName]
			  , [IsBookingActionTaken]
			  , [LastActionTakenDate]
			  , [ActionId]
			  , [ActionTakenGain]
			  , [PreBookCurrency]
			  , [ReservationCurrency]
			  , [prebookTableId]
			  , [DiffDayFromLastUpdate]
			  , [IsLivePreBook]
			  , [ReservationStatus_For_IsLivePreBook]
			  , [DiffDays_Optimisation]
			  , [PriceDifferenceValue]
			  , [PriceDifferencePercentage]
			  , [PriceDifferenceCurrency]
			  , [IsUsePercentage]
			  , [TravelDaysMaxSearchInDays]
			  , [TravelDaysMinSearchInDays]
			  , [DaysLimitCancellationPolicyEdgeCase]
			  , [CPDate]
			  , [CPBufferDate]
			  , [ReservationCancellationType]
			  , [PreBookCancellationType]
			  , [CancellationPolicyRemark]
			  , [ConnectionStatus]
			  , [ConnectionStatusDescription]
			  , [ReservationGiataPropertyName]
			  , [SearchGiataPropertyName]
			  , [ReservationLastActivity]
			  , [PrebookLastActivity]
			  , [ReservationCancelledOnDate]
			  , [PrebookCancelledOnDate]
			  , [FromCurrency]
			  , [ToCurrency]
			  , [Factor]
			  , [ProfitInOriginalCurrency]
			  , [ResellerName]
			  , [ResellerCode]
			  , [ResellerType]
			  , [rrd_updatedDate]
			  , [reservationCpJSON]
			  , [prebookCpJSON]
			  , RowNumber
            FROM dbo.tbl_vw_ResevationReports as v
            WHERE repricerid = @RepricerId
                  AND (
                          @ReportType IS NULL
                          OR ReportType = @ReportType
                      )
                  AND (
                          @ReservationId IS NULL
                          OR reservationid = @ReservationId
                      )
                  AND (
                          @ReservationStatus IS NULL
                          OR reservationstatus = @ReservationStatus
                      )
                  AND (
                          @PreBookFromDate IS NULL
                          OR createdate >= @PreBookFromDate
                      )
                  AND (
                          @PreBookToDate IS NULL
                          OR createdate <= @PreBookToDate
                      )
                  AND (
                          @BookingFromDate IS NULL
                          OR bookingdate >= @BookingFromDate
                      )
                  AND (
                          @BookingToDate IS NULL
                          OR bookingdate <= @BookingToDate
                      )
                  AND (
                          @CheckInFromDate IS NULL
                          OR reservationcheckin >= @CheckInFromDate
                      )
                  AND (
                          @CheckInToDate IS NULL
                          OR reservationcheckin <= @CheckInToDate
                      )
                  AND (
                          @ProfitFrom IS NULL
                          OR profit >= @ProfitFrom
                      )
                  AND (
                          @ProfitTo IS NULL
                          OR profit <= @ProfitTo
                      )
                  AND (
                          @Providers IS NULL
                          OR reservationSupplier = @Providers
                      )
                  AND (
                          @CPDaysGain IS NULL
                          OR cpdaysgain > @CPDaysGain
                      )
                  AND (
                          @Reservationlive IS NULL
                          OR reservationstatus = @Reservationlive
                      )
                  AND (
                          @IsLivePrebook IS NULL
                          OR ISNULL(IsLivePreBook, 1) = CAST(@IsLivePrebook AS INT)
                      )
                  AND (
                          @IsReservationActionTaken IS NULL
                          OR isbookingactiontaken = Cast(@IsReservationActionTaken AS INT)
                      )
            ORDER BY createdate DESC offset @RowStart rows FETCH next @PageSize rows only
            OPTION (recompile);

            PRINT 1;
        END
        ELSE
        BEGIN
           INSERT INTO #TempResult
			(
				[ReservationId]
			  , [RepricerId]
			  , [ReportType]
			  , [ReservationGiataMappingId]
			  , [SearchGiataMappingId]
			  , [CPStatus]
			  , [CPDaysGain]
			  , [MatchedCancellationPolicyGain]
			  , [ReservationStatus]
			  , [NewReservationID]
			  , [NewReservationStatus]
			  , [ReservationPrice]
			  , [PrebookPrice]
			  , [Profit]
			  , [ProfitAfterCancellation]
			  , [ProfitPercentage]
			  , [MatchedReservationCancellationChargeByPolicy]
			  , [MatchedPrebookCancellationChargeByPolicy]
			  , [MatchedReservationCancellationDate]
			  , [MatchedPrebookCancellationDate]
			  , [IsFutureDate_RES_CP]
			  , [IsFutureDate_PRE_CP]
			  , [IsWithInBuffer_RES_CP]
			  , [IsWithInBuffer_PRE_CP]
			  , [BookingDate]
			  , [ReservationCheckin]
			  , [ReservationCheckout]
			  , [FirstPreBookCreateDate]
			  , [Createdate]
			  , [PrebookLastUpdateDate]
			  , [ReservationAdultCount]
			  , [PrebookAdultCount]
			  , [ReservationChildAges]
			  , [PrebookChildAges]
			  , [ReservationSupplier]
			  , [PrebookSupplier]
			  , [CurrencyfactortoEUR]
			  , [ReservationRoomName]
			  , [PrebookRoomName]
			  , [ReservationRoomBoard]
			  , [PrebookRoomBoard]
			  , [ReservationRoomBoardGroup]
			  , [PrebookRoomBoardGroup]
			  , [ReservationRoomInfo]
			  , [PrebookRoomInfo]
			  , [PrebookRoomIndex]
			  , [IsCancellationPolicyMatched]
			  , [RoomType]
			  , [Token]
			  , [Availabilitytoken]
			  , [Numberofrooms]
			  , [ReservationDestination]
			  , [PrebookDestination]
			  , [ReservationHotelName]
			  , [PrebookHotelName]
			  , [IsBookingActionTaken]
			  , [LastActionTakenDate]
			  , [ActionId]
			  , [ActionTakenGain]
			  , [PreBookCurrency]
			  , [ReservationCurrency]
			  , [prebookTableId]
			  , [DiffDayFromLastUpdate]
			  , [IsLivePreBook]
			  , [ReservationStatus_For_IsLivePreBook]
			  , [DiffDays_Optimisation]
			  , [PriceDifferenceValue]
			  , [PriceDifferencePercentage]
			  , [PriceDifferenceCurrency]
			  , [IsUsePercentage]
			  , [TravelDaysMaxSearchInDays]
			  , [TravelDaysMinSearchInDays]
			  , [DaysLimitCancellationPolicyEdgeCase]
			  , [CPDate]
			  , [CPBufferDate]
			  , [ReservationCancellationType]
			  , [PreBookCancellationType]
			  , [CancellationPolicyRemark]
			  , [ConnectionStatus]
			  , [ConnectionStatusDescription]
			  , [ReservationGiataPropertyName]
			  , [SearchGiataPropertyName]
			  , [ReservationLastActivity]
			  , [PrebookLastActivity]
			  , [ReservationCancelledOnDate]
			  , [PrebookCancelledOnDate]
			  , [FromCurrency]
			  , [ToCurrency]
			  , [Factor]
			  , [ProfitInOriginalCurrency]
			  , [ResellerName]
			  , [ResellerCode]
			  , [ResellerType]
			  , [rrd_updatedDate]
			  , [reservationCpJSON]
			  , [prebookCpJSON]
			  , RowNumber
    )
            SELECT DISTINCT
                	[ReservationId]
			  , [RepricerId]
			  , [ReportType]
			  , [ReservationGiataMappingId]
			  , [SearchGiataMappingId]
			  , [CPStatus]
			  , [CPDaysGain]
			  , [MatchedCancellationPolicyGain]
			  , [ReservationStatus]
			  , [NewReservationID]
			  , [NewReservationStatus]
			  , [ReservationPrice]
			  , [PrebookPrice]
			  , [Profit]
			  , [ProfitAfterCancellation]
			  , [ProfitPercentage]
			  , [MatchedReservationCancellationChargeByPolicy]
			  , [MatchedPrebookCancellationChargeByPolicy]
			  , [MatchedReservationCancellationDate]
			  , [MatchedPrebookCancellationDate]
			  , [IsFutureDate_RES_CP]
			  , [IsFutureDate_PRE_CP]
			  , [IsWithInBuffer_RES_CP]
			  , [IsWithInBuffer_PRE_CP]
			  , [BookingDate]
			  , [ReservationCheckin]
			  , [ReservationCheckout]
			  , [FirstPreBookCreateDate]
			  , [Createdate]
			  , [PrebookLastUpdateDate]
			  , [ReservationAdultCount]
			  , [PrebookAdultCount]
			  , [ReservationChildAges]
			  , [PrebookChildAges]
			  , [ReservationSupplier]
			  , [PrebookSupplier]
			  , [CurrencyfactortoEUR]
			  , [ReservationRoomName]
			  , [PrebookRoomName]
			  , [ReservationRoomBoard]
			  , [PrebookRoomBoard]
			  , [ReservationRoomBoardGroup]
			  , [PrebookRoomBoardGroup]
			  , [ReservationRoomInfo]
			  , [PrebookRoomInfo]
			  , [PrebookRoomIndex]
			  , [IsCancellationPolicyMatched]
			  , [RoomType]
			  , [Token]
			  , [Availabilitytoken]
			  , [Numberofrooms]
			  , [ReservationDestination]
			  , [PrebookDestination]
			  , [ReservationHotelName]
			  , [PrebookHotelName]
			  , [IsBookingActionTaken]
			  , [LastActionTakenDate]
			  , [ActionId]
			  , [ActionTakenGain]
			  , [PreBookCurrency]
			  , [ReservationCurrency]
			  , [prebookTableId]
			  , [DiffDayFromLastUpdate]
			  , [IsLivePreBook]
			  , [ReservationStatus_For_IsLivePreBook]
			  , [DiffDays_Optimisation]
			  , [PriceDifferenceValue]
			  , [PriceDifferencePercentage]
			  , [PriceDifferenceCurrency]
			  , [IsUsePercentage]
			  , [TravelDaysMaxSearchInDays]
			  , [TravelDaysMinSearchInDays]
			  , [DaysLimitCancellationPolicyEdgeCase]
			  , [CPDate]
			  , [CPBufferDate]
			  , [ReservationCancellationType]
			  , [PreBookCancellationType]
			  , [CancellationPolicyRemark]
			  , [ConnectionStatus]
			  , [ConnectionStatusDescription]
			  , [ReservationGiataPropertyName]
			  , [SearchGiataPropertyName]
			  , [ReservationLastActivity]
			  , [PrebookLastActivity]
			  , [ReservationCancelledOnDate]
			  , [PrebookCancelledOnDate]
			  , [FromCurrency]
			  , [ToCurrency]
			  , [Factor]
			  , [ProfitInOriginalCurrency]
			  , [ResellerName]
			  , [ResellerCode]
			  , [ResellerType]
			  , [rrd_updatedDate]
			  , [reservationCpJSON]
			  , [prebookCpJSON]
			  , RowNumber
            FROM dbo.tbl_vw_ResevationReports as v
            WHERE repricerid = @RepricerId
                  AND (
                          @ReportType IS NULL
                          OR ReportType = @ReportType
                      )
                  AND (
                          @ReservationId IS NULL
                          OR reservationid = @ReservationId
                      )
                  AND (
                          @ReservationStatus IS NULL
                          OR reservationstatus = @ReservationStatus
                      )
                  AND (
                          @PreBookFromDate IS NULL
                          OR createdate >= @PreBookFromDate
                      )
                  AND (
                          @PreBookToDate IS NULL
                          OR createdate <= @PreBookToDate
                      )
                  AND (
                          @BookingFromDate IS NULL
                          OR bookingdate >= @BookingFromDate
                      )
                  AND (
                          @BookingToDate IS NULL
                          OR bookingdate <= @BookingToDate
                      )
                  AND (
                          @CheckInFromDate IS NULL
                          OR reservationcheckin >= @CheckInFromDate
                      )
                  AND (
                          @CheckInToDate IS NULL
                          OR reservationcheckin <= @CheckInToDate
                      )
                  AND (
                          @ProfitFrom IS NULL
                          OR profit >= @ProfitFrom
                      )
                  AND (
                          @ProfitTo IS NULL
                          OR profit <= @ProfitTo
                      )
                  AND (
                          @Providers IS NULL
                          OR reservationSupplier = @Providers
                      )
                  AND (
                          @CPDaysGain IS NULL
                          OR cpdaysgain > @CPDaysGain
                      )
                  AND (
                          @Reservationlive IS NULL
                          OR reservationstatus = @Reservationlive
                      )
                  AND (
                          @IsLivePrebook IS NULL
                          OR ISNULL(IsLivePreBook, 1) = CAST(@IsLivePrebook AS INT)
                      )
                  AND (
                          @IsReservationActionTaken IS NULL
                          OR isbookingactiontaken = Cast(@IsReservationActionTaken AS INT)
                      )
            ORDER BY createdate DESC
            OPTION (recompile);

            PRINT 2;
        END
    END try
    BEGIN catch
        SELECT Error_number()  AS errornumber
             , Error_message() AS errormessage;

        PRINT 3;
    END catch

    Select t.*
	, bat.CustomerAmount                                                                                                      as CustomerAmount
             , bat.CustomerCurrency                                                                                                    as CustomerCurrency
             , bat.CustomerCurrencyFactor                                                                                              as CustomerCurrencyFactor
             , bat.GainAmountInOriginalCurrency                                                                                        as OptimizationAmount
             , bat.OriginalCurrency                                                                                                    as OptimizationCurrency
             , bat.NewBookingPrice                                                                                                     as EuroAmount
             , bat.GainConvertedCurrency                                                                                               as EuroCurrency
             , bat.CurrencyFactor                                                                                                      as EuroCurrencyFactor
			 , ROW_NUMBER() Over (
								PARTITION BY t.RepricerId, t.ReservationId
								ORDER BY t.RepricerId  
                                     , t.CreateDate DESC  
                             ) AS PrebookRank 
    from #TempResult t
	LEFT JOIN dbo.BookingActionsTaken as bat
			on bat.RepricerId = t.RePricerId
			AND bat.ReservationId = t.ReservationId
			AND bat.ActionId = t.ActionId
    Order by t.Createdate DESC;

    SELECT Actionid
         , Actionname
    FROM dbo.actiondetail;

    Select t1.*
    from #TempResult                    v
        Inner join dbo.GiataRoomDetails t1
            on v.ReservationGiataMappingId = t1.GIATAMappingID
    Union
    Select t1.*
    from #TempResult                    v
        Inner join dbo.GiataRoomDetails t1
            on v.SearchGiataMappingId = t1.GIATAMappingID


    Select @RepricerId               as RepricerId
         , @ReportType               as ReportType
         , @ReservationId            as ReservationId
         , @ReservationStatus        as ReservationStatus
         , @PreBookFromDate          as PreBookFromDate
         , @PreBookToDate            as PreBookToDate
         , @BookingFromDate          as BookingFromDate
         , @BookingToDate            as BookingToDate
         , @CheckInFromDate          as CheckInFromDate
         , @CheckInToDate            as CheckInToDate
         , @ProfitFrom               as ProfitFrom
         , @ProfitTo                 as ProfitTo
         , @PageNumber               as PageNumber
         , @PageSize                 as PageSize
         , @Providers                as Providers
         , @CPDaysGain               as CPDaysGain
         , @Reservationlive          as Reservationlive
         , @IsLivePrebook            as IsLivePrebook
         , @IsReservationActionTaken as IsReservationActionTaken


END