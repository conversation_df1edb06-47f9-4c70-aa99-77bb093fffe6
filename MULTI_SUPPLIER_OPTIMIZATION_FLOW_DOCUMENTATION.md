# Multi-Supplier Optimization Flow - Complete Documentation

## 📋 Table of Contents
1. [Executive Summary](#executive-summary)
2. [Business Overview](#business-overview)
3. [Technical Architecture](#technical-architecture)
4. [Detailed Flow Analysis](#detailed-flow-analysis)
5. [Key Components](#key-components)
6. [Configuration Requirements](#configuration-requirements)
7. [Error Handling & Monitoring](#error-handling--monitoring)
8. [Performance Considerations](#performance-considerations)
9. [Troubleshooting Guide](#troubleshooting-guide)
10. [API Reference](#api-reference)

---

## 🎯 Executive Summary

The Multi-Supplier Optimization Flow is a sophisticated hotel booking re-optimization system that searches across multiple suppliers (excluding the original booking supplier) to find better rates while maintaining equivalent room types and cancellation policies. This system generates revenue through a 50% commission on savings achieved.

### Key Benefits
- **Revenue Generation**: 50% commission on all savings
- **Customer Satisfaction**: Better rates with same/better conditions
- **Risk Mitigation**: Maintains equivalent cancellation policies
- **Quality Assurance**: Manual approval for cross-supplier bookings

### Success Metrics
- **Optimization Rate**: % of bookings successfully optimized
- **Average Savings**: Mean savings per optimized booking
- **Processing Time**: Time from trigger to completion
- **Error Rate**: Failed optimization attempts

---

## 🏢 Business Overview

### What is Multi-Supplier Optimization?

Multi-supplier optimization is the process of finding better hotel rates by searching across different booking suppliers while ensuring:
- **Same Hotel**: Identical property
- **Equivalent Rooms**: Same or better room category via Giata mapping
- **Better/Same Cancellation**: Equal or more flexible cancellation terms
- **Significant Savings**: Meets minimum profit thresholds

### Business Flow Diagram

```mermaid
graph TD
    A[Customer Books Hotel] --> B[Booking Imported to System]
    B --> C[Multi-Supplier Search Triggered]
    C --> D{Better Rate Found?}
    D -->|Yes| E[Create Prebook Option]
    D -->|No| F[No Action Required]
    E --> G[Dry Run Validation]
    G --> H{Expected Gain Above Threshold?}
    H -->|Yes| I[Email Alert to Operations Team]
    H -->|No| J[Log for Analysis]
    I --> K[Manual Review & Approval]
    K --> L{Approve Optimization?}
    L -->|Yes| M[Execute Rebooking]
    L -->|No| N[Maintain Original Booking]
    M --> O[Customer Notification]
    O --> P[Commission Calculation - 50% of Savings]
```

### Revenue Model

| Component | Description | Example |
|-----------|-------------|---------|
| Original Booking | Customer's initial reservation | €500 |
| Optimized Rate | Better rate found | €400 |
| Total Savings | Difference | €100 |
| Platform Commission | 50% of savings | €50 |
| Customer Benefit | Remaining savings | €50 |

---

## 🏗️ Technical Architecture

### System Components Overview

```mermaid
graph TB
    subgraph "External Systems"
        IRIX[IRIX API]
        GIATA[Giata Mapping Service]
        SUPPLIERS[Hotel Suppliers]
    end
    
    subgraph "Core Services"
        MSS[SupplierSearchService]
        SH[SearchHelper]
        OS[OptimizationService]
        DRS[DryRunService]
        ES[EmailService]
    end
    
    subgraph "Data Layer"
        DB[(Database)]
        CACHE[Redis Cache]
        LOGS[Azure Logs]
    end
    
    subgraph "Configuration"
        CC[Client Config]
        SC[Supplier Config]
        TC[Threshold Config]
    end
    
    MSS --> SH
    MSS --> OS
    MSS --> DRS
    MSS --> ES
    SH --> IRIX
    MSS --> GIATA
    IRIX --> SUPPLIERS
    MSS --> DB
    MSS --> CACHE
    MSS --> LOGS
    MSS --> CC
    MSS --> SC
    MSS --> TC
```

### Key Technical Files

| File | Purpose | Key Methods |
|------|---------|-------------|
| `SupplierSearchService.cs` | Main orchestrator | `_1_PrebookAndOptimize_MultiSupplier` |
| `SearchHelper.cs` | Search operations | `SearchSync`, `PrebookAttempt` |
| `OptimizationService.cs` | Final booking optimization | `_3_OptimizeBookingApiIRIX` |
| `DryRunOptimizationService.cs` | Validation service | `_2_DryRunOptimizationApiIRIX` |
| `GiataService.cs` | Room mapping | `GiataApiCall` |

---

## 🔄 Detailed Flow Analysis

### Phase 1: Initialization & Validation (Lines 194-276)

**Purpose**: Validate client configuration and permissions

**Business Logic**:
- Verify client has multi-supplier enabled
- Check if optimization is allowed
- Validate manual trigger requirements

**Technical Implementation**:
```csharp
// Key validation checks
if ((repricerSchedules?.IsMultiSupplierEnabled ?? false) == false
    || (repricerSchedules?.IsJobsEnable ?? false) == false
    || (repricerSchedules?.IsActive ?? false) == false
    || (string.IsNullOrWhiteSpace(supplierName) && isOptimizeTriggeredManually))
{
    // Return error - client not configured for multi-supplier
}
```

**Business Impact**: Prevents unauthorized or misconfigured optimization attempts

### Phase 2: Data Loading (Lines 245-359)

**Purpose**: Load all required data for processing

**Key Data Elements**:
- **Reservations**: Bookings eligible for optimization
- **Allowed Suppliers**: Suppliers client can use
- **Cancellation Policies**: Original booking terms
- **Prebook Counts**: Historical attempt tracking

**Performance Considerations**:
- Parallel data loading where possible
- Caching of frequently accessed data
- Filtering early to reduce processing load

### Phase 3: Supplier Filtering & Batch Processing (Lines 330-682)

**Purpose**: Identify alternative suppliers and process in batches

**Business Logic**:
```mermaid
graph LR
    A[All Allowed Suppliers] --> B[Remove Original Supplier]
    B --> C[Create Batches of 5]
    C --> D[Process Each Batch]
    D --> E[Search for Alternatives]
```

**Why Batches of 5?**
- **Performance**: Prevents API overload
- **Reliability**: Reduces timeout risks
- **Resource Management**: Controls concurrent connections

### Phase 4: Giata Room Mapping (Lines 744-772)

**Purpose**: Map rooms across different suppliers

**Business Importance**: 
- Ensures customer gets equivalent room
- Prevents downgrades or mismatches
- Critical for customer satisfaction

**Technical Process**:
```csharp
giatadatafororiginalreservation = _giataService.GiataApiCall(
    repricerId, 
    reservation.ReservationId.ToString(), 
    hotelName, 
    prebookcriteriaroom.RoomName, 
    Destinations, 
    reservationsupplier
);
```

**Failure Handling**: If Giata mapping fails, optimization stops to prevent room mismatches

### Phase 5: Offer Filtering & Matching (Lines 798-842)

**Purpose**: Find and validate suitable alternatives

**Matching Criteria**:
1. **Room Equivalency**: Via Giata mapping
2. **Board Basis**: Meal plan compatibility
3. **Price Improvement**: Must meet thresholds
4. **Availability**: Must be bookable

**Technical Implementation**:
- Parallel processing for performance
- Similarity scoring for room matching
- Price validation at multiple levels

### Phase 6: Price Validation & Threshold Checking (Lines 858-1009)

**Purpose**: Ensure savings meet business requirements

**Threshold Types**:
- **Percentage Based**: e.g., 5% minimum savings
- **Fixed Amount**: e.g., €50 minimum savings
- **Currency Conversion**: Handled automatically

**Business Rules**:
```csharp
if (extraClientConfig?.IsUsePercentage == true)
{
    isPriceThreshold = profitperc >= extraClientConfig.PriceDifferencePercentage;
}
else
{
    isPriceThreshold = profit >= pricethresholdInOriginalReservationCurrency;
}
```

### Phase 7: Prebook Creation (Lines 1023-1070)

**Purpose**: Create tentative booking to lock rate

**Business Significance**:
- Secures the better rate temporarily
- Allows validation without commitment
- Provides accurate final pricing

**Technical Process**:
- Call IRIX API with prebook request
- Validate returned pricing
- Store prebook details for later optimization

### Phase 8: Cancellation Policy Validation (Lines 1074-1136)

**Purpose**: Ensure new booking has equal/better cancellation terms

**Policy Categories**:
- **Loose**: More flexible than original
- **TightWithBuffer**: Slightly less flexible but acceptable
- **CancellationChargesApplicable**: Charges apply but still beneficial

**Business Protection**: Prevents customer from getting worse cancellation terms

### Phase 9: Dry Run Execution (Lines 1174-1288)

**Purpose**: Final validation before optimization

**What Dry Run Validates**:
- Final expected savings
- Booking feasibility
- System readiness

**Business Process**:
```mermaid
graph TD
    A[Dry Run Executed] --> B{Expected Gain > Threshold?}
    B -->|Yes| C[Send Email Alert]
    B -->|No| D[Log for Analysis]
    C --> E[Await Manual Approval]
    E --> F[Proceed to Optimization]
```

### Phase 10: Manual Approval & Final Optimization (Lines 1290-1397)

**Purpose**: Human validation before final booking change

**Why Manual Approval?**
- **Risk Management**: Cross-supplier bookings carry higher risk
- **Quality Control**: Human oversight ensures customer satisfaction
- **Business Rules**: Complex scenarios need human judgment

**Approval Process**:
1. Operations team receives email alert
2. Reviews booking details and savings
3. Validates room equivalency
4. Approves or rejects optimization
5. System executes final rebooking if approved

---

## 🔧 Key Components

### 1. SupplierSearchService

**Primary Orchestrator** for the entire multi-supplier flow

**Key Responsibilities**:
- Coordinate all phases of optimization
- Manage parallel processing
- Handle error scenarios
- Update booking statuses

**Critical Methods**:
- `_1_PrebookAndOptimize_MultiSupplier`: Main entry point
- `_2_GetOfferInfoListAsync`: Offer filtering
- `UpdateMatchingRoomOffer`: Room matching logic

### 2. SearchHelper

**Search Operations Manager**

**Key Functions**:
- Create search criteria for multiple suppliers
- Execute searches via IRIX API
- Handle search response processing

**Multi-Supplier Specifics**:
```csharp
// Creates criteria for multiple suppliers
criteria = _searchserviceHelper.CreateCriteria(
    reservation,
    roomReservation,
    allowedProviders, // Multiple suppliers
    ismultiprebook: true
);
```

### 3. GiataService

**Room Mapping Service**

**Business Importance**: Ensures room equivalency across suppliers

**Technical Process**:
- Maps room names to standardized categories
- Provides confidence scores for matches
- Handles multiple languages and naming conventions

### 4. OptimizationService

**Final Booking Execution**

**Responsibilities**:
- Execute final rebooking
- Handle booking confirmations
- Manage booking failures
- Update customer records

### 5. DryRunOptimizationService

**Validation Service**

**Purpose**: Validate optimization feasibility without commitment

**Business Value**: Prevents failed optimizations and customer disruption

---

## ⚙️ Configuration Requirements

### Client Configuration

**Required Settings**:
```json
{
  "IsMultiSupplierEnabled": true,
  "IsJobsEnable": true,
  "IsActive": true,
  "IsOptimizationAllowed": true,
  "OptimizationType": "Manual",
  "PriceDifferenceValue": 50.0,
  "PriceDifferencePercentage": 5.0,
  "IsUsePercentage": false,
  "traveldaysmaxsearchindays": 60,
  "traveldaysminsearchindays": 10
}
```

### Supplier Configuration

**Allowed Providers Setup**:
- List of suppliers client can use for rebooking
- Excludes original booking supplier automatically
- Configurable per client

**Example**:
```json
{
  "allowedProviders": [
    "Booking.com",
    "Expedia",
    "Hotels.com",
    "Agoda"
  ]
}
```

### Threshold Configuration

**Price Thresholds**:
- Minimum savings amount or percentage
- Currency-specific settings
- Client-specific overrides

**Cancellation Policy Thresholds**:
- Acceptable policy degradation levels
- Buffer periods for policy comparison
- Risk tolerance settings

---

## 🚨 Error Handling & Monitoring

### Common Error Scenarios

| Error Type | Cause | Business Impact | Resolution |
|------------|-------|-----------------|------------|
| `GiataMappingNotFound` | Room mapping failed | No optimization possible | Manual room mapping required |
| `NoMatchingOffers` | No suitable alternatives | No savings opportunity | Normal business outcome |
| `PriceThresholdCheckFailed` | Savings below threshold | Optimization not worthwhile | Adjust thresholds if needed |
| `OptimizationNotAllowed` | Client restrictions | Cannot proceed | Check client configuration |
| `SupplierNotAllowed` | Supplier not in allowed list | Security/contract issue | Update supplier configuration |

### Monitoring & Alerting

**Key Metrics to Monitor**:
- Optimization success rate
- Average processing time
- Error frequency by type
- Revenue generated

**Alert Thresholds**:
- Error rate > 10%
- Processing time > 5 minutes
- Giata mapping failures > 5%

### Logging Strategy

**Log Levels**:
- **INFO**: Normal flow progression
- **WARN**: Recoverable issues
- **ERROR**: Failed optimizations
- **DEBUG**: Detailed troubleshooting

**Key Log Points**:
- Phase transitions
- External API calls
- Decision points
- Error conditions

---

## ⚡ Performance Considerations

### Parallel Processing

**Configuration**:
- Default: 50 concurrent reservations
- Single reservation: Sequential processing
- Batch size: 5 suppliers per batch

**Optimization Strategies**:
- Early termination on timeout
- Resource pooling for API calls
- Caching of frequently accessed data

### Timeout Management

**Timeout Settings**:
- Offer filtering: 5 minutes
- Manual trigger: 5 minutes
- Automatic trigger: 30 seconds
- API calls: 30 seconds each

### Memory Management

**Large Dataset Handling**:
- Streaming for large result sets
- Garbage collection optimization
- Connection pooling

### Caching Strategy

**Cache Layers**:
- **Redis**: Session and temporary data
- **Memory**: Frequently accessed configuration
- **Database**: Persistent optimization results

---

## 🔍 Troubleshooting Guide

### Performance Issues

**Symptoms**: Slow processing, timeouts
**Diagnosis**:
1. Check parallel processing settings
2. Monitor API response times
3. Analyze database query performance
4. Review memory usage

**Solutions**:
- Adjust concurrency limits
- Optimize database queries
- Implement additional caching
- Scale infrastructure

### Optimization Failures

**Symptoms**: High error rates, no optimizations found
**Diagnosis**:
1. Review client configuration
2. Check supplier availability
3. Validate Giata mapping coverage
4. Analyze price thresholds

**Solutions**:
- Update client settings
- Add more suppliers
- Improve room mapping
- Adjust thresholds

### Data Quality Issues

**Symptoms**: Incorrect room matches, policy mismatches
**Diagnosis**:
1. Validate Giata mapping accuracy
2. Check cancellation policy parsing
3. Review room categorization

**Solutions**:
- Update Giata mappings
- Improve policy parsing logic
- Enhance room matching algorithms

---

## 📚 API Reference

### Main Entry Point

```csharp
Task<PreBookResults> _1_PrebookAndOptimize_MultiSupplier(
    int repricerId,
    int? reservationId = 0,
    bool isUpdateDB = true,
    bool isMultiSupplier = true,
    string supplierName = null,
    bool isOptimizeTriggeredManually = false,
    int totalItems = 0,
    int currentItem = 0
)
```

**Parameters**:
- `repricerId`: Client identifier
- `reservationId`: Specific reservation (0 for all)
- `isUpdateDB`: Whether to persist results
- `supplierName`: Specific supplier for manual triggers
- `isOptimizeTriggeredManually`: Manual vs automatic trigger

### Response Structure

```csharp
public class PreBookResults
{
    public List<PreBookResponseResult> PreBookResponseResults { get; set; }
    public Error Error { get; set; }
}

public class PreBookResponseResult
{
    public int RepricerId { get; set; }
    public int ReservationId { get; set; }
    public bool IsPrebookSucess { get; set; }
    public bool IsOptimized { get; set; }
    public bool IsExpectedGainAboveThreshold { get; set; }
    public OptimizationOptimizationBooking OptimizableStatus { get; set; }
    public Dictionary<int, string> Status { get; set; }
}
```

### Configuration APIs

**Client Configuration**:
```csharp
RepricerClientDetail GetRePricerDetail(int repricerId)
ClientEmail GetClientEmail(int repricerId)
```

**Supplier Configuration**:
```csharp
Task<List<string>> GetAllowedProviders(int repricerId)
```

---

## 📈 Business Metrics & KPIs

### Revenue Metrics

| Metric | Calculation | Target |
|--------|-------------|--------|
| Total Revenue | Sum of all commissions | Monthly targets |
| Average Commission | Total revenue / optimizations | €25+ per optimization |
| Revenue per Client | Client-specific revenue | Client SLA targets |

### Operational Metrics

| Metric | Calculation | Target |
|--------|-------------|--------|
| Optimization Rate | Successful optimizations / attempts | >15% |
| Processing Time | Average time per optimization | <3 minutes |
| Error Rate | Failed attempts / total attempts | <5% |
| Manual Approval Rate | Manual approvals / dry run successes | >80% |

### Quality Metrics

| Metric | Calculation | Target |
|--------|-------------|--------|
| Customer Satisfaction | Post-optimization surveys | >4.5/5 |
| Room Match Accuracy | Correct room matches / total matches | >95% |
| Policy Compliance | Compliant policies / total optimizations | 100% |

---

## 🔄 Continuous Improvement

### Regular Reviews

**Monthly**:
- Performance metrics analysis
- Error pattern review
- Client feedback assessment

**Quarterly**:
- Configuration optimization
- Threshold adjustment
- Process improvement

**Annually**:
- Architecture review
- Technology updates
- Business model refinement

### Enhancement Opportunities

1. **AI/ML Integration**: Predictive optimization scoring
2. **Real-time Processing**: Instant optimization capabilities
3. **Advanced Matching**: Enhanced room and policy matching
4. **Automated Approval**: Intelligent approval workflows

---

## 🎯 Decision Flow Diagram

```mermaid
flowchart TD
    A[Start Multi-Supplier Optimization] --> B{Client Config Valid?}
    B -->|No| C[Return Error]
    B -->|Yes| D[Load Reservations & Data]
    D --> E[Filter Allowed Suppliers]
    E --> F{Suppliers Available?}
    F -->|No| G[No Suppliers Error]
    F -->|Yes| H[Process in Batches of 5]
    H --> I[Search Each Supplier]
    I --> J{Search Results Found?}
    J -->|No| K[Try Next Batch]
    J -->|Yes| L[Giata Room Mapping]
    L --> M{Room Mapping Success?}
    M -->|No| N[Mapping Error]
    M -->|Yes| O[Filter & Match Offers]
    O --> P{Offers Found?}
    P -->|No| Q[No Offers Found]
    P -->|Yes| R[Price Validation]
    R --> S{Price Above Threshold?}
    S -->|No| T[Below Threshold]
    S -->|Yes| U[Create Prebook]
    U --> V{Prebook Success?}
    V -->|No| W[Prebook Failed]
    V -->|Yes| X[Cancellation Check]
    X --> Y{Policy Acceptable?}
    Y -->|No| Z[Policy Rejected]
    Y -->|Yes| AA[Dry Run]
    AA --> BB{Expected Gain OK?}
    BB -->|No| CC[Gain Too Low]
    BB -->|Yes| DD[Send Email Alert]
    DD --> EE[Manual Approval]
    EE --> FF{Approved?}
    FF -->|No| GG[Rejected]
    FF -->|Yes| HH[Execute Optimization]
    HH --> II[Success]

    K --> J
    N --> K
    Q --> K
    T --> K
    W --> K
    Z --> K
    CC --> K
    GG --> K
```

---

## 📋 Checklist for Implementation

### Pre-Implementation
- [ ] Client configuration validated
- [ ] Supplier agreements in place
- [ ] Giata mapping coverage verified
- [ ] Price thresholds configured
- [ ] Email notifications set up

### During Implementation
- [ ] Monitor processing times
- [ ] Track error rates
- [ ] Validate room mappings
- [ ] Check cancellation policies
- [ ] Verify manual approval workflow

### Post-Implementation
- [ ] Performance metrics reviewed
- [ ] Customer feedback collected
- [ ] Revenue tracking active
- [ ] Error patterns analyzed
- [ ] Process improvements identified

---

*This document serves as the definitive guide for Multi-Supplier Optimization Flow. For technical implementation details, refer to the source code. For business questions, contact the operations team.*

**Document Version**: 1.0
**Last Updated**: December 2024
**Next Review**: March 2025

---

## ⚡ Performance Considerations

### Parallel Processing

**Configuration**:
- Default: 50 concurrent reservations
- Single reservation: Sequential processing
- Batch size: 5 suppliers per batch

**Optimization Strategies**:
- Early termination on timeout
- Resource pooling for API calls
- Caching of frequently accessed data

### Timeout Management

**Timeout Settings**:
- Offer filtering: 5 minutes
- Manual trigger: 5 minutes
- Automatic trigger: 30 seconds
- API calls: 30 seconds each

### Memory Management

**Large Dataset Handling**:
- Streaming for large result sets
- Garbage collection optimization
- Connection pooling

### Caching Strategy

**Cache Layers**:
- **Redis**: Session and temporary data
- **Memory**: Frequently accessed configuration
- **Database**: Persistent optimization results

---

## 🔍 Troubleshooting Guide

### Performance Issues

**Symptoms**: Slow processing, timeouts
**Diagnosis**:
1. Check parallel processing settings
2. Monitor API response times
3. Analyze database query performance
4. Review memory usage

**Solutions**:
- Adjust concurrency limits
- Optimize database queries
- Implement additional caching
- Scale infrastructure

### Optimization Failures

**Symptoms**: High error rates, no optimizations found
**Diagnosis**:
1. Review client configuration
2. Check supplier availability
3. Validate Giata mapping coverage
4. Analyze price thresholds

**Solutions**:
- Update client settings
- Add more suppliers
- Improve room mapping
- Adjust thresholds

### Data Quality Issues

**Symptoms**: Incorrect room matches, policy mismatches
**Diagnosis**:
1. Validate Giata mapping accuracy
2. Check cancellation policy parsing
3. Review room categorization

**Solutions**:
- Update Giata mappings
- Improve policy parsing logic
- Enhance room matching algorithms

---

## 🔧 Key Components

### 1. SupplierSearchService

**Primary Orchestrator** for the entire multi-supplier flow

**Key Responsibilities**:
- Coordinate all phases of optimization
- Manage parallel processing
- Handle error scenarios
- Update booking statuses

**Critical Methods**:
- `_1_PrebookAndOptimize_MultiSupplier`: Main entry point
- `_2_GetOfferInfoListAsync`: Offer filtering
- `UpdateMatchingRoomOffer`: Room matching logic

### 2. SearchHelper

**Search Operations Manager**

**Key Functions**:
- Create search criteria for multiple suppliers
- Execute searches via IRIX API
- Handle search response processing

**Multi-Supplier Specifics**:
```csharp
// Creates criteria for multiple suppliers
criteria = _searchserviceHelper.CreateCriteria(
    reservation,
    roomReservation,
    allowedProviders, // Multiple suppliers
    ismultiprebook: true
);
```

### 3. GiataService

**Room Mapping Service**

**Business Importance**: Ensures room equivalency across suppliers

**Technical Process**:
- Maps room names to standardized categories
- Provides confidence scores for matches
- Handles multiple languages and naming conventions

### 4. OptimizationService

**Final Booking Execution**

**Responsibilities**:
- Execute final rebooking
- Handle booking confirmations
- Manage booking failures
- Update customer records

### 5. DryRunOptimizationService

**Validation Service**

**Purpose**: Validate optimization feasibility without commitment

**Business Value**: Prevents failed optimizations and customer disruption

---

## ⚙️ Configuration Requirements

### Client Configuration

**Required Settings**:
```json
{
  "IsMultiSupplierEnabled": true,
  "IsJobsEnable": true,
  "IsActive": true,
  "IsOptimizationAllowed": true,
  "OptimizationType": "Manual", // Multi-supplier requires manual approval
  "PriceDifferenceValue": 50.0,
  "PriceDifferencePercentage": 5.0,
  "IsUsePercentage": false,
  "traveldaysmaxsearchindays": 60,
  "traveldaysminsearchindays": 10
}
```

### Supplier Configuration

**Allowed Providers Setup**:
- List of suppliers client can use for rebooking
- Excludes original booking supplier automatically
- Configurable per client

**Example**:
```json
{
  "allowedProviders": [
    "Booking.com",
    "Expedia",
    "Hotels.com",
    "Agoda"
  ]
}
```

### Threshold Configuration

**Price Thresholds**:
- Minimum savings amount or percentage
- Currency-specific settings
- Client-specific overrides

**Cancellation Policy Thresholds**:
- Acceptable policy degradation levels
- Buffer periods for policy comparison
- Risk tolerance settings

---

## 🚨 Error Handling & Monitoring

### Common Error Scenarios

| Error Type | Cause | Business Impact | Resolution |
|------------|-------|-----------------|------------|
| `GiataMappingNotFound` | Room mapping failed | No optimization possible | Manual room mapping required |
| `NoMatchingOffers` | No suitable alternatives | No savings opportunity | Normal business outcome |
| `PriceThresholdCheckFailed` | Savings below threshold | Optimization not worthwhile | Adjust thresholds if needed |
| `OptimizationNotAllowed` | Client restrictions | Cannot proceed | Check client configuration |
| `SupplierNotAllowed` | Supplier not in allowed list | Security/contract issue | Update supplier configuration |

### Monitoring & Alerting

**Key Metrics to Monitor**:
- Optimization success rate
- Average processing time
- Error frequency by type
- Revenue generated

**Alert Thresholds**:
- Error rate > 10%
- Processing time > 5 minutes
- Giata mapping failures > 5%

### Logging Strategy

**Log Levels**:
- **INFO**: Normal flow progression
- **WARN**: Recoverable issues
- **ERROR**: Failed optimizations
- **DEBUG**: Detailed troubleshooting

**Key Log Points**:
- Phase transitions
- External API calls
- Decision points
- Error conditions
