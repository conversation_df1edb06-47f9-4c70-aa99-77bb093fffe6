﻿using Irix.Entities;
using Newtonsoft.Json;
using RePricer.Util;
using System.Data;

namespace Irix.Persistence.Data
{
    public class ClientData
    {
        public RePricerDetail ConvertRepricerData(IDataReader reader)
        {
            var rePricerDetail = new RePricerDetail
            {
                RepricerUserID = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerUserID"),
                RepricerUserName = DbPropertyHelper.StringPropertyFromRow(reader, "RepricerUserName"),
                AdminUrl = DbPropertyHelper.StringPropertyFromRow(reader, "AdminURL"),
                AdminUserId = DbPropertyHelper.StringPropertyFromRow(reader, "AdminUserID"),
                AdminPassword = DbPropertyHelper.StringPropertyFromRow(reader, "AdminPassword"),
                ResellerUrl = DbPropertyHelper.StringPropertyFromRow(reader, "ResellerURL"),
                ResellerUserId = DbPropertyHelper.StringPropertyFromRow(reader, "ResellerUserID"),
                ResellerPassword = DbPropertyHelper.StringPropertyFromRow(reader, "ResellerPassword"),
                AdminApiScope = DbPropertyHelper.StringPropertyFromRow(reader, "ApiScope"),
                ResellerApiScope = DbPropertyHelper.StringPropertyFromRow(reader, "ResellerApiScope"),
                IsActive = DbPropertyHelper.BoolPropertyFromRow(reader, "IsActive"),
                IsJobsEnable = DbPropertyHelper.BoolPropertyFromRow(reader, "IsJobsEnable"),
                OptimizationType = (OptimizationType)DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "OptimizationType"),
                EmailTo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "EmailTo"),
                EmailCC = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "EmailCC"),
                EmailBcc = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "EmailBCC"),

                RestrictedHotel = JsonConvert.DeserializeObject<List<HotelMasterModel>>(
                                  DbPropertyHelper.StringPropertyFromRow(reader, "RestrictedHotelId")) ?? new List<HotelMasterModel>(),
                RestrictedCity = JsonConvert.DeserializeObject<List<CityMaster>>(
                                  DbPropertyHelper.StringPropertyFromRow(reader, "RestrictedCityId")) ?? new List<CityMaster>(),
                RestrictedCountry = JsonConvert.DeserializeObject<List<CountryMasterModel>>(
                                 DbPropertyHelper.StringPropertyFromRow(reader, "RestrictedCountryId")) ?? new List<CountryMasterModel>(),
                RestrictedReseller = JsonConvert.DeserializeObject<List<ResellerMaster>>(
                                   DbPropertyHelper.StringPropertyFromRow(reader, "RestrictedReseller")) ?? new List<ResellerMaster>(),
                RestrictedSupplier = JsonConvert.DeserializeObject<List<SupplierMaster>>(
                                    DbPropertyHelper.StringPropertyFromRow(reader, "RestrictedSupplier")) ?? new List<SupplierMaster>(),

                AutoJobServerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "AutoJobServerId"),
                // New properties with default values if not found in database
                DelayBetweenOptimizationJob = DbPropertyHelper.Int32NullablePropertyFromRow(reader, "DelayBetweenOptimizationJob") ?? 1,
                DelayBetweenDailyDownloadReservation = DbPropertyHelper.Int32NullablePropertyFromRow(reader, "DelayBetweenDailyDownloadReservation") ?? 3,
                DelayBetweenRequestsSameSupplier = DbPropertyHelper.Int32NullablePropertyFromRow(reader, "DelayBetweenRequestsSameSupplier") ?? 2,
                DelayBetweenRequestsMultiSupplier = DbPropertyHelper.Int32NullablePropertyFromRow(reader, "DelayBetweenRequestsMultiSupplier") ?? 2,
                IsMultiSupplierEnabled = DbPropertyHelper.BoolNullablePropertyFromRow(reader, "IsMultiSupplierRoomSync") ?? false,

                ClientConfiguration = new ClientConfiguration
                {
                    Service = DbPropertyHelper.StringPropertyFromRow(reader, "Service"),

                    CancelPenalty = DbPropertyHelper.BoolPropertyFromRow(reader, "CancelPenalty"),
                },
                ExtraClientDetail = new ExtraClientDetail
                {
                    PriceDifferenceValue = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "priceDifferenceValue"),
                    PriceDifferencePercentage = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "priceDifferencePercentage"),
                    IsUsePercentage = false,//DbPropertyHelper.BoolPropertyFromRow(reader, "isUsePercentage"),
                    TravelDaysMaxSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMaxSearchInDays"),
                    TravelDaysMinSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMinSearchInDays"),
                    ClientConfig_DaysDifferenceInPreBookCreation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ClientConfig_DaysDifferenceInPreBookCreation"),
                    MaxNumberOfTimesOptimization = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MaxNumberOfTimesOptimization"),

                    DaysLimitCancellationPolicyEdgeCase = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "DaysLimitCancellationPolicyEdgeCase"),

                    IsCreatePrebookForPriceEdgeCase = true, //DbPropertyHelper.BoolPropertyFromRow(reader, "IsCreatePrebookForPriceEdgeCase"),
                    IsUseDaysLimitCancellationPolicyEdgeCase = true,//DbPropertyHelper.BoolPropertyFromRow(reader, "IsUseDaysLimitCancellationPolicyEdgeCase"),

                    ReportEmailToSend = DbPropertyHelper.StringPropertyFromRow(reader, "ReportEmailToSend"),
                    Currency = DbPropertyHelper.StringPropertyFromRow(reader, "Currency"),
                    IsUseResellerCPHourDifference = DbPropertyHelper.BoolPropertyFromRow(reader, "IsUseResellerCPHourDifference"),
                    ResellerCPHourDifference = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ResellerCPHourDifference"),
                    CPAmountAllowedBufferPercentage = DbPropertyHelper.DecimalPropertyFromRow(reader, "CPAmountAllowedBufferPercentage"),
                    CPTimeAllowedBufferPercentage = DbPropertyHelper.DecimalPropertyFromRow(reader, "CPTimeAllowedBufferPercentage"),
                    PriceDifferenceValueAllowedBufferPercentage = DbPropertyHelper.DecimalNullablePropertyFromRow(reader, "PriceDifferenceValueAllowedBufferPercentage") ?? 0,
                    IsCheckCrossSupperBeforeOptimization = DbPropertyHelper.BoolNullablePropertyFromRow(reader, "IsCheckCrossSupperBeforeOptimization") ?? false,
                    IsCheckProfitAfterCancellation = DbPropertyHelper.BoolNullablePropertyFromRow(reader, "IsCheckProfitAfterCancellation") ?? false,
                },
                ClientConfigScheduler = new ClientConfigScheduler
                {
                    PreBook_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_CronTime"),
                    Reservation_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "Reservation_CronTime"),
                    CurrencyExchange_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "CurrencyExchange_CronTime"),
                    TimeZoneId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TimeZoneId"),
                    TimeZoneName = DbPropertyHelper.StringPropertyFromRow(reader, "TimeZoneName")
                }
            };
            try
            {
                rePricerDetail.UpdateDate = DbPropertyHelper.DateTimeNullablePropertyFromRow(reader, "UpdateDate") ?? null;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            rePricerDetail.ExtraClientDetail.PriceDifferenceValueOriginal = rePricerDetail.ExtraClientDetail.PriceDifferenceValue;
            if (rePricerDetail?.ExtraClientDetail?.PriceDifferenceValueAllowedBufferPercentage >= 1)
            {
                try
                {
                    var calculateBuffer = (rePricerDetail.ExtraClientDetail.PriceDifferenceValue * rePricerDetail.ExtraClientDetail.PriceDifferenceValueAllowedBufferPercentage) / 100;
                    var newPriceDifferenceValue = Math.Round(System.Convert.ToDecimal(rePricerDetail.ExtraClientDetail.PriceDifferenceValue - calculateBuffer), 2);
                    if (newPriceDifferenceValue > 1)
                    {
                        rePricerDetail.ExtraClientDetail.PriceDifferenceValue = newPriceDifferenceValue;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }
            }
            return rePricerDetail;
        }

        public RepricerResponse ConvertRepricerResponse(IDataReader reader)
        {
            var rePricerDetail = new RepricerResponse
            {
                UserName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "UserName"),
                RepricerUserID = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerUserID")
            };

            return rePricerDetail;
        }

        public static List<int> ConvertToIntList(string value)
        {
            return string.IsNullOrEmpty(value)
                ? new List<int>()
                : value.Split(',')
                       .Select(v => int.TryParse(v.Trim(), out int result) ? result : 0) // Default to 0 if parse fails
                       .ToList();
        }

        public static List<string> ConvertToStringList(string value)
        {
            return string.IsNullOrEmpty(value)
                ? new List<string>()
                : value.Split(',')
                       .Select(v => v.Trim())
                       .ToList();
        }
    }
}