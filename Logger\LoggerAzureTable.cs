using Azure;
using Azure.Data.Tables;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using Irix.Entities;
using Logger.Contract;
using Microsoft.Extensions.Caching.Memory;
using RePricer.Constants;
using RePricer.Util;
using System.Net;
using System.Text;
using Constants = RePricer.Constants.ServiceAdapterConstants;

namespace Logger
{
    public class LoggerAzureTable : ILoggerMongoDb
    {
        private readonly TableClient? _apiLogsTable;
        private readonly BlobContainerClient? _blobContainerClient;
        private readonly string? _azureStorageConnectionString;
        private readonly int _maxInlineDataSizeKb;
        private readonly string? _isLogging;
        private readonly string? _saveInStorageConfigFlag;
        private readonly Dictionary<string, TableClient> _tableClients;
        private readonly object _tableClientsLock = new object();

        // List of all required tables
        private static readonly string[] _requiredTables = new string[]
        {
            "ApiLogs",
            "ApiErrorLogs",
            "OptimizedReservations",
            "OptimizedReservationsSearchAndPrebookLogs",
            "OptimizationStatusCheck",
            "MultiSupplierPreBook",
            "ErrorLoggingGeneral",
            "ErrorLoggingHangFire",
            "GiataApi",
            "LoginDetails",

            "GeneralLogs",
            "ApiHitCounters",
            "OptimizationToQueue",
        };

        private readonly LoggerMongoDb? _loggerMongoDb;

        public LoggerAzureTable()
        {
            try
            {
                // Initialize the table clients dictionary
                _tableClients = new Dictionary<string, TableClient>();

                IMemoryCache _memoryCache = new MemoryCache(new MemoryCacheOptions());
                _loggerMongoDb = new LoggerMongoDb(_memoryCache);

                Console.WriteLine("Initializing LoggerAzureTable...");

                // Get configuration values
                _azureStorageConnectionString = KeyVaultUriHelper.GetSecretAsync(CacheHelper.GetConnectionString("StorageConnectionString")).GetAwaiter().GetResult();

                if (string.IsNullOrEmpty(_azureStorageConnectionString))
                {
                    Console.WriteLine("WARNING: StorageConnectionString is empty or null. Azure Table logging will be disabled.");
                    _isLogging = "0";
                    _saveInStorageConfigFlag = "0";
                    _maxInlineDataSizeKb = 30;
                    return;
                }

                _maxInlineDataSizeKb = Convert.ToInt32(ConfigurationManagerHelper.GetValuefromAppSettings("AzureTableloggingLimitInkb"));
                _isLogging = ConfigurationManagerHelper.GetValuefromAppSettings(LoggerConstants.isloging);
                _saveInStorageConfigFlag = ConfigurationManagerHelper.GetValuefromAppSettings(LoggerConstants.SaveInStorage);

                //Console.WriteLine($"Configuration: AzureTableloggingLimitInkb={_maxInlineDataSizeKb}, isAzurelogging={_isLogging}, SaveInStorage={_saveInStorageConfigFlag}");

                if (_maxInlineDataSizeKb == 0)
                {
                    _maxInlineDataSizeKb = 30; // Default 30KB limit for inline storage
                    //Console.WriteLine($"Using default value for AzureTableloggingLimitInkb: {_maxInlineDataSizeKb}");
                }

                // Initialize Azure Table client
                var tableServiceClient = new TableServiceClient(_azureStorageConnectionString);

                // Initialize all required tables
                InitializeAllTables(tableServiceClient).GetAwaiter().GetResult();

                // Set the ApiLogs table client for backward compatibility
                _apiLogsTable = _tableClients.ContainsKey("ApiLogs") ? _tableClients["ApiLogs"] : null;

                // Initialize Blob Storage client for large payloads
                try
                {
                    var blobServiceClient = new BlobServiceClient(_azureStorageConnectionString);
                    _blobContainerClient = blobServiceClient.GetBlobContainerClient("apilogs");
                    _blobContainerClient.CreateIfNotExists(PublicAccessType.None);
                    //Console.WriteLine("Successfully connected to Azure Blob Storage and created apilogs container if needed.");
                }
                catch (Exception blobEx)
                {
                    Console.WriteLine($"Error creating blob container: {blobEx.Message}. Large payload logging will be disabled.");
                    // If blob storage fails, we'll still try to log to tables, but with limited payload size
                    _maxInlineDataSizeKb = 30; // Limit to small payloads only
                }

                Console.WriteLine("LoggerAzureTable initialization completed successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing LoggerAzureTable: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                // Initialize with defaults to prevent null reference exceptions
                _maxInlineDataSizeKb = 30;
                _isLogging = "0";
                _saveInStorageConfigFlag = "0";
                _tableClients = new Dictionary<string, TableClient>();

                // Log to a file as a last resort
                try
                {
                    string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                    Directory.CreateDirectory(logPath);
                    string logFile = Path.Combine(logPath, $"azure_logger_error_{DateTime.Now:yyyyMMdd}.log");
                    File.AppendAllText(logFile, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Error initializing LoggerAzureTable: {ex.Message}\n{ex.StackTrace}\n\n");
                }
                catch
                {
                    // If even file logging fails, we can't do much more
                }
            }
        }

        // Method to initialize all required tables
        private async Task InitializeAllTables(TableServiceClient tableServiceClient)
        {
            //Console.WriteLine("Initializing all required tables...");

            foreach (var tableName in _requiredTables)
            {
                try
                {
                    // Get or create the table client
                    var tableClient = tableServiceClient.GetTableClient(tableName);

                    // Create the table if it doesn't exist
                    await RetryOperationAsync(async () =>
                    {
                        await tableClient.CreateIfNotExistsAsync().ConfigureAwait(false);
                    }, $"Create table {tableName}", 5).ConfigureAwait(false); // Use 5 retries for table creation

                    // Add the table client to the dictionary with lock protection
                    lock (_tableClientsLock)
                    {
                        _tableClients[tableName] = tableClient;
                    }

                    //Console.WriteLine($"Successfully initialized table: {tableName}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error initializing table {tableName}: {ex.Message}");
                    // Continue with other tables even if one fails
                }
            }

            //Console.WriteLine($"Initialized {_tableClients.Count} out of {_requiredTables.Length} required tables.");

            // Verify that OptimizedReservationsSearchAndPrebookLogs table was created
            if (_tableClients.ContainsKey("OptimizedReservationsSearchAndPrebookLogs"))
            {
                //Console.WriteLine("✓ OptimizedReservationsSearchAndPrebookLogs table client created successfully");
            }
            else
            {
                Console.WriteLine("✗ OptimizedReservationsSearchAndPrebookLogs table client NOT created");
            }
        }

        // Method to get or create a table client
        private async Task<TableClient> GetOrCreateTableClientAsync(string tableName)
        {
            // Use a double-checked locking pattern to prevent race conditions
            // First check outside the lock for performance
            if (_tableClients.TryGetValue(tableName, out var existingClient))
            {
                return existingClient;
            }

            // If not found, lock and check again
            lock (_tableClientsLock)
            {
                // Check again inside the lock
                if (_tableClients.TryGetValue(tableName, out existingClient))
                {
                    return existingClient;
                }

                // If still not found, create a new client
                if (_azureStorageConnectionString == null)
                {
                    throw new InvalidOperationException($"Cannot create table client for {tableName}: Storage connection string is null");
                }

                // Create the client (but don't create the table yet)
                var tableServiceClient = new TableServiceClient(_azureStorageConnectionString);
                var tableClient = tableServiceClient.GetTableClient(tableName);

                // Add the client to the dictionary inside the lock
                _tableClients[tableName] = tableClient;

                // Return the client
                existingClient = tableClient;
            }

            // Now create the table outside the lock (this is an async operation)
            try
            {
                // Create the table if it doesn't exist
                await RetryOperationAsync(async () =>
                {
                    await existingClient.CreateIfNotExistsAsync();
                }, $"Create table {tableName}", 5); // Use 5 retries for table creation

                return existingClient;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating table {tableName}: {ex.Message}");

                // Even if table creation fails, we can still return the client
                // The table might already exist or will be created on the next attempt
                return existingClient;
            }
        }

        public void Write(int repricerId, string request, string response, string methodName, string apiUrl, HttpStatusCode statusCode, string? reservationId = null, bool isMultiSupplier = false)
        {
            try
            {
                // Check if logging is enabled
                if (_isLogging != "1" || _saveInStorageConfigFlag == null || !_saveInStorageConfigFlag.Equals(LoggerConstants.SaveInStorageValue))
                {
                    return;
                }

                var logTime = DateTime.UtcNow;

                Task.Run(async () =>
                {
                    try
                    {
                        // Create the entity for Azure Table
                        var entity = CreateEntity(repricerId, reservationId, methodName, apiUrl, statusCode, logTime, isMultiSupplier);

                        // Process request and response data (handling large payloads)
                        await ProcessEntityDataAsync(entity, request, response);

                        // Special case: Successful optimization - log to multiple tables
                        if (methodName == Constants.OptimizeBookingMethod && statusCode == HttpStatusCode.OK)
                        {
                            // 1. Log to OptimizedReservations table
                            await LogToTableAsync(entity, "OptimizedReservations");

                            // 2. Also log to ApiLogs table as backup
                            await LogToTableAsync(entity, "ApiLogs");

                            // 3. Process related search and prebook logs
                            if (!string.IsNullOrEmpty(reservationId))
                            {
                                await ProcessSearchAndPrebookLogsForOptimization(repricerId, reservationId);
                            }

                            return; // Exit since we've handled all required logging
                        }
                        // Special case: Search and PreBook operations - log to ApiLogs
                        // These will later be retrieved and logged to OptimizedReservationsSearchAndPrebookLogs
                        // when a related optimization succeeds
                        else if (methodName == Constants.SearchSync || methodName == Constants.PreBook)
                        {
                            // Always log search and prebook operations to ApiLogs
                            await LogToTableAsync(entity, "ApiLogs");
                            return;
                        }

                        // For all other cases, determine the appropriate single table
                        string tableName;

                        if (methodName == Constants.OptimizerStatusCheckMethod)
                        {
                            tableName = "OptimizationStatusCheck";
                        }
                        else if (statusCode != HttpStatusCode.OK && statusCode != HttpStatusCode.Created && statusCode != HttpStatusCode.Accepted)
                        {
                            tableName = "ApiErrorLogs"; // For error logs
                        }
                        else if (methodName == Constants.GiataApiCall)
                        {
                            tableName = "GiataApi";
                        }
                        else
                        {
                            tableName = "ApiLogs"; // Default for all other logs, including MultiSupplier logs
                        }

                        // Log to the determined table
                        await LogToTableAsync(entity, tableName);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in Write method: {ex.Message}");

                        // Try to log to error log
                        try
                        {
                            await LogErrorAsync("Write method failed", ex);
                        }
                        catch (Exception logEx)
                        {
                            // Last resort: console log
                            Console.WriteLine($"Failed to log error: {logEx.Message}");

                            // Last resort fallback - log to file
                            LogToFile("error", $"Error in Write method: {ex.Message}\nFailed to log error: {logEx.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Write method: {ex.Message}");

                // Last resort fallback - log to file
                LogToFile("critical", $"Critical error in Write method: {ex.Message}");
            }
        }

        // Helper method to process search and prebook logs for a successful optimization
        private async Task ProcessSearchAndPrebookLogsForOptimization(int repricerId, string reservationId)
        {
            try
            {
                Console.WriteLine($"Processing search and prebook logs for optimization - repricerId: {repricerId}, reservationId: {reservationId}");

                // 1. Get cached search and prebook logs from Azure Table Storage
                var cachedSearchAndPrebookLogs = await GetCachedSearchAndPrebookLogsAsync(repricerId, reservationId);

                if (cachedSearchAndPrebookLogs != null && cachedSearchAndPrebookLogs.Count > 0)
                {
                    Console.WriteLine($"Found {cachedSearchAndPrebookLogs.Count} search and prebook logs to process");

                    // 2. Get or create the OptimizedReservationsSearchAndPrebookLogs table client
                    var tableClient = await GetOrCreateTableClientAsync("OptimizedReservationsSearchAndPrebookLogs");

                    // 3. Write each log to the OptimizedReservationsSearchAndPrebookLogs table
                    int processedCount = 0;
                    foreach (var logEntry in cachedSearchAndPrebookLogs)
                    {
                        try
                        {
                            // Create a new entity for the table
                            var entity = new ApiLoggingTableEntity
                            {
                                PartitionKey = ApiLoggingTableEntity.CreatePartitionKey(logEntry.CurrentTime, logEntry.RePricerId),
                                RowKey = ApiLoggingTableEntity.CreateRowKey(logEntry.RePricerId, logEntry.ReservationId, logEntry.Method, logEntry.CurrentTime),
                                RepricerId = logEntry.RePricerId,
                                ReservationId = logEntry.ReservationId ?? string.Empty,
                                Method = logEntry.Method,
                                ApiUrl = logEntry.ApiUrl,
                                StatusCode = (int)logEntry.StatusCode,
                                LogTime = logEntry.CurrentTime,
                                IsMultiSupplier = logEntry.IsMultiSupplier
                            };

                            // Process request and response data
                            await ProcessEntityDataAsync(entity, logEntry.Request, logEntry.Response);

                            // Add a reference to the successful optimization
                            var additionalProperties = new Dictionary<string, object>
                            {
                                ["OptimizationTimestamp"] = DateTime.UtcNow,
                                ["OptimizationReservationId"] = reservationId
                            };

                            // Add the entity to the table with retry logic
                            await LogToTableWithAdditionalPropertiesAsync(entity, "OptimizedReservationsSearchAndPrebookLogs", additionalProperties);

                            processedCount++;
                            Console.WriteLine($"Successfully processed log entry {processedCount}: Method={logEntry.Method}, Time={logEntry.CurrentTime}");
                        }
                        catch (Exception logEx)
                        {
                            Console.WriteLine($"Error processing individual log entry: {logEx.Message}");
                        }
                    }

                    Console.WriteLine($"Successfully processed {processedCount} out of {cachedSearchAndPrebookLogs.Count} cached search and prebook logs for optimization of reservation {reservationId}");
                }
                else
                {
                    Console.WriteLine($"No cached search or prebook logs found for reservation {reservationId}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing cached search and prebook logs: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                await LogErrorAsync("Error processing search and prebook logs", ex);
            }
        }

        // Helper method to get cached search and prebook logs
        private async Task<List<MongoDbLoggingEntity>> GetCachedSearchAndPrebookLogsAsync(int repricerId, string reservationId)
        {
            try
            {
                Console.WriteLine($"Fetching cached search and prebook logs for repricerId: {repricerId}, reservationId: {reservationId}");

                // Now fetch from Azure Table Storage instead of MongoDB
                var result = new List<MongoDbLoggingEntity>();

                if (_azureStorageConnectionString == null)
                {
                    Console.WriteLine("Azure storage connection string is null");
                    return result;
                }

                // Get the ApiLogs table client where search and prebook logs are stored
                var tableClient = await GetOrCreateTableClientAsync("ApiLogs");

                // Search for logs from the last 3 days to ensure we capture all related logs
                var fromDate = DateTime.UtcNow.AddDays(-3);
                var toDate = DateTime.UtcNow;

                // Query for each day in the range
                for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
                {
                    try
                    {
                        // Create partition key for this repricerId and date
                        string partitionKey = ApiLoggingTableEntity.CreatePartitionKey(date, repricerId);

                        // Build filter to find search and prebook logs with matching reservationId
                        var filterConditions = new List<string>
                        {
                            $"PartitionKey eq '{partitionKey}'",
                            $"ReservationId eq '{reservationId}'",
                            $"(Method eq '{Constants.SearchSync}' or Method eq '{Constants.PreBook}')"
                        };

                        string filter = string.Join(" and ", filterConditions);

                        Console.WriteLine($"Querying ApiLogs table with filter: {filter}");

                        // Query the table
                        var queryResults = tableClient.Query<ApiLoggingTableEntity>(filter);

                        // Convert results to MongoDbLoggingEntity for compatibility
                        foreach (var entity in queryResults)
                        {
                            var logEntry = await ConvertToMongoDbLoggingEntityAsync(entity);
                            result.Add(logEntry);
                            Console.WriteLine($"Found log: Method={logEntry.Method}, ReservationId={logEntry.ReservationId}, Time={logEntry.CurrentTime}");
                        }
                    }
                    catch (Exception dayEx)
                    {
                        Console.WriteLine($"Error querying logs for date {date:yyyy-MM-dd}: {dayEx.Message}");
                    }
                }

                Console.WriteLine($"Found {result.Count} cached search and prebook logs for reservation {reservationId}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving cached search and prebook logs: {ex.Message}");
                return new List<MongoDbLoggingEntity>();
            }
        }

        public void Error(int repricerId, string request, string response, string methodName, string apiUrl, HttpStatusCode statusCode, string? reservationId = null, bool isMultiSupplier = false)
        {
            try
            {
                // Check if logging is enabled
                if (_isLogging != "1" || _saveInStorageConfigFlag == null || !_saveInStorageConfigFlag.Equals(LoggerConstants.SaveInStorageValue))
                {
                    return;
                }

                var logTime = DateTime.UtcNow;

                Task.Run(async () =>
                {
                    try
                    {
                        // Create the entity for Azure Table
                        var entity = CreateEntity(repricerId, reservationId, methodName, apiUrl, statusCode, logTime, isMultiSupplier);

                        // Process request and response data (handling large payloads)
                        await ProcessEntityDataAsync(entity, request, response);

                        // Always log errors to ApiErrorLogs table
                        await LogToTableAsync(entity, "ApiErrorLogs");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in Error method: {ex.Message}");

                        // Try to log to error log
                        try
                        {
                            await LogErrorAsync("Error method failed", ex);
                        }
                        catch (Exception logEx)
                        {
                            // Last resort: console log
                            Console.WriteLine($"Failed to log error: {logEx.Message}");

                            // Last resort fallback - log to file
                            LogToFile("error", $"Error in Error method: {ex.Message}\nFailed to log error: {logEx.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Error method: {ex.Message}");

                // Last resort fallback - log to file
                LogToFile("critical", $"Critical error in Error method: {ex.Message}");
            }
        }

        // Retry operation with exponential backoff
        private async Task<bool> RetryOperationAsync(Func<Task> operation, string operationName, int maxRetries = 3)
        {
            int retryCount = 0;
            bool success = false;

            while (retryCount < maxRetries && !success)
            {
                try
                {
                    await operation();
                    success = true;
                }
                catch (Exception ex)
                {
                    retryCount++;

                    // Check if this is an EntityAlreadyExists error
                    bool isEntityAlreadyExistsError = ex.Message.Contains("EntityAlreadyExists") ||
                                                     (ex is RequestFailedException rfe && rfe.Status == 409);

                    // Check if this is a TableNotFound error
                    bool isTableNotFoundError = ex.Message.Contains("TableNotFound") ||
                                               (ex is RequestFailedException rfe2 && rfe2.Status == 404 &&
                                                rfe2.ErrorCode == "TableNotFound");

                    // Log the error
                    Console.WriteLine($"Error in {operationName} (Attempt {retryCount}/{maxRetries}): {ex.Message}");

                    if (isEntityAlreadyExistsError)
                    {
                        // For EntityAlreadyExists errors, we can consider this a success since the entity exists
                        // This is especially true when we switch to UpsertEntityAsync
                        Console.WriteLine($"Entity already exists in {operationName}. Considering operation successful.");
                        success = true;
                        return success;
                    }

                    if (isTableNotFoundError && operationName.Contains("Write to"))
                    {
                        // For TableNotFound errors during write operations, try to create the table
                        string tableName = operationName.Replace("Write to ", "").Trim();
                        Console.WriteLine($"Table {tableName} not found. Attempting to create it...");

                        try
                        {
                            // Extract table name from the operation name
                            if (!string.IsNullOrEmpty(tableName))
                            {
                                // Create the table
                                var tableClient = await GetOrCreateTableClientAsync(tableName);

                                // Retry the original operation
                                await operation();
                                success = true;
                                Console.WriteLine($"Successfully created table {tableName} and completed operation.");
                                return success;
                            }
                        }
                        catch (Exception createEx)
                        {
                            Console.WriteLine($"Error creating table {tableName}: {createEx.Message}");
                        }
                    }

                    if (retryCount >= maxRetries)
                    {
                        Console.WriteLine($"Operation {operationName} failed after {maxRetries} attempts");

                        // Log to error log
                        try
                        {
                            await LogErrorAsync($"Operation {operationName} failed after {maxRetries} attempts", ex);
                        }
                        catch (Exception logEx)
                        {
                            Console.WriteLine($"Failed to log error: {logEx.Message}");
                        }
                    }
                    else
                    {
                        // Wait before retrying (exponential backoff)
                        int delayMilliseconds = 100 * (int)Math.Pow(2, retryCount);
                        await Task.Delay(delayMilliseconds);
                    }
                }
            }

            return success;
        }

        private async Task LogErrorAsync(string message, Exception ex)
        {
            try
            {
                var logTime = DateTime.UtcNow;
                var entity = new TableEntity
                {
                    PartitionKey = $"{logTime:dd-MMM-yyyy}",
                    RowKey = Guid.NewGuid().ToString(),
                    ["Message"] = message,
                    ["Exception"] = ex?.ToString() ?? "No exception details",
                    ["LogTime"] = logTime
                };

                if (_azureStorageConnectionString != null)
                {
                    // Get or create the table client
                    var tableClient = await GetOrCreateTableClientAsync("ErrorLoggingGeneral");

                    // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                    await tableClient.UpsertEntityAsync(entity);
                }
            }
            catch (Exception error)
            {
                Console.WriteLine($"Error writing error log: {error.Message}");
            }
        }

        private async Task<string> UploadToBlobAsync(string content, string blobName)
        {
            try
            {
                // Handle reservationId with comma-separated values in blobName
                if (blobName.Contains(","))
                {
                    string[] parts = blobName.Split('_');
                    if (parts.Length > 1 && parts[1].Contains(","))
                    {
                        parts[1] = parts[1].Split(',')[0].Trim();
                        blobName = string.Join("_", parts);
                    }
                }

                // Create a unique blob name with date prefix for better organization
                string uniqueBlobName = $"{DateTime.UtcNow:yyyy/MM/dd}/{blobName}";

                if (_blobContainerClient == null)
                {
                    throw new InvalidOperationException("Blob container client is not initialized");
                }

                BlobClient blobClient = _blobContainerClient.GetBlobClient(uniqueBlobName);

                // Convert string to stream
                using var stream = new MemoryStream(Encoding.UTF8.GetBytes(content));

                // Upload to blob storage with content type with retry logic
                bool success = await RetryOperationAsync(async () =>
                {
                    await blobClient.UploadAsync(stream, new BlobHttpHeaders { ContentType = "application/json" });
                }, "Upload to blob");

                if (!success)
                {
                    return string.Empty;
                }

                // Return the blob URL without SAS token
                return blobClient.Uri.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading to blob: {ex.Message}");
                return string.Empty;
            }
        }

        private async Task<string> DownloadFromBlobAsync(string blobUrl)
        {
            try
            {
                // Parse the URL to get the blob name
                var uri = new Uri(blobUrl);
                string blobPath = uri.AbsolutePath[(uri.AbsolutePath.IndexOf('/', 1) + 1)..];

                if (_blobContainerClient == null)
                {
                    throw new InvalidOperationException("Blob container client is not initialized");
                }

                BlobClient blobClient = _blobContainerClient.GetBlobClient(blobPath);

                // Download the blob with retry logic
                BlobDownloadInfo download = null;
                await RetryOperationAsync(async () =>
                {
                    download = await blobClient.DownloadAsync();
                }, "Download from blob");

                if (download == null)
                {
                    return "Failed to download blob after multiple attempts";
                }

                // Read the content
                using var reader = new StreamReader(download.Content);
                return await reader.ReadToEndAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error downloading from blob: {ex.Message}");
                return $"Error retrieving data: {ex.Message}";
            }
        }

        private async Task<(string TableData, string BlobUrl)> ProcessLargeDataAsync(string data, string blobNamePrefix)
        {
            if (string.IsNullOrEmpty(data))
            {
                return (null, null);
            }

            try
            {
                // Check data size
                int dataSizeKb = Encoding.UTF8.GetByteCount(data) / 1024;

                if (dataSizeKb > 256)
                {
                    // Data is too large, don't store it
                    return ("PAYLOAD_TOO_LARGE", null);
                }
                else if (dataSizeKb > _maxInlineDataSizeKb)
                {
                    // Store in Blob Storage
                    string blobUrl = await UploadToBlobAsync(data, blobNamePrefix);
                    if (string.IsNullOrEmpty(blobUrl))
                    {
                        // Fallback if blob storage fails
                        return ($"Failed to store in blob storage. First {Math.Min(data.Length, 1024)} chars: {data.Substring(0, Math.Min(data.Length, 1024))}...", null);
                    }

                    string tableData = $"[STORED IN BLOB] - Access at: {blobUrl}";
                    return (tableData, blobUrl);
                }
                else
                {
                    // Data is small enough for Table Storage
                    return (data, null);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing large data: {ex.Message}");
                return ($"Error processing data: {ex.Message}", null);
            }
        }

        private async Task ProcessEntityDataAsync(ApiLoggingTableEntity entity, string request, string response)
        {
            // Process request data
            if (!string.IsNullOrEmpty(request))
            {
                try
                {
                    var (requestData, requestBlobUrl) = await ProcessLargeDataAsync(request, $"{entity.RowKey}_request.json");
                    entity.RequestData = requestData;
                    entity.RequestBlobUrl = requestBlobUrl;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing request data: {ex.Message}");
                    entity.RequestData = $"Error processing request: {ex.Message}";
                }
            }

            // Process response data
            if (!string.IsNullOrEmpty(response))
            {
                try
                {
                    var (responseData, responseBlobUrl) = await ProcessLargeDataAsync(response, $"{entity.RowKey}_response.json");
                    entity.ResponseData = responseData;
                    entity.ResponseBlobUrl = responseBlobUrl;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing response data: {ex.Message}");
                    entity.ResponseData = $"Error processing response: {ex.Message}";
                }
            }
        }

        private ApiLoggingTableEntity CreateEntity(int repricerId, string reservationId, string methodName, string apiUrl, HttpStatusCode statusCode, DateTime logTime, bool isMultiSupplier)
        {
            return new ApiLoggingTableEntity
            {
                PartitionKey = ApiLoggingTableEntity.CreatePartitionKey(logTime, repricerId),
                RowKey = ApiLoggingTableEntity.CreateRowKey(repricerId, reservationId, methodName, logTime),
                RepricerId = repricerId,
                ReservationId = reservationId ?? string.Empty,
                Method = methodName,
                ApiUrl = apiUrl,
                StatusCode = (int)statusCode,
                LogTime = logTime,
                IsMultiSupplier = isMultiSupplier
            };
        }

        private async Task LogToTableAsync(ApiLoggingTableEntity entity, string tableName)
        {
            await RetryOperationAsync(async () =>
            {
                // Get or create the table client
                var tableClient = await GetOrCreateTableClientAsync(tableName);

                // Convert to TableEntity for more flexibility
                var tableEntity = new TableEntity(entity.PartitionKey, entity.RowKey)
                {
                    ["RepricerId"] = entity.RepricerId,
                    ["ReservationId"] = entity.ReservationId,
                    ["Method"] = entity.Method,
                    ["ApiUrl"] = entity.ApiUrl,
                    ["StatusCode"] = entity.StatusCode,
                    ["LogTime"] = entity.LogTime,
                    ["IsMultiSupplier"] = entity.IsMultiSupplier
                };

                // Add request and response data
                if (!string.IsNullOrEmpty(entity.RequestData))
                {
                    tableEntity["RequestData"] = entity.RequestData;
                }

                if (!string.IsNullOrEmpty(entity.ResponseData))
                {
                    tableEntity["ResponseData"] = entity.ResponseData;
                }

                // Add blob URLs if available
                if (!string.IsNullOrEmpty(entity.RequestBlobUrl))
                {
                    tableEntity["RequestBlobUrl"] = entity.RequestBlobUrl;
                }

                if (!string.IsNullOrEmpty(entity.ResponseBlobUrl))
                {
                    tableEntity["ResponseBlobUrl"] = entity.ResponseBlobUrl;
                }

                // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                await tableClient.UpsertEntityAsync(tableEntity);
            }, $"Write to {tableName}");
        }

        private async Task LogToTableWithAdditionalPropertiesAsync(ApiLoggingTableEntity entity, string tableName, Dictionary<string, object> additionalProperties)
        {
            await RetryOperationAsync(async () =>
            {
                // Get or create the table client
                var tableClient = await GetOrCreateTableClientAsync(tableName);

                // Convert to TableEntity for more flexibility
                var tableEntity = new TableEntity(entity.PartitionKey, entity.RowKey)
                {
                    ["RepricerId"] = entity.RepricerId,
                    ["ReservationId"] = entity.ReservationId,
                    ["Method"] = entity.Method,
                    ["ApiUrl"] = entity.ApiUrl,
                    ["StatusCode"] = entity.StatusCode,
                    ["LogTime"] = entity.LogTime,
                    ["IsMultiSupplier"] = entity.IsMultiSupplier
                };

                // Add request and response data
                if (!string.IsNullOrEmpty(entity.RequestData))
                {
                    tableEntity["RequestData"] = entity.RequestData;
                }

                if (!string.IsNullOrEmpty(entity.ResponseData))
                {
                    tableEntity["ResponseData"] = entity.ResponseData;
                }

                // Add blob URLs if available
                if (!string.IsNullOrEmpty(entity.RequestBlobUrl))
                {
                    tableEntity["RequestBlobUrl"] = entity.RequestBlobUrl;
                }

                if (!string.IsNullOrEmpty(entity.ResponseBlobUrl))
                {
                    tableEntity["ResponseBlobUrl"] = entity.ResponseBlobUrl;
                }

                // Add additional properties
                foreach (var property in additionalProperties)
                {
                    tableEntity[property.Key] = property.Value;
                }

                // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                await tableClient.UpsertEntityAsync(tableEntity);
            }, $"Write to {tableName} with additional properties");
        }

        public async Task<List<MongoDbLoggingEntity>> GetLogsAsync(DateTime? startDate = null, DateTime? endDate = null, int? repricerId = null, string? reservationId = null, string? methodName = null)
        {
            var result = new List<MongoDbLoggingEntity>();

            try
            {
                // Set default date range if not provided
                startDate ??= DateTime.UtcNow.AddDays(-3);
                endDate ??= DateTime.UtcNow;

                // Query for each day in the range (since partition keys are date-based)
                for (var date = startDate.Value.Date; date <= endDate.Value.Date; date = date.AddDays(1))
                {
                    // Build filter conditions
                    var filterConditions = new List<string>();

                    // If repricerId is provided, we can use the exact partition key
                    if (repricerId.HasValue)
                    {
                        string partitionKey = ApiLoggingTableEntity.CreatePartitionKey(date, repricerId.Value);
                        filterConditions.Add($"PartitionKey eq '{partitionKey}'");
                    }
                    else
                    {
                        // Without repricerId, we need a more general filter
                        // This is less efficient but necessary
                        string datePrefix = date.ToString("dd-MMM-yyyy");
                        filterConditions.Add($"PartitionKey eq '{datePrefix}'");
                    }

                    // Add other filter conditions
                    if (!string.IsNullOrEmpty(reservationId))
                    {
                        filterConditions.Add($"ReservationId eq '{reservationId}'");
                    }

                    if (!string.IsNullOrEmpty(methodName))
                    {
                        filterConditions.Add($"Method eq '{methodName}'");
                    }

                    // Combine all conditions
                    string filter = string.Join(" and ", filterConditions);

                    // Query the table
                    if (_apiLogsTable == null)
                    {
                        continue; // Skip this date if table client is not available
                    }

                    var queryResults = _apiLogsTable.Query<ApiLoggingTableEntity>(filter);

                    // Convert results to MongoDbLoggingEntity for compatibility
                    foreach (var entity in queryResults)
                    {
                        var logEntry = await ConvertToMongoDbLoggingEntityAsync(entity);
                        result.Add(logEntry);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving logs: {ex.Message}");
            }

            return result;
        }

        // Implement other methods from ILoggerMongoDb interface
        public void MultiSupplierPreBooklog(MultiSupplierlog multiSupplierlog)
        {
            Task.Run(async () =>
            {
                try
                {
                    var logTime = DateTime.UtcNow;
                    var entity = new TableEntity
                    {
                        PartitionKey = $"{logTime:dd-MMM-yyyy}",
                        RowKey = Guid.NewGuid().ToString(),
                        ["RePricerId"] = multiSupplierlog.RePricerId,
                        ["ReservationId"] = multiSupplierlog.ReservationId,
                        ["ReasonCode"] = multiSupplierlog.ReasonCode,
                        ["CreatedOn"] = logTime
                    };

                    // Use retry logic for table operations
                    await RetryOperationAsync(async () =>
                    {
                        // Get or create the table client
                        var tableClient = await GetOrCreateTableClientAsync("MultiSupplierPreBook");

                        // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                        await tableClient.UpsertEntityAsync(entity);
                    }, "Write to MultiSupplierPreBook");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error logging MultiSupplierPreBook: {ex.Message}");
                }
            });
        }

        // Implement required methods from ILoggerMongoDb interface
        public List<MongoDbLoggingEntity> FetchAdapterLogsByRequest(string methodName, DateTime? startTime, DateTime? endTime)
        {
            // For Azure Table implementation, we use the same table for all logs
            // so this is essentially the same as FetchLogsByRequest
            return FetchLogsByRequest(methodName, startTime, endTime);
        }

        public List<MongoDbLoggingEntity> FetchLogsByRequest(string methodName, DateTime? startTime, DateTime? endTime)
        {
            var result = new List<MongoDbLoggingEntity>();

            try
            {
                if (_apiLogsTable == null || string.IsNullOrEmpty(methodName))
                {
                    return result;
                }

                // Set default date range if not provided
                startTime ??= DateTime.UtcNow.AddDays(-3);
                endTime ??= DateTime.UtcNow;

                // Build filter conditions
                var filterConditions = new List<string> { $"Method eq '{methodName}'" };

                // Add date range filter - we need to query across multiple partition keys
                // since partition keys are date-based
                for (var date = startTime.Value.Date; date <= endTime.Value.Date; date = date.AddDays(1))
                {
                    // Query for each day in the range
                    string datePrefix = date.ToString("dd-MMM-yyyy");
                    string dateFilter = $"PartitionKey eq '{datePrefix}'";

                    // Combine with method filter
                    string filter = $"{dateFilter} and {string.Join(" and ", filterConditions)}";

                    // Query the table
                    var queryResults = _apiLogsTable.Query<ApiLoggingTableEntity>(filter);

                    // Convert results to MongoDbLoggingEntity
                    foreach (var entity in queryResults)
                    {
                        var logEntry = ConvertToMongoDbLoggingEntity(entity);
                        result.Add(logEntry);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in FetchLogsByRequest: {ex.Message}");
            }

            return result;
        }

        public List<MongoDbLoggingEntity> FetchLogsByCurrentDate(int repricerId, DateTime? fromDate = null, DateTime? toDate = null, string? methodName = null)
        {
            var result = new List<MongoDbLoggingEntity>();

            try
            {
                if (_apiLogsTable == null)
                {
                    return result;
                }

                // Set default date range if not provided
                fromDate ??= DateTime.UtcNow.AddDays(-3);
                toDate ??= DateTime.UtcNow;

                // Build filter conditions
                var filterConditions = new List<string> { $"RepricerId eq {repricerId}" };

                // Add method filter if provided
                if (!string.IsNullOrEmpty(methodName))
                {
                    filterConditions.Add($"Method eq '{methodName}'");
                }

                // Query for each day in the range
                for (var date = fromDate.Value.Date; date <= toDate.Value.Date; date = date.AddDays(1))
                {
                    // Create partition key for this repricerId and date
                    string partitionKey = ApiLoggingTableEntity.CreatePartitionKey(date, repricerId);
                    string filter = $"PartitionKey eq '{partitionKey}'";

                    // Add other conditions if any
                    if (filterConditions.Count > 0)
                    {
                        filter += $" and {string.Join(" and ", filterConditions)}";
                    }

                    // Query the table
                    var queryResults = _apiLogsTable.Query<ApiLoggingTableEntity>(filter);

                    // Convert results to MongoDbLoggingEntity
                    foreach (var entity in queryResults)
                    {
                        var logEntry = ConvertToMongoDbLoggingEntity(entity);
                        result.Add(logEntry);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in FetchLogsByCurrentDate: {ex.Message}");
            }

            return result;
        }

        public List<MongoDbLoggingEntity> FetchAdapterLogsByCurrentDate(int repricerId, DateTime? fromDate = null, DateTime? toDate = null, string? methodName = null)
        {
            // For Azure Table implementation, we use the same table for all logs
            // so this is essentially the same as FetchLogsByCurrentDate
            return FetchLogsByCurrentDate(repricerId, fromDate, toDate, methodName);
        }

        public int Reservationidcount(int repricerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                if (_apiLogsTable == null)
                {
                    return 0;
                }

                // Set default date range if not provided
                fromDate ??= DateTime.UtcNow.AddDays(-3);
                toDate ??= DateTime.UtcNow;

                // Get all logs for this repricerId in the date range
                var logs = FetchLogsByCurrentDate(repricerId, fromDate, toDate);

                // Count distinct reservation IDs
                return logs
                    .Where(log => !string.IsNullOrEmpty(log.ReservationId))
                    .Select(log => log.ReservationId)
                    .Distinct()
                    .Count();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Reservationidcount: {ex.Message}");
                return 0;
            }
        }

        public void LogLoginAttempt(string userName, bool success, string message, int repricerId = 0)
        {
            try
            {
                if (_isLogging != "1" || _saveInStorageConfigFlag == null || !_saveInStorageConfigFlag.Equals(LoggerConstants.SaveInStorageValue))
                {
                    return;
                }

                Task.Run(async () =>
                {
                    try
                    {
                        var logTime = DateTime.UtcNow;
                        var entity = new TableEntity
                        {
                            PartitionKey = $"{logTime:dd-MMM-yyyy}",
                            RowKey = Guid.NewGuid().ToString(),
                            ["UserName"] = userName,
                            ["Success"] = success,
                            ["Message"] = message,
                            ["RepricerId"] = repricerId,
                            ["LogTime"] = logTime
                        };

                        if (_azureStorageConnectionString != null)
                        {
                            await RetryOperationAsync(async () =>
                            {
                                // Get or create the table client
                                var tableClient = await GetOrCreateTableClientAsync("LoginDetails");

                                // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                                await tableClient.UpsertEntityAsync(entity);
                            }, "Write to LoginDetails");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error logging login attempt: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in LogLoginAttempt: {ex.Message}");
            }
        }

        public async Task<List<LoginDetails>> GetLoginDetailsByDateAsync(DateTime date, string userName, int repricerId = 0)
        {
            var result = new List<LoginDetails>();

            try
            {
                if (_azureStorageConnectionString == null)
                {
                    return result;
                }

                // Get a reference to the LoginDetails table
                var tableClient = new TableServiceClient(_azureStorageConnectionString)
                    .GetTableClient("LoginDetails");

                // Build filter conditions
                var filterConditions = new List<string>();

                // Add date filter
                string datePrefix = date.ToString("dd-MMM-yyyy");
                filterConditions.Add($"PartitionKey eq '{datePrefix}'");

                // Add username filter if provided
                if (!string.IsNullOrEmpty(userName))
                {
                    filterConditions.Add($"UserName eq '{userName}'");
                }

                // Add repricerId filter if provided
                if (repricerId > 0)
                {
                    filterConditions.Add($"RepricerId eq {repricerId}");
                }

                // Combine all conditions
                string filter = string.Join(" and ", filterConditions);

                // Query the table
                var queryResults = await Task.Run(() => tableClient.Query<TableEntity>(filter).ToList());

                // Convert results to LoginDetails
                foreach (var entity in queryResults)
                {
                    var loginDetail = new LoginDetails
                    {
                        UserName = entity.GetString("UserName"),
                        Success = entity.GetBoolean("Success") ?? false,
                        Message = entity.GetString("Message"),
                        RepricerId = entity.GetInt32("RepricerId") ?? 0,
                        Timestamp = entity.GetDateTimeOffset("LogTime")?.DateTime ?? DateTime.UtcNow
                    };

                    result.Add(loginDetail);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetLoginDetailsByDateAsync: {ex.Message}");
            }

            return result;
        }

        public void DeleteOldEntries()
        {
            try
            {
                if (_azureStorageConnectionString == null)
                {
                    return;
                }

                // Default to deleting entries older than 3 days
                var cutoffDate = DateTime.UtcNow.AddDays(-3);

                Task.Run(async () =>
                {
                    try
                    {
                        // Get a reference to the table service client
                        var tableServiceClient = new TableServiceClient(_azureStorageConnectionString);

                        // Get all tables
                        var tables = tableServiceClient.Query().ToList();

                        foreach (var table in tables)
                        {
                            // Skip system tables
                            if (table.Name.StartsWith("$"))
                                continue;

                            var tableClient = tableServiceClient.GetTableClient(table.Name);

                            // For date-based partition keys, we can filter by the partition key
                            // Format: yyyyMMdd_*
                            for (var date = DateTime.MinValue; date <= cutoffDate; date = date.AddDays(1))
                            {
                                if (date.Year < 2000) // Skip invalid dates
                                    continue;

                                string datePrefix = date.ToString("dd-MMM-yyyy");
                                string filter = $"PartitionKey eq '{datePrefix}'";

                                try
                                {
                                    // Query entities to delete
                                    var entities = tableClient.Query<TableEntity>(filter).ToList();

                                    // Delete entities in batches
                                    const int batchSize = 100;
                                    for (int i = 0; i < entities.Count; i += batchSize)
                                    {
                                        var batch = entities.Skip(i).Take(batchSize).ToList();
                                        foreach (var entity in batch)
                                        {
                                            await tableClient.DeleteEntityAsync(entity.PartitionKey, entity.RowKey);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error deleting old entries from table {table.Name} for date {datePrefix}: {ex.Message}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in DeleteOldEntries task: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in DeleteOldEntries: {ex.Message}");
            }
        }

        public Task<List<MongoDbLoggingEntity>> GetAdapterLogsAsync(DateTime startDate, DateTime endDate, int? repricerId = null, string? reservationId = null, string? methodName = null)
        {
            return _loggerMongoDb.GetAdapterLogsAsync(startDate, endDate, repricerId, reservationId, methodName);
        }

        public Task<List<MongoDbLoggingEntity>> GetOptimizedLogsAsync(DateTime? startDate = null, DateTime? endDate = null, int? repricerId = null, string? reservationId = null)
        {
            // Return empty list for now - implement actual functionality if needed
            return _loggerMongoDb.GetOptimizedLogsAsync(startDate, endDate, repricerId, reservationId);
        }

        public void Write(MongoDbLoggingEntity logEntry)
        {
            if (logEntry == null)
                return;

            try
            {
                if (_isLogging != "1" || _saveInStorageConfigFlag == null || !_saveInStorageConfigFlag.Equals(LoggerConstants.SaveInStorageValue))
                {
                    return;
                }

                var logTime = logEntry.CurrentTime;

                Task.Run(async () =>
                {
                    try
                    {
                        // Create the entity for Azure Table
                        var entity = new ApiLoggingTableEntity
                        {
                            PartitionKey = ApiLoggingTableEntity.CreatePartitionKey(logTime, logEntry.RePricerId),
                            RowKey = ApiLoggingTableEntity.CreateRowKey(logEntry.RePricerId, logEntry.ReservationId, logEntry.Method, logTime),
                            RepricerId = logEntry.RePricerId,
                            ReservationId = logEntry.ReservationId ?? string.Empty,
                            Method = logEntry.Method,
                            ApiUrl = logEntry.ApiUrl,
                            StatusCode = (int)logEntry.StatusCode,
                            LogTime = logTime,
                            IsMultiSupplier = logEntry.IsMultiSupplier
                        };

                        // Process request and response data
                        await ProcessEntityDataAsync(entity, logEntry.Request, logEntry.Response);

                        // Special case: Successful optimization - log to multiple tables
                        if (logEntry.Method == Constants.OptimizeBookingMethod && logEntry.StatusCode == HttpStatusCode.OK)
                        {
                            // 1. Log to OptimizedReservations table
                            await LogToTableAsync(entity, "OptimizedReservations");

                            // 2. Also log to ApiLogs table as backup
                            await LogToTableAsync(entity, "ApiLogs");

                            // 3. Process related search and prebook logs
                            if (!string.IsNullOrEmpty(logEntry.ReservationId))
                            {
                                await ProcessSearchAndPrebookLogsForOptimization(logEntry.RePricerId, logEntry.ReservationId);
                            }

                            return; // Exit since we've handled all required logging
                        }
                        // Special case: Search and PreBook operations - log to ApiLogs
                        else if (logEntry.Method == Constants.SearchSync || logEntry.Method == Constants.PreBook)
                        {
                            // Always log search and prebook operations to ApiLogs
                            await LogToTableAsync(entity, "ApiLogs");
                            return;
                        }

                        // For all other cases, determine the appropriate single table
                        string tableName;

                        if (logEntry.Method == Constants.OptimizerStatusCheckMethod)
                        {
                            tableName = "OptimizationStatusCheck";
                        }
                        else if (logEntry.StatusCode != HttpStatusCode.OK && logEntry.StatusCode != HttpStatusCode.Created && logEntry.StatusCode != HttpStatusCode.Accepted)
                        {
                            tableName = "ApiErrorLogs"; // For error logs
                        }
                        else if (logEntry.Method == Constants.GiataApiCall)
                        {
                            tableName = "GiataApi";
                        }
                        else
                        {
                            tableName = "ApiLogs"; // Default for all other logs, including MultiSupplier logs
                        }

                        // Log to the determined table
                        await LogToTableAsync(entity, tableName);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in Write(MongoDbLoggingEntity) method: {ex.Message}");

                        // Try to log to error log
                        try
                        {
                            await LogErrorAsync("Write(MongoDbLoggingEntity) method failed", ex);
                        }
                        catch (Exception logEx)
                        {
                            // Last resort: console log
                            Console.WriteLine($"Failed to log error: {logEx.Message}");

                            // Last resort fallback - log to file
                            LogToFile("error", $"Error in Write(MongoDbLoggingEntity) method: {ex.Message}\nFailed to log error: {logEx.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Write(MongoDbLoggingEntity) method: {ex.Message}");

                // Last resort fallback - log to file
                LogToFile("critical", $"Critical error in Write(MongoDbLoggingEntity) method: {ex.Message}");
            }
        }

        public Task<List<MongoDbLoggingEntity>> GetOptimizedSearchLogsAsync(DateTime? startDate = null, DateTime? endDate = null, int? repricerId = null, string? reservationId = null, string? method = null)
        {
            // Return empty list for now - implement actual functionality if needed
            return _loggerMongoDb.GetOptimizedSearchLogsAsync(startDate, endDate, repricerId, reservationId, method);
        }

        public void ChangeStatelogs(int repricerId, string request, string response, string methodName, string newState, string oldState, string modifyBy, string? reservationId = null)
        {
            // Implement if needed
            _loggerMongoDb.ChangeStatelogs(repricerId, request, response, methodName, newState, oldState, modifyBy, reservationId);
        }

        public Task<List<ChangedStatelogs>> GetChangedStatelogsAsync(DateTime? startDate = null, DateTime? endDate = null, int? repricerId = null, string? reservationId = null, string? methodName = null)
        {
            // Return empty list for now - implement actual functionality if needed
            return _loggerMongoDb.GetChangedStatelogsAsync(startDate, endDate, repricerId, reservationId, methodName);
        }

        public Task UpsertGiataRoomResponseAsync(List<GiataRoomMapping> mappings, string hotelId, GiataRoomResponse response)
        {
            return _loggerMongoDb.UpsertGiataRoomResponseAsync(mappings, hotelId, response);
        }

        public Task<GiataRoomResponse> GetGiataRoomResponseAsync(string hotelId)
        {
            // Return new empty instance for now - implement actual functionality if needed
            return _loggerMongoDb.GetGiataRoomResponseAsync(hotelId);
        }

        public Task<List<MultiRoomMongo>> GetMultiSupplierRooms(int repricerId, int hotelId, string checkIn)
        {
            // Return empty list for now - implement actual functionality if needed
            return _loggerMongoDb.GetMultiSupplierRooms(repricerId, hotelId, checkIn);
        }

        public Task MultiSupplierRoom(int repricerId, int hotelId, List<string> rooms, string checkIn)
        {
            // Implement if needed
            return _loggerMongoDb.MultiSupplierRoom(repricerId, hotelId, rooms, checkIn);
        }

        public Task UpsertGiataRoomResponseForMappingIdorAsync(List<GiataRoomMapping> mappings, string hotelId)
        {
            // Implement if needed
            return _loggerMongoDb.UpsertGiataRoomResponseForMappingIdorAsync(mappings, hotelId);
        }

        public async Task CleanOldCollections(int daysToKeep)
        {
            try
            {
                daysToKeep = 30;
                /*
                if (_azureStorageConnectionString == null)
                {
                    return;
                }

                // Calculate cutoff date
                var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
                var startFrom = DateTime.UtcNow.AddDays(-(daysToKeep * 2));

                // Get a reference to the table service client
                var tableServiceClient = new TableServiceClient(_azureStorageConnectionString);

                // Get all tables
                var tables = tableServiceClient.Query().ToList();

                foreach (var table in tables)
                {
                    // Skip system tables
                    if (table.Name.StartsWith("$"))
                        continue;

                    var tableClient = tableServiceClient.GetTableClient(table.Name);

                    // For date-based partition keys, we can filter by the partition key
                    // Format: yyyyMMdd_*
                    for (var date = startFrom; date <= cutoffDate; date = date.AddDays(1))
                    {
                        if (date.Year < 2000) // Skip invalid dates
                            continue;

                        string datePrefix = date.ToString("dd-MMM-yyyy");
                        string filter = $"PartitionKey eq '{datePrefix}'";

                        try
                        {
                            // Query entities to delete
                            var entities = tableClient.Query<TableEntity>(filter).ToList();

                            // Delete entities in batches
                            int batchSize = 1000;
                            if (entities.Count > 0)
                            {
                                for (int i = 0; i < entities.Count; i += batchSize)
                                {
                                    var batch = entities.Skip(i).Take(batchSize).ToList();
                                    if (batch.Count > 0)
                                    {
                                        foreach (var entity in batch)
                                        {
                                            await tableClient.DeleteEntityAsync(entity.PartitionKey, entity.RowKey);
                                        }
                                        Console.WriteLine($"Deleted {entities.Count} entities from table {table.Name} for date {datePrefix}");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error cleaning old entries from table {table.Name} for date {datePrefix}: {ex.Message}");
                        }
                    }
                }

                // Also clean up blob storage
                if (_blobContainerClient != null)
                {
                    try
                    {
                        // List all blobs
                        var blobs = _blobContainerClient.GetBlobs().ToList();

                        // Filter blobs by date in path (yyyy/MM/dd)
                        foreach (var blob in blobs)
                        {
                            try
                            {
                                // Check if the blob name contains a date pattern
                                var match = System.Text.RegularExpressions.Regex.Match(blob.Name, @"(\d{4})/(\d{2})/(\d{2})");
                                if (match.Success)
                                {
                                    // Extract date components
                                    int year = int.Parse(match.Groups[1].Value);
                                    int month = int.Parse(match.Groups[2].Value);
                                    int day = int.Parse(match.Groups[3].Value);

                                    // Create date
                                    var blobDate = new DateTime(year, month, day);

                                    // Check if older than cutoff
                                    if (blobDate <= cutoffDate)
                                    {
                                        // Delete the blob
                                        await _blobContainerClient.DeleteBlobAsync(blob.Name);
                                        Console.WriteLine($"Deleted blob {blob.Name}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error processing blob {blob.Name}: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error cleaning old blobs: {ex.Message}");
                    }
                }
                //*/
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CleanOldCollections: {ex.Message}");
            }
        }

        public Task UpsertGiataRoomStatusAsync(GiataRoomStatus newStatus)
        {
            // Implement if needed
            return _loggerMongoDb.UpsertGiataRoomStatusAsync(newStatus);
        }

        public Task<List<GiataRoomStatus>> GetAllGiataRoomStatusesAsync(bool isCacheRefresh = false)
        {
            // Return empty list for now - implement actual functionality if needed
            return _loggerMongoDb.GetAllGiataRoomStatusesAsync(isCacheRefresh);
        }

        // Helper method for fallback file logging
        private static void LogToFile(string type, string message)
        {
            try
            {
                string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                Directory.CreateDirectory(logPath);
                string logFile = Path.Combine(logPath, $"azure_logger_{type}_{DateTime.Now:yyyyMMdd}.log");
                File.AppendAllText(logFile, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n\n");
            }
            catch
            {
                // If even file logging fails, we can't do much more
            }
        }

        // Test method to verify OptimizedReservationsSearchAndPrebookLogs functionality
        public async Task<string> TestOptimizedReservationsSearchAndPrebookLogsAsync(int repricerId, string reservationId)
        {
            try
            {
                Console.WriteLine($"=== Testing OptimizedReservationsSearchAndPrebookLogs functionality ===");
                Console.WriteLine($"RepricerId: {repricerId}, ReservationId: {reservationId}");

                var results = new List<string>();

                // 1. Test table creation
                try
                {
                    var tableClient = await GetOrCreateTableClientAsync("OptimizedReservationsSearchAndPrebookLogs");
                    results.Add("✓ OptimizedReservationsSearchAndPrebookLogs table client created successfully");
                    Console.WriteLine("✓ OptimizedReservationsSearchAndPrebookLogs table client created successfully");
                }
                catch (Exception ex)
                {
                    results.Add($"✗ Failed to create OptimizedReservationsSearchAndPrebookLogs table: {ex.Message}");
                    Console.WriteLine($"✗ Failed to create OptimizedReservationsSearchAndPrebookLogs table: {ex.Message}");
                }

                // 2. Test fetching search and prebook logs
                try
                {
                    var searchAndPrebookLogs = await GetCachedSearchAndPrebookLogsAsync(repricerId, reservationId);
                    results.Add($"✓ Found {searchAndPrebookLogs.Count} search and prebook logs");
                    Console.WriteLine($"✓ Found {searchAndPrebookLogs.Count} search and prebook logs");

                    foreach (var log in searchAndPrebookLogs)
                    {
                        Console.WriteLine($"  - Method: {log.Method}, Time: {log.CurrentTime}, ReservationId: {log.ReservationId}");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"✗ Failed to fetch search and prebook logs: {ex.Message}");
                    Console.WriteLine($"✗ Failed to fetch search and prebook logs: {ex.Message}");
                }

                // 3. Test the full ProcessSearchAndPrebookLogsForOptimization method
                try
                {
                    await ProcessSearchAndPrebookLogsForOptimization(repricerId, reservationId);
                    results.Add("✓ ProcessSearchAndPrebookLogsForOptimization completed successfully");
                    Console.WriteLine("✓ ProcessSearchAndPrebookLogsForOptimization completed successfully");
                }
                catch (Exception ex)
                {
                    results.Add($"✗ ProcessSearchAndPrebookLogsForOptimization failed: {ex.Message}");
                    Console.WriteLine($"✗ ProcessSearchAndPrebookLogsForOptimization failed: {ex.Message}");
                }

                // 4. Test querying the OptimizedReservationsSearchAndPrebookLogs table
                try
                {
                    var tableClient = await GetOrCreateTableClientAsync("OptimizedReservationsSearchAndPrebookLogs");
                    var entities = tableClient.Query<TableEntity>().Take(10).ToList();
                    results.Add($"✓ OptimizedReservationsSearchAndPrebookLogs table contains {entities.Count} entities (showing first 10)");
                    Console.WriteLine($"✓ OptimizedReservationsSearchAndPrebookLogs table contains {entities.Count} entities (showing first 10)");

                    foreach (var entity in entities)
                    {
                        Console.WriteLine($"  - PartitionKey: {entity.PartitionKey}, RowKey: {entity.RowKey}");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"✗ Failed to query OptimizedReservationsSearchAndPrebookLogs table: {ex.Message}");
                    Console.WriteLine($"✗ Failed to query OptimizedReservationsSearchAndPrebookLogs table: {ex.Message}");
                }

                Console.WriteLine($"=== Test completed ===");
                return string.Join("\n", results);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Test failed with exception: {ex.Message}\n{ex.StackTrace}";
                Console.WriteLine(errorMessage);
                return errorMessage;
            }
        }

        // Helper method to convert ApiLoggingTableEntity to MongoDbLoggingEntity
        private async Task<MongoDbLoggingEntity> ConvertToMongoDbLoggingEntityAsync(ApiLoggingTableEntity entity)
        {
            var logEntry = new MongoDbLoggingEntity
            {
                RePricerId = entity.RepricerId,
                ReservationId = entity.ReservationId,
                Method = entity.Method,
                ApiUrl = entity.ApiUrl,
                StatusCode = (HttpStatusCode)entity.StatusCode,
                CurrentTime = entity.LogTime,
                IsMultiSupplier = entity.IsMultiSupplier
            };

            // Get request data (either inline or from blob)
            if (!string.IsNullOrEmpty(entity.RequestData))
            {
                logEntry.Request = entity.RequestData;
            }
            else if (!string.IsNullOrEmpty(entity.RequestBlobUrl))
            {
                logEntry.Request = await DownloadFromBlobAsync(entity.RequestBlobUrl);
            }

            // Get response data (either inline or from blob)
            if (!string.IsNullOrEmpty(entity.ResponseData))
            {
                logEntry.Response = entity.ResponseData;
            }
            else if (!string.IsNullOrEmpty(entity.ResponseBlobUrl))
            {
                logEntry.Response = await DownloadFromBlobAsync(entity.ResponseBlobUrl);
            }

            return logEntry;
        }

        // Synchronous version for use in non-async methods
        private MongoDbLoggingEntity ConvertToMongoDbLoggingEntity(ApiLoggingTableEntity entity)
        {
            var logEntry = new MongoDbLoggingEntity
            {
                RePricerId = entity.RepricerId,
                ReservationId = entity.ReservationId,
                Method = entity.Method,
                ApiUrl = entity.ApiUrl,
                StatusCode = (HttpStatusCode)entity.StatusCode,
                CurrentTime = entity.LogTime,
                IsMultiSupplier = entity.IsMultiSupplier
            };

            // Get request data (either inline or from blob)
            if (!string.IsNullOrEmpty(entity.RequestData))
            {
                logEntry.Request = entity.RequestData;
            }
            else if (!string.IsNullOrEmpty(entity.RequestBlobUrl))
            {
                // For synchronous method, we need to use GetAwaiter().GetResult()
                logEntry.Request = DownloadFromBlobAsync(entity.RequestBlobUrl).GetAwaiter().GetResult();
            }

            // Get response data (either inline or from blob)
            if (!string.IsNullOrEmpty(entity.ResponseData))
            {
                logEntry.Response = entity.ResponseData;
            }
            else if (!string.IsNullOrEmpty(entity.ResponseBlobUrl))
            {
                // For synchronous method, we need to use GetAwaiter().GetResult()
                logEntry.Response = DownloadFromBlobAsync(entity.ResponseBlobUrl).GetAwaiter().GetResult();
            }

            return logEntry;
        }

        // Additional utility methods
        public void WriteLog(string message)
        {
            Task.Run(async () =>
            {
                try
                {
                    var logTime = DateTime.UtcNow;
                    var entity = new TableEntity
                    {
                        PartitionKey = $"{logTime:dd-MMM-yyyy}",
                        RowKey = Guid.NewGuid().ToString(),
                        ["Message"] = message,
                        ["LogTime"] = logTime
                    };

                    if (_azureStorageConnectionString != null)
                    {
                        await RetryOperationAsync(async () =>
                        {
                            // Get or create the table client
                            var tableClient = await GetOrCreateTableClientAsync("GeneralLogs");

                            // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                            await tableClient.UpsertEntityAsync(entity);
                        }, "Write to GeneralLogs");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing general log: {ex.Message}");
                }
            });
        }

        public void ErrorLog(string message, Exception ex)
        {
            try
            {
                if (_isLogging != "1" || _saveInStorageConfigFlag == null || !_saveInStorageConfigFlag.Equals(LoggerConstants.SaveInStorageValue))
                {
                    return;
                }

                Task.Run(async () =>
                {
                    try
                    {
                        var logTime = DateTime.UtcNow;
                        var entity = new TableEntity
                        {
                            PartitionKey = $"{logTime:dd-MMM-yyyy}",
                            RowKey = Guid.NewGuid().ToString(),
                            ["Message"] = message,
                            ["Exception"] = ex?.ToString() ?? "No exception details",
                            ["LogTime"] = logTime
                        };

                        await RetryOperationAsync(async () =>
                        {
                            // Get or create the table client
                            var tableClient = await GetOrCreateTableClientAsync("ErrorLoggingGeneral");

                            // Use UpsertEntityAsync instead of AddEntityAsync to handle existing entities
                            await tableClient.UpsertEntityAsync(entity);
                        }, "Write to ErrorLoggingGeneral");
                    }
                    catch (Exception error)
                    {
                        Console.WriteLine($"Error writing error log: {error.Message}");

                        // Last resort fallback - log to file
                        LogToFile("error_log", $"Error writing error log: {error.Message}\nOriginal error: {message}\n{ex}");
                    }
                });
            }
            catch (Exception error)
            {
                Console.WriteLine($"Error in ErrorLog method: {error.Message}");

                // Last resort fallback - log to file
                LogToFile("critical", $"Critical error in ErrorLog method: {error.Message}");
            }
        }
    }
}