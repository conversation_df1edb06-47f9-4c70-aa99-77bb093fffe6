﻿--/*
-- [dbo].[usp_GetRePricerDataByUserID] 1
-- LoadRePricerDetail
CREATE PROCEDURE [dbo].[usp_GetRePricerDataByUserID] @RepricerUserID INT
AS
--*/

-- DECLARE @RepricerUserID INT = 1

BEGIN
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    -- Retrieve data from RePricerDetail and ClientConfiguration tables
    SELECT rd.RepricerUserID
         , rd.RepricerUserName
         , rd.AdminURL
         , rd.AdminUserID
         , rd.AdminPassword
         , rd.ResellerURL
         , rd.ResellerUserID
         , rd.ResellerPassword
         , rd.ApiScope
         , rd.ResellerApiScope
         , rd.IsActive
         , isnull(rd.isJobsEnable, 1)                                  as IsJobsEnable
         , cc.Service
         , cc.CancelPenalty
         , ISNULL(cc.IsExtraServices, 0)                               as IsExtraServices
         , cec.priceDifferenceValue
         , cec.priceDifferencePercentage
         , case
               when cec.pricedifferencecurrency is null
                    or cec.pricedifferencecurrency = '' then
                   'EUR'
               else
                   cec.pricedifferencecurrency
           end                                                         as Currency
         , 0                                                           as isUsePercentage
         , ISNULL(cec.traveldaysmaxsearchindays, 60)                   as traveldaysmaxsearchindays
         , isnull(cec.traveldaysminsearchindays, 5)                    as traveldaysminsearchindays
         , cec.MaxNumberOfTimesOptimization
         , isnull(cec.ClientConfig_DaysDifferenceInPreBookCreation, 0) as ClientConfig_DaysDifferenceInPreBookCreation
         , 1                                                           as IsUseDaysLimitCancellationPolicyEdgeCase
         , isnull(cec.DaysLimitCancellationPolicyEdgeCase, 5)          as DaysLimitCancellationPolicyEdgeCase
         -- isnull(cec.IsCreatePrebookFoPriceEdgeCase,0)
         , 1                                                           as IsCreatePrebookForPriceEdgeCase
         , cec.ClientEmail                                             as ReportEmailToSend
         , ccs.Reservation_CronTime
         , ccs.PreBook_CronTime
         , ccs.CurrencyExchange_CronTime
         , ccs.PreBook_CronTime
         , case
               when isnull(ccs.TimeZoneId, 0) = 0 then
                   47
               else
                   ccs.TimeZoneId
           end                                                         as TimeZoneId
         , tz.Name                                                     as TimeZoneName
         -- case when isnull( rd.OptimizationType ,0) <= 2 then 2 else  rd.OptimizationType   end as
         , rd.OptimizationType
         , rd.EmailTo
         , rd.EmailCC
         , rd.EmailBCC
         , rd.RestrictedHotelId
         , rd.RestrictedCityId
         , rd.RestrictedCountryId
         , rd.RestrictedSupplier
         , rd.RestrictedReseller
         , ISNULL(rd.AutoJobServerId, 0)                               as AutoJobServerId
         , ISNULL(rd.IsMultiSupplierRoomSync, 0)                       as IsMultiSupplierRoomSync
         , ISNULL(rd.IsUseResellerCPHourDifference, 0)                 as IsUseResellerCPHourDifference
         , ISNULL(rd.ResellerCPHourDifference, 0)                      as ResellerCPHourDifference
         , case
               when ISNULL(rd.DelayBetweenOptimizationJob, 0) = 0 then
                   4
               else
                   ISNULL(rd.DelayBetweenOptimizationJob, 4)
           END                                                         as DelayBetweenOptimizationJob
         , case
               when ISNULL(rd.DelayBetweenDailyDownloadReservation, 0) = 0 then
                   3
               else
                   ISNULL(rd.DelayBetweenDailyDownloadReservation, 3)
           END                                                         as DelayBetweenDailyDownloadReservation
         , case
               when ISNULL(rd.DelayBetweenRequestsSameSupplier, 0) < 10 then
                   10
               else
                   ISNULL(rd.DelayBetweenRequestsSameSupplier, 15)
           end                                                         as DelayBetweenRequestsSameSupplier
         , case
               when ISNULL(rd.DelayBetweenRequestsMultiSupplier, 0) < 10 then
                   10
               else
                   ISNULL(rd.DelayBetweenRequestsMultiSupplier, 15)
           end                                                         as DelayBetweenRequestsMultiSupplier
         , ISNULL(rd.CPTimeAllowedBufferPercentage, 0)                 as CPTimeAllowedBufferPercentage
         , ISNULL(rd.CPAmountAllowedBufferPercentage, 0)               as CPAmountAllowedBufferPercentage
		 , ISNULL(rd.ModifiedDate, rd.CreatedDate)					   as UpdateDate
         -- NEW COLUMNS FOR EXTRACLIENTDETAIL ENHANCEMENT
         , ISNULL(cec.PriceDifferenceValueAllowedBufferPercentage, 0)  as PriceDifferenceValueAllowedBufferPercentage
         , ISNULL(cec.IsCheckCrossSupperBeforeOptimization, 0)         as IsCheckCrossSupperBeforeOptimization
    FROM dbo.RePricerDetail                              rd
        INNER JOIN dbo.ClientConfiguration               cc
            ON rd.RepricerUserID = cc.RepricerUserID
        INNER Join dbo.clientconfiguration_ExtraCriteria cec
            ON rd.RepricerUserID = cec.RepricerID
        INNER Join dbo.ClientConfig_CronScheduler        ccs
            ON rd.RepricerUserID = ccs.RepricerID
        INNER Join dbo.TimeZones                         tz
            ON ccs.TimeZoneId = tz.TimeZoneId
    WHERE rd.RepricerUserID = @RepricerUserID;
-- and rd.isActive = 1;
END;