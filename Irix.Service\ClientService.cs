﻿using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Service.Contract;
using Logger.Contract;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Repricer.Cache;
using RePricer.Util;
using System.Diagnostics;
using constant = RePricer.Constants.ServiceConstants;

namespace Irix.Service
{
    public class ClientService : IClientServices
    {
        private readonly IClientPersistance _clientPersistance;
        private readonly ILogger _log;
        private readonly IMemoryCache _memoryCache;
        private readonly IReservationPersistence _reservationPersistance;
        private readonly IMasterService _masterService;
        private readonly string _className;
        //private readonly IConfigurationService _configurationService;
        public ClientService(IClientPersistance clientPersistance
            , ILogger log
            , IMemoryCache memoryCache
            , IReservationPersistence reservationPersistance
            , IMasterService masterService
        //, IConfigurationService configurationService
        )
        {
            _clientPersistance = clientPersistance;
            _log = log;
            _memoryCache = memoryCache;
            _reservationPersistance = reservationPersistance;
            _masterService = masterService;
            _className = nameof(ClientService);
            //_configurationService = configurationService;
        }

        public int InsertRePricerDetail(RePricerDetail rePricerDetail, ConfigurationResult? configurationResult = null)
        {
            var watch = Stopwatch.StartNew();

            int result = 0;
            try
            {

                if (configurationResult != null && rePricerDetail != null)
                {
                    rePricerDetail.ExtraClientDetail.PriceDifferenceValue = configurationResult?.MinimumOfferGainValue;
                    rePricerDetail.ExtraClientDetail.Currency = configurationResult?.MinimumOfferGainCurrency;
                    rePricerDetail.ExtraClientDetail.TravelDaysMinSearchInDays = configurationResult?.DaysBeforeCancellationPolicy ?? rePricerDetail?.ExtraClientDetail?.TravelDaysMinSearchInDays ?? 2;
                    rePricerDetail.ExtraClientDetail.DaysLimitCancellationPolicyEdgeCase = rePricerDetail?.ExtraClientDetail?.DaysLimitCancellationPolicyEdgeCase ?? 2;
                }
                if (rePricerDetail != null)
                {
                    var hotelDataJson = rePricerDetail.RestrictedHotel != null
                        ? JsonConvert.SerializeObject(rePricerDetail.RestrictedHotel)
                        : string.Empty;

                    var cityDataJson = rePricerDetail.RestrictedCity != null
                        ? JsonConvert.SerializeObject(rePricerDetail.RestrictedCity)
                        : string.Empty;

                    var countryDataJson = rePricerDetail.RestrictedCountry != null
                        ? JsonConvert.SerializeObject(rePricerDetail.RestrictedCountry)
                        : string.Empty;

                    var resellerDataJson = rePricerDetail.RestrictedReseller != null
                        ? JsonConvert.SerializeObject(rePricerDetail.RestrictedReseller)
                        : string.Empty;

                    var supplierDataJson = rePricerDetail.RestrictedSupplier != null
                        ? JsonConvert.SerializeObject(rePricerDetail.RestrictedSupplier)
                        : string.Empty;

                    // Serialize the entire rePricerDetail object if needed
                    var rePricerJsonData = JsonConvert.SerializeObject(rePricerDetail, Formatting.Indented);

                    // Insert JSON data into the database
                    result = _clientPersistance.InsertResellerDetailDataAsync(rePricerJsonData, hotelDataJson, cityDataJson, countryDataJson, resellerDataJson, supplierDataJson);

                    // Update additional criteria if needed
                    _reservationPersistance.InsertOrUpdateClientConfigurationExtraCriteria(
                       result,
                       rePricerDetail.ExtraClientDetail,
                       rePricerDetail.OptimizationType
                   );
                }

                //if (rePricerDetail != null)
                //{
                //    var hotelIds = rePricerDetail.RestrictedHotelId != null ? string.Join(",", rePricerDetail.RestrictedHotelId) : string.Empty;
                //    var cityIds = rePricerDetail.RestrictedCityId != null ? string.Join(",", rePricerDetail.RestrictedCityId) : string.Empty;
                //    var countryIds = rePricerDetail.RestrictedCountryId != null ? string.Join(",", rePricerDetail.RestrictedCountryId) : string.Empty;
                //    var resellers = rePricerDetail.RestrictedReseller != null ? string.Join(",", rePricerDetail.RestrictedReseller) : string.Empty;
                //    var suppliers = rePricerDetail.RestrictedSupplier != null ? string.Join(",", rePricerDetail.RestrictedSupplier) : string.Empty;


                //    var RePricerjsonData = JsonConvert.SerializeObject(rePricerDetail, Formatting.Indented);
                //    result = _clientPersistence.InsertResellerDetailDataAsync(RePricerjsonData, hotelIds, cityIds, countryIds, resellers, suppliers);
                //    _reservationPersistence.InsertOrUpdateClientConfigurationExtraCriteria(result, rePricerDetail.ExtraClientDetail, rePricerDetail.OptimizationType);
                //}
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(InsertRePricerDetail),
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            _log.Info($"{constant.ClientService}|{nameof(InsertRePricerDetail)}|{elapsedTimeInSeconds}|{rePricerDetail} in {watch.Elapsed}");

            return result;
        }

        public int SyncRePricerDetail(int RepricerId, ConfigurationResult? configresult = null)
        {
            var watch = Stopwatch.StartNew();

            int result = 0;
            var rePricerDetail = (_clientPersistance.LoadRePricerDetail(RepricerId)).GetAwaiter().GetResult();
            try
            {

                if (configresult != null && rePricerDetail != null)
                {
                    rePricerDetail.ExtraClientDetail.PriceDifferenceValue = configresult?.MinimumOfferGainValue;
                    rePricerDetail.ExtraClientDetail.Currency = configresult?.MinimumOfferGainCurrency;
                    rePricerDetail.ExtraClientDetail.DaysLimitCancellationPolicyEdgeCase = configresult?.DaysBeforeCancellationPolicy ?? rePricerDetail.ExtraClientDetail.DaysLimitCancellationPolicyEdgeCase;
                }
                var RePricerjsonData = JsonConvert.SerializeObject(rePricerDetail, Formatting.Indented);
                result = _clientPersistance.InsertResellerDetailDataAsync(RePricerjsonData);
                _reservationPersistance.InsertOrUpdateClientConfigurationExtraCriteria(result, rePricerDetail.ExtraClientDetail, rePricerDetail.OptimizationType);

            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(InsertRePricerDetail),
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            _log.Info($"{constant.ClientService}|{nameof(InsertRePricerDetail)}|{elapsedTimeInSeconds}|{rePricerDetail} in {watch.Elapsed}");

            return result;
        }

        public RePricerAuthDetail GetRePricerAuthDetailByRedisCache(int RePricerUserId)
        {
            var watch = Stopwatch.StartNew();

            try
            {
                var repricerdetail = (_clientPersistance.LoadRePricerDetail(RePricerUserId)).GetAwaiter().GetResult();
                repricerdetail.RepricerUserID = RePricerUserId;
                if (repricerdetail.RepricerUserID != 0)
                {
                    var repricerauthdetail = ConvertAuthRequest(repricerdetail, RePricerUserId);
                    return repricerauthdetail;
                }

                return null;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(GetRePricerAuthDetailByRedisCache),
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public RePricerDetail GetRePricerDetail(int RePricerUserId)
        {
            var watch = Stopwatch.StartNew();

            try
            {
                var repricerdetail = (_clientPersistance.LoadRePricerDetail(RePricerUserId)).GetAwaiter().GetResult();
                return repricerdetail;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(GetRePricerAuthDetailByRedisCache),
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public RePricerAuthDetail ConvertAuthRequest(RePricerDetail rePricerDetail, int RePricerUserId)
        {
            try
            {
                return new RePricerAuthDetail
                {
                    RepricerUserID = RePricerUserId,
                    AdminURL = rePricerDetail.AdminUrl,
                    AdminUserID = rePricerDetail.AdminUserId,
                    AdminPassword = rePricerDetail.AdminPassword,
                    ResellerURL = rePricerDetail.ResellerUrl,
                    ResellerUserID = rePricerDetail.ResellerUserId,
                    ResellerPassword = rePricerDetail.ResellerPassword,
                    ApiScope = rePricerDetail.AdminApiScope,
                    ResellerApiScope = rePricerDetail.ResellerApiScope
                };
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(ConvertAuthRequest),
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public List<ClientConfigScheduler_Hangfire> GetClientScheduler(bool isCacheRefresh = false)
        {
            List<ClientConfigScheduler_Hangfire> clientConfigSchedulers = new List<ClientConfigScheduler_Hangfire>();
            try
            {
                clientConfigSchedulers = _clientPersistance.LoadRePricerScheduleConfig(isCacheRefresh);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(GetClientScheduler),
                };
                _log.Error(irixErrorEntity, ex);
                // throw;
            }
            return clientConfigSchedulers;
        }

        public SmtpConfigModel GetClientEmail(int RePricerId, bool iscached = true)
        {
            var watch = Stopwatch.StartNew();

            try
            {
                var clientConfigEmails = _clientPersistance.LoadRePricerEmailConfig(RePricerId)?.GetAwaiter().GetResult();
                if (clientConfigEmails != null && clientConfigEmails?.RepricerId != 0)
                {
                    string smtpConfiginMinValue = ConfigurationManagerHelper.GetValuefromAppSettings(constant.CachingTimeinMin);
                    double smtpConfiginMin;

                    if (double.TryParse(smtpConfiginMinValue, out smtpConfiginMin))
                    {
                        RedisCacheHelper.Set($"{constant.Repriceremaildetail}_{RePricerId}", clientConfigEmails, TimeSpan.FromMinutes(smtpConfiginMin));
                    }
                    else
                    {
                        RedisCacheHelper.Set($"{constant.Repriceremaildetail}_{RePricerId}", clientConfigEmails, TimeSpan.FromMinutes(50));
                    }
                    watch.Stop();
                    var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
                    _log.Info($"{constant.ClientService}|{nameof(GetClientEmail)}|{elapsedTimeInSeconds}|{RePricerId} in {watch.Elapsed}");

                    return clientConfigEmails;
                }

                return null;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(GetClientEmail),
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public ReportData GetReportData(int Repriceruserid)
        {
            var extraclientconfig = GetClientEmail(Repriceruserid);
            var reportdata = _reservationPersistance.GetReportData(Repriceruserid, extraclientconfig.traveldaysmaxsearchindays, extraclientconfig.traveldaysminsearchindays);
            return reportdata;
        }

        public void UpdateBookingActionTakenInDB(List<BookingActionTakenModel> reservationUpdates, bool isCacheRefresh = false)
        {
            if (reservationUpdates != null)
            {
                var watch = Stopwatch.StartNew();
                int repricerId = reservationUpdates?.FirstOrDefault()?.RepricerId ?? 0;
                var _method = nameof(UpdateBookingActionTakenInDB);


                try
                {
                    _reservationPersistance.UpdateBookingActionTakenInDB(updateModels: reservationUpdates, isCacheRefresh: isCacheRefresh);

                    //if (isCacheRefresh)
                    //{
                    //    _masterService.RefreshCacheTrigger(repricerId, isCacheRefresh);
                    //    //_masterService.RefreshCache(repricerId);
                    //}

                    watch.Stop();
                    var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
                    _log.Info($"{constant.ClientService}|{_method}|{elapsedTimeInSeconds}|{reservationUpdates} in {watch.Elapsed}");
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = _method,
                        Params = SerializeDeSerializeHelper.Serialize(reservationUpdates)
                    };
                    _log.Error(irixErrorEntity, ex);
                    //throw ex;
                }
            }
        }

        public string CalculateCronForOneYearLater()
        {
            try
            {
                DateTime currentDate = DateTime.UtcNow;
                DateTime oneMonthBefore = currentDate.AddMonths(-1);

                // Adjust the year and month if the resulting month is 0 (invalid)
                if (oneMonthBefore.Month == 0)
                {
                    oneMonthBefore = new DateTime(oneMonthBefore.Year - 1, 12, oneMonthBefore.Day);
                }

                // If the calculated day does not exist in the resulting month (e.g., 31st February),
                // we adjust to the last valid day of the month
                int lastDayOfMonth = DateTime.DaysInMonth(oneMonthBefore.Year, oneMonthBefore.Month);
                if (oneMonthBefore.Day > lastDayOfMonth)
                {
                    oneMonthBefore = new DateTime(oneMonthBefore.Year, oneMonthBefore.Month, lastDayOfMonth);
                }

                // Cron format: "Minute Hour Day Month DayOfWeek"
                string cronExpression = $"{oneMonthBefore.Minute} {oneMonthBefore.Hour} {oneMonthBefore.Day} {oneMonthBefore.Month} *";

                return cronExpression;
            }
            catch (Exception)
            {
                // Return a default valid cron expression if an error occurs
                return "0 0 1 * *"; // Default cron expression for the first day of each month at midnight
            }
        }

        public void DeleteLoggingData(int RepricerId)
        {
            try
            {
                _clientPersistance.DeleteLoggingData(RepricerId);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(DeleteLoggingData),
                    Params = SerializeDeSerializeHelper.Serialize(RepricerId)
                };
                _log.Error(irixErrorEntity, ex);
                //throw ex;
            }
        }

        public string GetCronTimeAfterHours(string cronTime, int hoursToAdd)
        {
            string crontime = "0 20 * * *";
            try
            {
                var cronParts = cronTime.Split(' ');
                int minute = int.Parse(cronParts[0]);
                int hour = int.Parse(cronParts[1]);

                hour += hoursToAdd;

                if (hour >= 24)
                {
                    hour -= 24;
                }
                cronParts[1] = hour.ToString();
                crontime = string.Join(" ", cronParts);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(GetCronTimeAfterHours),
                };
                _log.Error(irixErrorEntity, ex);
                //throw ex;
            }
            return crontime;
        }
    }
}