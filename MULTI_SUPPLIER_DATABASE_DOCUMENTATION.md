# Multi-Supplier Optimization Database Documentation

## 📋 Table of Contents
1. [Database Architecture Overview](#database-architecture-overview)
2. [Core Tables](#core-tables)
3. [Multi-Supplier Specific Tables](#multi-supplier-specific-tables)
4. [Stored Procedures](#stored-procedures)
5. [Data Flow Analysis](#data-flow-analysis)
6. [Table Relationships](#table-relationships)
7. [Performance Considerations](#performance-considerations)
8. [Data Lifecycle](#data-lifecycle)

---

## 🏗️ Database Architecture Overview

The multi-supplier optimization system uses a sophisticated database architecture with multiple layers:

### Database Structure
```mermaid
graph TB
    subgraph "Source Data Layer"
        RM[ReservationMain]
        RP[ReservationPrice]
        RHI[ReservationHotelInformation]
        RR[ReservationRoom]
    end
    
    subgraph "Multi-Supplier Processing Layer"
        MSS[MultiSupplierSearchRoom]
        MSR[MultiSupplierReservationRoom]
        MSRM[MultiSupplierRoomMapping]
        MST[MultiSupplierRoomsTemp]
    end
    
    subgraph "Prebook Layer"
        RT[ReservationTable]
        RTS[ReservationTableSupplier]
        RTSL[ReservationTableSupplierlog]
        PBR[PreBookRoom]
    end
    
    subgraph "Action Layer"
        BAT[BookingActionsTaken]
        RL[ReservationLogging]
    end
    
    RM --> MSS
    RM --> MSR
    MSS --> RT
    MSR --> RTS
    RTS --> BAT
    RT --> BAT
```

### Key Database Principles
- **Separation of Concerns**: Different tables for different phases
- **Audit Trail**: Complete logging of all actions
- **Performance**: Optimized indexes for multi-supplier queries
- **Scalability**: Partitioned by RepricerId for large datasets

---

## 📊 Core Tables

### 1. ReservationMain - Source of Truth

**Purpose**: Central repository for all imported reservations

```sql
CREATE TABLE [dbo].[ReservationMain] (
    [repricer_reservation_id] INT IDENTITY(1,1) NOT NULL,
    [ReservationId] INT NOT NULL,
    [RePricerId] INT NOT NULL,
    [ACComdationId] INT NULL,
    [checkIn] DATETIME NULL,
    [Checkout] DATETIME NULL,
    [leaderNationality] INT NULL,
    [adultCount] INT NULL,
    [childrenAges] NVARCHAR(MAX) NULL,
    [language] NVARCHAR(10) NULL,
    [sellingChannel] NVARCHAR(50) NULL,
    [cancellation_date] DATETIME NULL,
    [CreatedDate] DATETIME DEFAULT(getutcdate()) NULL,
    [ReservationStatus] NVARCHAR(50) NULL,
    [supplierName] NVARCHAR(255) NULL,
    [Destinations] NVARCHAR(255) NULL,
    [PaymentType] NVARCHAR(50) NULL,
    [ConnectionStatus] NVARCHAR(50) NULL,
    [CriteriaJson] NVARCHAR(MAX) NULL,
    [cpJSON] NVARCHAR(MAX) NULL,
    [BookingDate] DATETIME NULL,
    [Modifieddate] DATETIME NULL
);
```

**Key Indexes**:
- `IX_ReservationMain_RID_RS_ACCID_CHD_CHO` - Multi-supplier lookup
- `IX_ReservationMain_Search_LookUP` - Search optimization
- `IX_ReservationMain_RePricerId_checkIn_cancellation_date_LookUp` - Date filtering

**Business Rules**:
- `ReservationStatus = 'OK'` for eligible reservations
- `supplierName` identifies original booking supplier (excluded from multi-supplier search)
- `cancellation_date` must be future for optimization eligibility

### 2. ReservationPrice - Financial Data

**Purpose**: Stores pricing information for profit calculations

```sql
CREATE TABLE [dbo].[ReservationPrice] (
    [RePricerId] INT NOT NULL,
    [ReservationId] INT NOT NULL,
    [currency] NVARCHAR(10) NULL,
    [total_selling] DECIMAL(18,5) NULL,
    [issue_net] DECIMAL(18,5) NULL,
    [CreatedDate] DATETIME DEFAULT(getutcdate()) NULL
);
```

**Critical for Multi-Supplier**:
- `issue_net` is the baseline price for profit calculations
- `currency` determines exchange rate conversions
- Used in threshold validation

### 3. ReservationRoom - Room Details

**Purpose**: Room-level information for Giata mapping

```sql
CREATE TABLE [dbo].[ReservationRoom] (
    [RePricerId] INT NOT NULL,
    [ReservationId] INT NOT NULL,
    [roominfo] NVARCHAR(MAX) NULL,
    [roomname] NVARCHAR(255) NULL,
    [roomid] INT NULL,
    [roomtype] NVARCHAR(255) NULL,
    [roomboard] NVARCHAR(255) NULL,
    [passengerCount] INT NULL,
    [ChildAges] NVARCHAR(MAX) NULL
);
```

**Multi-Supplier Usage**:
- `roomname` and `roomboard` used for Giata mapping
- `passengerCount` and `ChildAges` for occupancy matching
- Foundation for cross-supplier room equivalency

---

## 🔄 Multi-Supplier Specific Tables

### 1. MultiSupplierSearchRoom - Search Results

**Purpose**: Stores search results from alternative suppliers

```sql
CREATE TABLE [dbo].[MultiSupplierSearchRoom] (
    [MultiSupplierSearchId] INT IDENTITY(1,1) NOT NULL,
    [RepricerId] INT NULL,
    [ReservationId] INT NULL,
    [RoomName] VARCHAR(255) NULL,
    [RoomBoard] VARCHAR(255) NULL,
    [RoomInfo] VARCHAR(MAX) NULL,
    [RoomCode] VARCHAR(100) NULL,
    [Descriptions] VARCHAR(MAX) NULL,
    [FacilitiesDescription] VARCHAR(MAX) NULL,
    [RoomStatus] VARCHAR(50) NULL,
    [RoomBoardBasis] VARCHAR(255) NULL,
    [IssueNetPrice] DECIMAL(18,4) NULL,
    [IssueCurrency] VARCHAR(10) NULL,
    [CancellationDate] DATETIME NULL,
    [CancellationCurrency] VARCHAR(10) NULL,
    [CancellationCharge] DECIMAL(18,2) NULL,
    [RoomRateTags] VARCHAR(MAX) NULL,
    [SupplierName] VARCHAR(255) NULL,
    [CreatedDate] DATETIME DEFAULT(getdate()) NULL
);
```

**Data Flow**: Populated during Phase 5 (Offer Filtering) of multi-supplier flow

### 2. MultiSupplierReservationRoom - Qualified Options

**Purpose**: Stores rooms that passed initial filtering

```sql
CREATE TABLE [dbo].[MultiSupplierReservationRoom] (
    [MultiSupplierReservationId] INT IDENTITY(1,1) NOT NULL,
    [RepricerId] INT NULL,
    [ReservationId] INT NULL,
    [RoomName] VARCHAR(255) NULL,
    [RoomBoard] VARCHAR(255) NULL,
    [RoomInfo] VARCHAR(MAX) NULL,
    [RoomCode] VARCHAR(100) NULL,
    [Descriptions] VARCHAR(MAX) NULL,
    [FacilitiesDescription] VARCHAR(MAX) NULL,
    [RoomStatus] VARCHAR(50) NULL,
    [RoomBoardBasis] VARCHAR(255) NULL,
    [IssueNetPrice] DECIMAL(18,4) NULL,
    [IssueCurrency] VARCHAR(10) NULL,
    [CancellationDate] DATETIME NULL,
    [CancellationCurrency] VARCHAR(10) NULL,
    [CancellationCharge] DECIMAL(18,2) NULL,
    [RoomRateTags] VARCHAR(MAX) NULL,
    [SupplierName] VARCHAR(255) NULL,
    [CreatedDate] DATETIME DEFAULT(getdate()) NULL
);
```

**Business Logic**: Only rooms with successful Giata mapping and price validation

### 3. ReservationTableSupplier - Multi-Supplier Prebooks

**Purpose**: Stores successful prebook attempts for multi-supplier optimization

```sql
CREATE TABLE [dbo].[ReservationTableSupplier] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [ReservationId] INT NULL,
    [RePricerId] INT NULL,
    [createdate] DATETIME NULL,
    [ReservationAdultCount] INT NULL,
    [PreBookAdultCount] INT NULL,
    [ReservationChildAges] NVARCHAR(MAX) NULL,
    [PreBookChildAges] NVARCHAR(MAX) NULL,
    [Providers] NVARCHAR(MAX) NULL,
    [PrebookProviders] NVARCHAR(200) NULL,
    [IsMailSent] BIT DEFAULT(0) NULL,
    [BookingDate] DATETIME NULL,
    [ReservationPrice] DECIMAL(18,5) NULL,
    [PreBookPrice] DECIMAL(18,5) NULL,
    [preBookCount] INT NULL,
    [ProfitAfterCancellation] DECIMAL(18,5) NULL,
    [Profit] DECIMAL(18,5) NULL,
    [CurrencyFactortoEur] DECIMAL(18,5) NULL,
    [Reservationroomname] NVARCHAR(MAX) NULL,
    [PreBookRoomName] NVARCHAR(MAX) NULL,
    [ReservationRoomBoard] NVARCHAR(MAX) NULL,
    [PreBookRoomBoard] NVARCHAR(MAX) NULL,
    [ReservationRoomInfo] NVARCHAR(MAX) NULL,
    [PrebookRoomInfo] NVARCHAR(MAX) NULL,
    [PreBookRoomIndex] NVARCHAR(MAX) NULL,
    [MatchedReservationCancellationDate] DATETIME NULL,
    [MatchedPreBookCancellationDate] DATETIME NULL,
    [MatchedReservationCancellationChargeByPolicy] NVARCHAR(MAX) NULL,
    [MatchedPreBookCancellationChargeByPolicy] NVARCHAR(MAX) NULL,
    [IsCancellationPolicyMatched] BIT NULL,
    [cPStatus] NVARCHAR(MAX) NULL,
    [cpdaysgain] INT NULL,
    [matchedcancellationpolicygain] DECIMAL(10,3) NULL,
    [token] NVARCHAR(MAX) NULL,
    [AvailabilityToken] NVARCHAR(MAX) NULL,
    [emailbody] NVARCHAR(MAX) NULL,
    [reservationemailbody] NVARCHAR(MAX) NULL,
    [prebookemailbody] NVARCHAR(MAX) NULL,
    [PreBookResponseJson] NVARCHAR(MAX) NULL,
    [SearchJson] NVARCHAR(MAX) NULL,
    [ExtraData] VARCHAR(MAX) NULL,
    [HMNotes] VARCHAR(MAX) NULL,
    [ActionId] INT NULL,
    [NewBookingPrice] DECIMAL(18,5) NULL,
    [ClientConfig_DaysDifferenceInPreBookCreation] INT NULL,
    [JobToken] VARCHAR(MAX) NULL
);
```

**Key Differences from ReservationTable**:
- `PrebookProviders` field for multi-supplier tracking
- Enhanced cancellation policy fields
- Separate from same-supplier prebooks

### 4. BookingActionsTaken - Optimization History

**Purpose**: Records all optimization attempts and results

```sql
CREATE TABLE [dbo].[BookingActionsTaken] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [RepricerId] INT NULL,
    [ReservationId] INT NULL,
    [ExtraData] NVARCHAR(MAX) NULL,
    [HMNotes] NVARCHAR(MAX) NULL,
    [NewBookingId] INT NULL,
    [ActionId] INT NULL,
    [NewBookingPrice] DECIMAL(18,5) NULL,
    [createdById] NVARCHAR(255) NULL,
    [createdByName] NVARCHAR(255) NULL,
    [createdOn] DATETIME NULL,
    [token] VARCHAR(MAX) NULL,
    [GainConvertedCurrency] VARCHAR(3) NULL,
    [CurrencyFactor] DECIMAL(10,6) NULL,
    [GainAmountInOriginalCurrency] DECIMAL(18,5) NULL,
    [OriginalCurrency] VARCHAR(3) NULL,
    [ResponseBody] VARCHAR(MAX) NULL,
    [CustomerAmount] DECIMAL(18,2) NULL,
    [CustomerCurrency] VARCHAR(10) NULL,
    [CustomerCurrencyFactor] DECIMAL(10,6) NULL,
    [isUseCustomerCurrencyFactor] BIT DEFAULT(0) NULL
);
```

**ActionId Values**:
- `1` = Successfully Optimized
- `2` = Dry Run Success (Awaiting Approval)
- `3` = Failed Optimization
- `4` = Manual Rejection

**Multi-Supplier Tracking**: `ExtraData` contains supplier change information

---

## 🔧 Stored Procedures

### 1. usp_get_PreBookCriteria - Data Loading

**Purpose**: Loads reservations eligible for multi-supplier optimization

```sql
CREATE PROCEDURE usp_get_PreBookCriteria
    @ReservationId INT = NULL,
    @RepricerId INT,
    @isMultiSupplier INT = 0
AS
BEGIN
    -- CTE for room counting
    WITH cte AS (
        SELECT reservationid, RepricerId, COUNT(roomid) as count_room_id
        FROM ReservationRoom
        WHERE RepricerId = @RepricerId
        GROUP BY reservationid, RepricerId
    )

    SELECT DISTINCT
        rp.RepricerId,
        rp.ReservationId,
        rp.currency,
        rp.total_selling,
        rp.issue_net,
        rhi.hotelid,
        rhi.hotelname,
        rr.roominfo,
        rr.roomname,
        rr.roomid,
        rr.roomtype,
        rr.roomboard,
        rr.passengerCount,
        rr.ChildAges,
        rm.Destinations,
        ct.count_room_id as room_count
    FROM ReservationPrice rp
        JOIN ReservationHotelInformation rhi
            ON rp.reservationid = rhi.reservationid
               AND rp.RepricerId = rhi.RepricerId
        JOIN ReservationRoom rr
            ON rp.reservationid = rr.reservationid
               AND rp.RepricerId = rr.RepricerId
        INNER JOIN ReservationMain rm
            ON rm.reservationid = rr.reservationid
               AND rm.RepricerId = rr.RepricerId
        INNER JOIN cte ct
            ON ct.reservationid = rr.reservationid
               AND ct.RepricerId = rr.RepricerId
    WHERE rm.ReservationStatus = 'OK'
        AND rm.checkIn > GETUTCDATE()
        AND rm.cancellation_date > GETUTCDATE()
        AND (@ReservationId IS NULL OR rp.ReservationId = @ReservationId)
END
```

**Multi-Supplier Filtering**:
- Only active reservations (`ReservationStatus = 'OK'`)
- Future check-in dates
- Valid cancellation dates
- Room count validation

### 2. usp_Ins_Prebook_Supplier_V2 - Prebook Creation

**Purpose**: Creates prebook records for multi-supplier optimization

```sql
CREATE PROCEDURE usp_Ins_Prebook_Supplier_V2
    @ReservationId INT,
    @RePricerId INT,
    @ReservationAdultCount INT,
    @PreBookAdultCount INT,
    @ReservationChildAges NVARCHAR(MAX),
    @PreBookChildAges NVARCHAR(MAX),
    @Providers NVARCHAR(MAX),
    @PrebookProviders NVARCHAR(200),
    @IsMailSent BIT,
    @BookingDate DATETIME,
    @ReservationPrice DECIMAL(18,5),
    @PreBookPrice DECIMAL(18,5),
    @preBookCount INT,
    @ProfitAfterCancellation DECIMAL(18,5),
    @Profit DECIMAL(18,5),
    -- ... additional parameters
AS
BEGIN
    INSERT INTO [dbo].[ReservationTableSupplier] (
        [ReservationId], [RePricerId], [createdate],
        [ReservationAdultCount], [PreBookAdultCount],
        [ReservationChildAges], [PreBookChildAges],
        [Providers], [PrebookProviders],
        [IsMailSent], [BookingDate],
        [ReservationPrice], [PreBookPrice],
        [preBookCount], [ProfitAfterCancellation], [Profit],
        -- ... all fields
    )
    VALUES (
        @ReservationId, @RePricerId, GETUTCDATE(),
        @ReservationAdultCount, @PreBookAdultCount,
        @ReservationChildAges, @PreBookChildAges,
        @Providers, @PrebookProviders,
        @IsMailSent, @BookingDate,
        @ReservationPrice, @PreBookPrice,
        @preBookCount, @ProfitAfterCancellation, @Profit
        -- ... all values
    );
END
```

**Key Features**:
- Separate table for multi-supplier prebooks
- `PrebookProviders` tracks new supplier
- Complete audit trail with timestamps

### 3. usp_ins_MultiSupplierSearchRoom - Search Results

**Purpose**: Stores search results from alternative suppliers

```sql
CREATE PROCEDURE usp_ins_MultiSupplierSearchRoom
    @RepricerId INT,
    @ReservationId INT,
    @RoomName VARCHAR(255),
    @RoomBoard VARCHAR(255),
    @RoomInfo VARCHAR(MAX),
    @RoomCode VARCHAR(100),
    @Descriptions VARCHAR(MAX),
    @FacilitiesDescription VARCHAR(MAX),
    @RoomStatus VARCHAR(50),
    @RoomBoardBasis VARCHAR(255),
    @IssueNetPrice DECIMAL(18,2),
    @IssueCurrency VARCHAR(10),
    @CancellationDate DATE,
    @CancellationCurrency VARCHAR(10),
    @CancellationCharge DECIMAL(18,2),
    @RoomRateTags VARCHAR(MAX),
    @SupplierName VARCHAR(255),
    @ImageList ImageListType READONLY
AS
BEGIN
    BEGIN TRANSACTION;

    DECLARE @NewMultiSupplierRoomId INT;

    INSERT INTO [dbo].[MultiSupplierSearchRoom] (
        [RepricerId], [ReservationId], [RoomName], [RoomBoard],
        [RoomInfo], [RoomCode], [Descriptions], [FacilitiesDescription],
        [RoomStatus], [RoomBoardBasis], [IssueNetPrice], [IssueCurrency],
        [CancellationDate], [CancellationCurrency], [CancellationCharge],
        [RoomRateTags], [SupplierName], [CreatedDate]
    )
    VALUES (
        @RepricerId, @ReservationId, @RoomName, @RoomBoard,
        @RoomInfo, @RoomCode, @Descriptions, @FacilitiesDescription,
        @RoomStatus, @RoomBoardBasis, @IssueNetPrice, @IssueCurrency,
        @CancellationDate, @CancellationCurrency, @CancellationCharge,
        @RoomRateTags, @SupplierName, GETDATE()
    );

    SET @NewMultiSupplierRoomId = SCOPE_IDENTITY();

    -- Handle image list insertion
    IF EXISTS(SELECT 1 FROM @ImageList)
    BEGIN
        INSERT INTO [dbo].[MultiSupplierSearchRoomImages] (
            [MultiSupplierSearchId], [ImageUrl], [ImageDescription]
        )
        SELECT @NewMultiSupplierRoomId, [ImageUrl], [ImageDescription]
        FROM @ImageList;
    END

    COMMIT TRANSACTION;
END
```

**Transaction Safety**: Ensures data consistency during bulk inserts

### 4. usp_get_MultiSupplierRoomDetailsByRepricerId - Reporting

**Purpose**: Retrieves multi-supplier optimization results for dashboard

```sql
CREATE PROCEDURE usp_get_MultiSupplierRoomDetailsByRepricerId
    @RepricerId INT,
    @ReservationId INT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 100
AS
BEGIN
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    WITH DistinctReservations AS (
        SELECT DISTINCT mss.ReservationId
        FROM MultiSupplierSearchRoom mss
            INNER JOIN MultiSupplierReservationRoom msr
                ON mss.RepricerId = msr.RepricerId
                   AND mss.ReservationId = msr.ReservationId
            INNER JOIN ReservationPrice rp
                ON rp.RepricerId = mss.RepricerId
                   AND rp.ReservationId = mss.ReservationId
            LEFT JOIN dbo.BookingActionsTaken AS realized
                ON realized.RepricerId = rp.RepricerId
                   AND realized.ReservationId = rp.ReservationId
                   AND realized.ActionId = 1
        WHERE (@ReservationId IS NULL OR mss.ReservationId = @ReservationId)
            AND mss.RepricerId = @RepricerId
            AND realized.id IS NULL  -- Exclude already optimized
    )

    SELECT
        mss.MultiSupplierSearchId,
        mss.RepricerId,
        mss.ReservationId,
        mss.RoomName,
        mss.RoomBoard,
        mss.IssueNetPrice,
        mss.SupplierName,
        msr.SupplierName AS ReservationSupplier,
        rp.issue_net AS OriginalPrice,
        (rp.issue_net - mss.IssueNetPrice) AS PotentialSavings,
        rm.checkIn,
        rm.checkout,
        rhi.hotelname
    FROM MultiSupplierSearchRoom mss
        INNER JOIN DistinctReservations dr ON mss.ReservationId = dr.ReservationId
        INNER JOIN MultiSupplierReservationRoom msr ON mss.ReservationId = msr.ReservationId
        INNER JOIN ReservationPrice rp ON mss.ReservationId = rp.ReservationId
        INNER JOIN ReservationMain rm ON mss.ReservationId = rm.ReservationId
        INNER JOIN ReservationHotelInformation rhi ON mss.ReservationId = rhi.ReservationId
    WHERE mss.RepricerId = @RepricerId
        AND rm.ReservationStatus = 'OK'
        AND CAST(rm.checkIn AS DATE) > CAST(GETUTCDATE() AS DATE)
    ORDER BY PotentialSavings DESC
    OFFSET (@PageNumber - 1) * @PageSize ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
```

**Business Logic**:
- Excludes already optimized reservations (`ActionId = 1`)
- Calculates potential savings
- Paginates results for performance
- Orders by highest savings first

---

## 📊 Data Flow Analysis

### Phase-by-Phase Data Movement

```mermaid
sequenceDiagram
    participant App as Application
    participant RM as ReservationMain
    participant MSS as MultiSupplierSearchRoom
    participant MSR as MultiSupplierReservationRoom
    participant RTS as ReservationTableSupplier
    participant BAT as BookingActionsTaken

    Note over App,BAT: Phase 1-2: Initialization & Data Loading
    App->>RM: GetReservationsAsync()
    RM-->>App: Eligible reservations

    Note over App,BAT: Phase 3-5: Supplier Search & Filtering
    App->>MSS: usp_ins_MultiSupplierSearchRoom()
    MSS-->>App: Search results stored

    Note over App,BAT: Phase 6-7: Room Mapping & Validation
    App->>MSR: usp_ins_MultiSupplierReservationRoom()
    MSR-->>App: Qualified options stored

    Note over App,BAT: Phase 8-9: Prebook Creation
    App->>RTS: usp_Ins_Prebook_Supplier_V2()
    RTS-->>App: Prebook created

    Note over App,BAT: Phase 10: Final Optimization
    App->>BAT: usp_upd_BookingActionsTaken()
    BAT-->>App: Action recorded
```

### Data Lifecycle States

| State | Table | Description | Next Action |
|-------|-------|-------------|-------------|
| **Imported** | ReservationMain | Original booking data | Eligibility check |
| **Searched** | MultiSupplierSearchRoom | Alternative options found | Room mapping |
| **Mapped** | MultiSupplierReservationRoom | Giata mapping successful | Price validation |
| **Prebooked** | ReservationTableSupplier | Tentative booking created | Dry run |
| **Approved** | BookingActionsTaken | Manual approval received | Final optimization |
| **Optimized** | BookingActionsTaken | Successfully reboked | Revenue calculation |

### Critical Data Relationships

```mermaid
erDiagram
    ReservationMain ||--o{ ReservationPrice : "has pricing"
    ReservationMain ||--o{ ReservationRoom : "contains rooms"
    ReservationMain ||--o{ MultiSupplierSearchRoom : "generates searches"
    MultiSupplierSearchRoom ||--o{ MultiSupplierReservationRoom : "qualifies to"
    MultiSupplierReservationRoom ||--o{ ReservationTableSupplier : "creates prebooks"
    ReservationTableSupplier ||--o{ BookingActionsTaken : "results in actions"

    ReservationMain {
        int ReservationId PK
        int RepricerId PK
        string ReservationStatus
        datetime checkIn
        datetime cancellation_date
        string supplierName
    }

    MultiSupplierSearchRoom {
        int MultiSupplierSearchId PK
        int ReservationId FK
        int RepricerId FK
        string SupplierName
        decimal IssueNetPrice
        datetime CancellationDate
    }

    ReservationTableSupplier {
        int id PK
        int ReservationId FK
        int RepricerId FK
        string PrebookProviders
        decimal Profit
        string cPStatus
        string AvailabilityToken
    }

    BookingActionsTaken {
        int id PK
        int ReservationId FK
        int RepricerId FK
        int ActionId
        decimal NewBookingPrice
        datetime createdOn
    }
```

---

## ⚡ Performance Considerations

### Index Strategy

**Primary Indexes**:
```sql
-- ReservationMain - Multi-supplier lookup
CREATE NONCLUSTERED INDEX [IX_ReservationMain_Search_LookUP]
    ON [dbo].[ReservationMain]([RePricerId] ASC, [ReservationStatus] ASC,
                               [ACComdationId] ASC, [checkIn] ASC,
                               [ConnectionStatus] ASC, [PaymentType] ASC)
    INCLUDE([ReservationId], [Checkout], [leaderNationality], [adultCount],
            [childrenAges], [language], [sellingChannel], [cancellation_date],
            [CreatedDate], [CriteriaJson], [BookingDate], [Modifieddate], [cpJSON]);

-- BookingActionsTaken - Optimization tracking
CREATE CLUSTERED INDEX [idx_BookingActionsTaken_RepricerIdReservationIdActionId]
    ON [dbo].[BookingActionsTaken]([RepricerId] ASC, [ReservationId] ASC, [ActionId] ASC);
```

**Query Optimization**:
- Partition by `RepricerId` for large datasets
- Use `READ UNCOMMITTED` for reporting queries
- Implement pagination for large result sets
- Cache frequently accessed configuration data

### Performance Metrics

| Operation | Target Time | Optimization Strategy |
|-----------|-------------|----------------------|
| Data Loading | <30 seconds | Parallel queries, caching |
| Search Results | <2 minutes | Batch processing, indexes |
| Room Mapping | <1 minute | Giata API optimization |
| Prebook Creation | <30 seconds | Bulk inserts, transactions |
| Final Optimization | <1 minute | Async processing |

### Monitoring Queries

```sql
-- Check multi-supplier processing volume
SELECT
    RepricerId,
    COUNT(*) AS TotalSearches,
    COUNT(DISTINCT ReservationId) AS UniqueReservations,
    AVG(IssueNetPrice) AS AvgPrice,
    COUNT(DISTINCT SupplierName) AS SuppliersUsed
FROM MultiSupplierSearchRoom
WHERE CreatedDate >= DATEADD(day, -7, GETUTCDATE())
GROUP BY RepricerId
ORDER BY TotalSearches DESC;

-- Monitor optimization success rates
SELECT
    RepricerId,
    COUNT(CASE WHEN ActionId = 1 THEN 1 END) AS Successful,
    COUNT(CASE WHEN ActionId = 3 THEN 1 END) AS Failed,
    COUNT(*) AS Total,
    CAST(COUNT(CASE WHEN ActionId = 1 THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) AS SuccessRate
FROM BookingActionsTaken
WHERE createdOn >= DATEADD(day, -30, GETUTCDATE())
GROUP BY RepricerId
ORDER BY SuccessRate DESC;
```

---

## 🔄 Data Lifecycle

### 1. Reservation Import Phase

**Tables Involved**: `ReservationMain`, `ReservationPrice`, `ReservationRoom`, `ReservationHotelInformation`

**Process**:
1. Client systems push booking data
2. Data validation and cleansing
3. Duplicate detection and handling
4. Status assignment (`ReservationStatus = 'OK'`)

**Key Validations**:
```sql
-- Eligibility check for multi-supplier optimization
SELECT rm.ReservationId
FROM ReservationMain rm
    INNER JOIN ReservationPrice rp ON rm.ReservationId = rp.ReservationId
WHERE rm.ReservationStatus = 'OK'
    AND rm.checkIn > GETUTCDATE()
    AND rm.cancellation_date > GETUTCDATE()
    AND rm.supplierName IS NOT NULL
    AND rp.issue_net > 0;
```

### 2. Multi-Supplier Search Phase

**Tables Involved**: `MultiSupplierSearchRoom`

**Process**:
1. Exclude original supplier from search
2. Search alternative suppliers in batches of 5
3. Store all search results for analysis
4. Apply initial price and availability filters

**Data Retention**: 30 days for analysis, then archived

### 3. Room Mapping & Validation Phase

**Tables Involved**: `MultiSupplierReservationRoom`, `MultiSupplierRoomMapping`

**Process**:
1. Giata API calls for room equivalency
2. Board basis matching
3. Occupancy validation
4. Store only qualified options

**Quality Metrics**:
- Room mapping success rate: >90%
- Board basis match rate: >85%
- Occupancy accuracy: 100%

### 4. Prebook Creation Phase

**Tables Involved**: `ReservationTableSupplier`, `ReservationTableSupplierlog`

**Process**:
1. Create tentative bookings via IRIX API
2. Validate actual pricing
3. Cancellation policy comparison
4. Store prebook details and tokens

**Business Rules**:
```sql
-- Prebook validation criteria
INSERT INTO ReservationTableSupplier (...)
SELECT ...
WHERE Profit >= @MinimumProfit
    AND cPStatus IN ('loose', 'tightWithBuffer', 'CancellationChargesApplicable')
    AND PreBookPrice > 0
    AND AvailabilityToken IS NOT NULL;
```

### 5. Approval & Optimization Phase

**Tables Involved**: `BookingActionsTaken`

**Process**:
1. Dry run execution and validation
2. Email notification to operations team
3. Manual review and approval
4. Final optimization execution
5. Customer notification

**Action Tracking**:
```sql
-- Track optimization outcomes
INSERT INTO BookingActionsTaken (
    RepricerId, ReservationId, ActionId,
    NewBookingPrice, GainAmountInOriginalCurrency,
    createdById, createdByName, createdOn
)
VALUES (
    @RepricerId, @ReservationId, 1, -- ActionId 1 = Successful
    @NewBookingPrice, @ActualSavings,
    @UserId, @UserName, GETUTCDATE()
);
```

---

## 📈 Business Intelligence & Reporting

### Key Performance Indicators

**Revenue Metrics**:
```sql
-- Monthly revenue from multi-supplier optimization
SELECT
    YEAR(createdOn) AS Year,
    MONTH(createdOn) AS Month,
    COUNT(*) AS OptimizationsCount,
    SUM(GainAmountInOriginalCurrency) AS TotalSavings,
    SUM(GainAmountInOriginalCurrency * 0.5) AS PlatformRevenue,
    AVG(GainAmountInOriginalCurrency) AS AvgSavingsPerOptimization
FROM BookingActionsTaken
WHERE ActionId = 1  -- Successful optimizations
    AND createdOn >= DATEADD(month, -12, GETUTCDATE())
GROUP BY YEAR(createdOn), MONTH(createdOn)
ORDER BY Year DESC, Month DESC;
```

**Operational Metrics**:
```sql
-- Multi-supplier vs same-supplier performance
SELECT
    CASE
        WHEN ExtraData LIKE '%MultiSupplier%' THEN 'Multi-Supplier'
        ELSE 'Same-Supplier'
    END AS OptimizationType,
    COUNT(*) AS TotalAttempts,
    COUNT(CASE WHEN ActionId = 1 THEN 1 END) AS Successful,
    AVG(GainAmountInOriginalCurrency) AS AvgSavings,
    AVG(DATEDIFF(hour, createdOn, GETUTCDATE())) AS AvgProcessingHours
FROM BookingActionsTaken
WHERE createdOn >= DATEADD(day, -30, GETUTCDATE())
GROUP BY CASE
    WHEN ExtraData LIKE '%MultiSupplier%' THEN 'Multi-Supplier'
    ELSE 'Same-Supplier'
END;
```

### Data Quality Monitoring

**Daily Health Checks**:
```sql
-- Check for data inconsistencies
SELECT
    'Missing Prebook Data' AS Issue,
    COUNT(*) AS Count
FROM MultiSupplierReservationRoom msr
    LEFT JOIN ReservationTableSupplier rts
        ON msr.ReservationId = rts.ReservationId
        AND msr.RepricerId = rts.RePricerId
WHERE rts.id IS NULL
    AND msr.CreatedDate >= DATEADD(day, -1, GETUTCDATE())

UNION ALL

SELECT
    'Orphaned Search Results' AS Issue,
    COUNT(*) AS Count
FROM MultiSupplierSearchRoom mss
    LEFT JOIN ReservationMain rm
        ON mss.ReservationId = rm.ReservationId
        AND mss.RepricerId = rm.RePricerId
WHERE rm.ReservationId IS NULL
    AND mss.CreatedDate >= DATEADD(day, -1, GETUTCDATE());
```

---

## 🛠️ Maintenance & Operations

### Regular Maintenance Tasks

**Daily**:
- Archive old search results (>30 days)
- Update index statistics
- Monitor disk space usage
- Check for failed transactions

**Weekly**:
- Analyze query performance
- Review error logs
- Update cached configuration data
- Generate performance reports

**Monthly**:
- Archive completed optimizations (>90 days)
- Review and optimize indexes
- Update database statistics
- Capacity planning review

### Backup & Recovery Strategy

**Backup Schedule**:
- **Full Backup**: Daily at 2 AM UTC
- **Differential Backup**: Every 6 hours
- **Transaction Log Backup**: Every 15 minutes
- **Retention**: 30 days online, 1 year archived

**Recovery Scenarios**:
1. **Point-in-time Recovery**: For data corruption
2. **Table-level Recovery**: For specific table issues
3. **Cross-region Failover**: For disaster recovery

### Troubleshooting Common Issues

**Performance Issues**:
```sql
-- Identify slow queries
SELECT
    qt.query_sql_text,
    qs.execution_count,
    qs.total_elapsed_time / qs.execution_count AS avg_elapsed_time,
    qs.total_logical_reads / qs.execution_count AS avg_logical_reads
FROM sys.dm_exec_query_stats qs
    CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) qt
WHERE qt.query_sql_text LIKE '%MultiSupplier%'
ORDER BY avg_elapsed_time DESC;
```

**Data Integrity Checks**:
```sql
-- Verify referential integrity
SELECT
    'ReservationTableSupplier orphans' AS Issue,
    COUNT(*) AS Count
FROM ReservationTableSupplier rts
    LEFT JOIN ReservationMain rm
        ON rts.ReservationId = rm.ReservationId
        AND rts.RePricerId = rm.RePricerId
WHERE rm.ReservationId IS NULL;
```

---

*This database documentation provides comprehensive coverage of all database objects, relationships, and operations involved in the multi-supplier optimization flow. Use this as a reference for development, troubleshooting, and system maintenance.*

**Document Version**: 1.0
**Last Updated**: December 2024
**Next Review**: March 2025
```
