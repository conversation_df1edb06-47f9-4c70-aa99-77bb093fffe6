﻿using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Persistence.Data;
using Logger.Contract;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Caching.Memory;
using Repricer.Cache;
using RePricer.Util;
using System.Data;
using System.Diagnostics;
using TimeZoneConverter;
using Constant = RePricer.Constants.PersistanceConstant;

namespace Irix.Persistence
{
    public class ClientPersistance : PersistanceBase, IClientPersistance
    {
        private readonly ILogger _log;

        private readonly IMemoryCache _memoryCache;
        private readonly string _className = nameof(ClientPersistance);

        public ClientPersistance(IMemoryCache memoryCache, ILogger log)
        {
            _memoryCache = memoryCache;
            _log = log;
        }

        public int InsertResellerDetailDataAsync(
                           string RePricerJsonData,
                           string hotelIds = null,
                           string cityIds = null,
                           string countryIds = null,
                           string resellers = null,
                           string suppliers = null)
        {
            var watch = Stopwatch.StartNew();
            int repricerUserID = 0;
            int rowsAffected = 0;

            try
            {
                using SqlConnection connection = new SqlConnection(_RePricerconnectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertResellerData, connection);
                command.CommandType = CommandType.StoredProcedure;

                // Add parameters to stored procedure
                command.Parameters.AddWithValue(Constant.RePricerJsonData, RePricerJsonData);
                command.Parameters.AddWithValue(Constant.HotelIds, hotelIds);
                command.Parameters.AddWithValue(Constant.CityIds, cityIds);
                command.Parameters.AddWithValue(Constant.CountryIds, countryIds);
                command.Parameters.AddWithValue(Constant.Resellers, resellers);
                command.Parameters.AddWithValue(Constant.Suppliers, suppliers);

                SqlParameter outputParam = new SqlParameter
                {
                    ParameterName = Constant.RePricerId,
                    SqlDbType = SqlDbType.Int,
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                rowsAffected = command.ExecuteNonQuery();
                repricerUserID = (int)outputParam.Value;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.InsertResellerDetailDataAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"ClientPersistance|InsertReservationDataAsync|{elapsedTimeInSeconds}|{RePricerJsonData} in {watch.Elapsed}");

            return repricerUserID;
        }

        public int DeleteClientDataAsync(int RePricerUserId)
        {
            var watch = Stopwatch.StartNew();

            int rowsAffected = 0;

            try
            {
                using SqlConnection connection = new SqlConnection(_RePricerconnectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.DeleteRePricerDetail, connection);
                command.CommandType = CommandType.StoredProcedure;

                // Add parameters
                command.Parameters.AddWithValue(Constant.RePricerUserId, RePricerUserId);

                rowsAffected = command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.DeleteClientDataAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"ClientPersistance|DeleteClientDataAsync|{elapsedTimeInSeconds}|{RePricerUserId} in {watch.Elapsed}");

            return rowsAffected;
        }

        public async Task<RepricerResponse> LoadRepricerResponse(string UserName)
        {
            var watch = Stopwatch.StartNew();

            var repricerdata = new RepricerResponse();
            var key = $"LoadRepricerResponse_{UserName}";

            if (_memoryCache.TryGetValue(key, out RepricerResponse cachedMemoryRepricerDetail))
            {
                return cachedMemoryRepricerDetail;
            }

            var cachedRepricerauthdetail = RedisCacheHelper.Get<RepricerResponse>($"LoadRepricerResponse_{UserName}");
            if (cachedRepricerauthdetail != null && !string.IsNullOrEmpty(cachedRepricerauthdetail.UserName))
            {
                _memoryCache.Set(key, cachedRepricerauthdetail, TimeSpan.FromHours(4));

                return cachedRepricerauthdetail;
            }
            repricerdata = new RepricerResponse();

            try
            {
                using (SqlConnection connection = new SqlConnection(_RePricerconnectionString))
                {
                    connection.Open();
                    SqlParameter[] parameters =
                          {
                              new SqlParameter(Constant.UserName, SqlDbType.VarChar) { Value = UserName }
                              };

                    using (SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.LoadRepricerResponse, parameters))
                    {
                        var clientdata = new ClientData();
                        while (reader.Read())
                        {
                            repricerdata = clientdata.ConvertRepricerResponse(reader);
                        }
                    }
                }
                RedisCacheHelper.Set(key, repricerdata, TimeSpan.FromMinutes(50));

                _memoryCache.Set(key, repricerdata, TimeSpan.FromMinutes(60));
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.LoadRePricerDetail,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;

            return repricerdata;
        }

        public async Task<RePricerDetail> LoadRePricerDetail(int RepricerId, string steptoken = null, bool isRefreshCache = false)
        {
            var repricerdata = new RePricerDetail
            {
                RepricerUserID = RepricerId
            };

            var key = $"Repricer_{RepricerId}_LoadRePricerDetail";

            if (_memoryCache.TryGetValue(key, out RePricerDetail cachedMemoryRepricerDetail) && !isRefreshCache)
            {
                return cachedMemoryRepricerDetail;
            }

            var cachedRedisRepricerDetail = RedisCacheHelper.Get<RePricerDetail>(key);
            if (cachedRedisRepricerDetail != null && !isRefreshCache)
            {
                _memoryCache.Set(key, cachedRedisRepricerDetail, TimeSpan.FromMinutes(180));
                return cachedRedisRepricerDetail;
            }

            try
            {
                using (SqlConnection connection = new SqlConnection(_RePricerconnectionString))
                {
                    await connection.OpenAsync();

                    SqlParameter[] parameters =
                    {
                        new SqlParameter(Constant.RePricerUserId, SqlDbType.Int) { Value = RepricerId }
                    };

                    using (SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.LoadRePricerDta, parameters))
                    {
                        var clientdata = new ClientData();
                        while (await reader.ReadAsync())
                        {
                            repricerdata = clientdata.ConvertRepricerData(reader);
                        }
                    }
                }

                // Save to both RedisCache and MemoryCache
                try
                {
                    _memoryCache.Set(key, repricerdata, TimeSpan.FromMinutes(180));
                    RedisCacheHelper.Set(key, repricerdata, TimeSpan.FromDays(1));
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ClientPersistance,
                        MethodName = Constant.LoadRePricerDetail,
                    };
                    _log.Error(irixErrorEntity, ex);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.LoadRePricerDetail,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return repricerdata;
        }

        public List<ClientConfigScheduler_Hangfire> LoadRePricerScheduleConfig(bool isRefreshCache = false)
        {
            var watch = Stopwatch.StartNew();
            var clientConfigSchedulers = new List<ClientConfigScheduler_Hangfire>();
            var key = $"LoadRePricerScheduleConfig";

            var memoryCacheTime = TimeSpan.FromHours(4);
            var redisCacheTime = TimeSpan.FromDays(1);

            if (_memoryCache.TryGetValue(key, out List<ClientConfigScheduler_Hangfire> cachedMemoryConfig) && !isRefreshCache)
            {
                return cachedMemoryConfig;
            }

            var cachedRedisConfig = RedisCacheHelper.Get<List<ClientConfigScheduler_Hangfire>>(key);
            if (cachedRedisConfig != null && !isRefreshCache)
            {
                _memoryCache.Set(key, cachedRedisConfig, memoryCacheTime);
                return cachedRedisConfig;
            }

            try
            {
                using SqlConnection connection = RePricerConnection();
                connection.Open();

                using SqlDataReader reader = ExecuteStoredProcedureAsync(connection, Constant.LoadClientSchedulerData).GetAwaiter().GetResult();

                var clientdata = new ClientData();
                while (reader.Read())
                {
                    var clientConfigScheduler = new ClientConfigScheduler_Hangfire
                    {
                        RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                        Reservation_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "Reservation_CronTime"),
                        PreBook_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_CronTime"),
                        CurrencyExchange_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "CurrencyExchange_CronTime"),
                        RefreshCache_CronTime = DbPropertyHelper.StringPropertyFromRow(reader, "RefreshCache_CronTime"),
                        TimeZoneName = DbPropertyHelper.StringPropertyFromRow(reader, "TimeZoneName"),
                        RepricerName = DbPropertyHelper.StringPropertyFromRow(reader, "RepricerName"),
                        IsMultiSupplierEnabled = DbPropertyHelper.BoolPropertyFromRow(reader, "isMultiSupplierRoomSync"),
                        IsJobsEnable = DbPropertyHelper.BoolPropertyFromRow(reader, "IsJobsEnable"),
                    };

                    try
                    {
                        clientConfigScheduler.IsActive = DbPropertyHelper.BoolPropertyFromRow(reader, "isActive");
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(LoadRePricerDetail),
                            RePricerId = clientConfigScheduler?.RepricerId ?? 0,
                            Params = $"RepricerId: ALL"
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                    try
                    {
                        clientConfigScheduler.IsUseResellerCPHourDifference = DbPropertyHelper.BoolPropertyFromRow(reader, "IsUseResellerCPHourDifference");
                        clientConfigScheduler.ResellerCPHourDifference = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ResellerCPHourDifference");
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(LoadRePricerDetail),
                            RePricerId = clientConfigScheduler?.RepricerId ?? 0,
                            Params = $"RepricerId: ALL"
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                    try
                    {
                        var recpricerDetails = LoadRePricerDetail(clientConfigScheduler.RepricerId)?.GetAwaiter().GetResult();
                        if (recpricerDetails != null)
                        {
                            clientConfigScheduler.OptimizationType = recpricerDetails.OptimizationType;
                            try
                            {
                                recpricerDetails.AutoJobServerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "AutoJobServerId");
                                clientConfigScheduler.AutoJobServerId = recpricerDetails.AutoJobServerId;
                            }
                            catch (Exception ex)
                            {
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = _className,
                                    MethodName = nameof(LoadRePricerDetail),
                                    RePricerId = clientConfigScheduler?.RepricerId ?? 0,
                                    Params = SerializeDeSerializeHelper.Serialize(new { clientConfigScheduler?.RepricerId, recpricerDetails?.AutoJobServerId })
                                };
                                _log.Error(irixErrorEntity, ex);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(LoadRePricerDetail),
                            RePricerId = clientConfigScheduler?.RepricerId ?? 0,
                            Params = $"RepricerId: ALL"
                        };
                        _log.Error(irixErrorEntity, ex);
                    }

                    clientConfigSchedulers.Add(clientConfigScheduler);
                }

                if (clientConfigSchedulers.Any())
                {
                    _memoryCache.Set(key, clientConfigSchedulers, memoryCacheTime);
                    RedisCacheHelper.Set(key, clientConfigSchedulers, redisCacheTime);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(LoadRePricerScheduleConfig),
                    RePricerId = 0,
                    Params = $"RepricerId: ALL"
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            return clientConfigSchedulers;
        }

        public async Task<SmtpConfigModel> LoadRePricerEmailConfig(int RepricerId, bool isRefreshCache = false)
        {
            var watch = Stopwatch.StartNew();
            SmtpConfigModel smtpConfigModel = null;
            var memoryCacheTime = TimeSpan.FromMinutes(180);
            var redisCacheTime = TimeSpan.FromDays(1);

            var key = $"LoadRePricerEmailConfig_{RepricerId}_";

            if (_memoryCache.TryGetValue(key, out SmtpConfigModel cachedMemorySmtpConfig) && !isRefreshCache)
            {
                return cachedMemorySmtpConfig;
            }

            var cachedRedisSmtpConfig = RedisCacheHelper.Get<SmtpConfigModel>(key);
            if (cachedRedisSmtpConfig != null && !isRefreshCache)
            {
                _memoryCache.Set(key, cachedRedisSmtpConfig, memoryCacheTime);
                return cachedRedisSmtpConfig;
            }

            try
            {
                using (SqlConnection connection = RePricerConnection())
                {
                    await connection.OpenAsync();

                    SqlParameter[] parameters =
                    {
                        new SqlParameter(Constant.RePricerUserId, SqlDbType.VarChar) { Value = RepricerId }
                    };

                    using (SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.LoadClientEmailData, parameters))
                    {
                        var clientdata = new ClientData();
                        while (await reader.ReadAsync())
                        {
                            smtpConfigModel = new SmtpConfigModel
                            {
                                RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                                SMTPPort = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "SMTPPort"),
                                SMTPServer = DbPropertyHelper.StringPropertyFromRow(reader, "SMTPServer"),
                                SMTPUsername = DbPropertyHelper.StringPropertyFromRow(reader, "SMTPUsername"),
                                SMTPPassword = DbPropertyHelper.StringPropertyFromRow(reader, "SMTPPassword"),
                                SenderEmail = DbPropertyHelper.StringPropertyFromRow(reader, "SenderEmail"),
                                IsBodyHtml = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsBodyHtml"),
                                Host = DbPropertyHelper.StringPropertyFromRow(reader, "Host"),
                                ToEmails = DbPropertyHelper.StringPropertyFromRow(reader, "ToEmails"),
                                PriceDifferenceValue = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "priceDifferenceValue"),
                                PriceDifferencePercentage = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "priceDifferencePercentage"),
                                IsUsePercentage = DbPropertyHelper.BoolPropertyFromRow(reader, "isUsePercentage"),
                                traveldaysmaxsearchindays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "traveldaysmaxsearchindays"),
                                traveldaysminsearchindays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "traveldaysminsearchindays"),
                                pricedifferencecurrency = DbPropertyHelper.StringPropertyFromRow(reader, "pricedifferencecurrency"),
                                MaxNumberOfTimesOptimization = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MaxNumberOfTimesOptimization"),
                                ClientConfig_DaysDifferenceInPreBookCreation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ClientConfig_DaysDifferenceInPreBookCreation"),
                                IsCreatePrebookFoPriceEdgeCase = DbPropertyHelper.BoolPropertyFromRow(reader, "IsCreatePrebookFoPriceEdgeCase"),
                                IsSearchSyncDataSave = DbPropertyHelper.BoolPropertyFromRow(reader, "IsSearchSyncDataSave"),
                                IsDifferentSupplierSearchSyncDataSave = DbPropertyHelper.BoolPropertyFromRow(reader, "IsDifferentSupplierSearchSyncDataSave"),
                                IsOptimization = DbPropertyHelper.BoolPropertyFromRow(reader, "IsOptimization"),
                                OptimizationType = (OptimizationType)DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "OptimizationType"),
                                RepricerUserName = DbPropertyHelper.StringPropertyFromRow(reader, "RepricerUserName")
                            };
                        }
                    }
                }

                if (smtpConfigModel != null)
                {
                    try
                    {
                        _memoryCache.Set(key, smtpConfigModel, memoryCacheTime);
                        RedisCacheHelper.Set(key, smtpConfigModel);
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(LoadRePricerEmailConfig),
                            RePricerId = RepricerId,
                            Params = $"RepricerId: {RepricerId}"
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(LoadRePricerEmailConfig),
                    RePricerId = RepricerId,
                    Params = $"RepricerId: {RepricerId}"
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;

            return smtpConfigModel;
        }

        public int DeleteUserMapping(string UserId)
        {
            var watch = Stopwatch.StartNew();

            int rowsAffected = 0;

            try
            {
                using SqlConnection connection = new SqlConnection(_RePricerconnectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.DeleteUserbyUserId, connection);
                command.CommandType = CommandType.StoredProcedure;

                command.Parameters.AddWithValue(Constant.UserId, UserId);

                rowsAffected = command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.DeleteClientDataAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return rowsAffected;
        }

        public int DeleteLoggingData(int rePricerUserId)
        {
            var watch = Stopwatch.StartNew();

            int rowsAffected = 0;

            try
            {
                // Use the SqlConnection with a specified timeout
                using (SqlConnection connection = new SqlConnection(_RePricerconnectionString))
                {
                    connection.Open();

                    // Create the command and specify the stored procedure
                    using (SqlCommand command = new SqlCommand(Constant.DeleteloggingData, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;
                        command.CommandTimeout = _commandTimeout;
                        command.Parameters.AddWithValue(Constant.RePricerUserId, rePricerUserId);

                        // Execute the command and get the number of affected rows
                        rowsAffected = command.ExecuteNonQuery();
                    }
                }

                // Optionally, you can log the time taken for the operation
                //_log.Info($"DeleteLoggingData executed in {watch.ElapsedMilliseconds} ms.");
            }
            catch (SqlException sqlEx) when (sqlEx.Number == -2) // SQL Timeout exception code is -2
            {
                // Handle SQL Timeout specifically
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.DeleteClientDataAsync,
                };
                //_log.Error(irixErrorEntity, new TimeoutException("Database operation timed out", sqlEx));
                throw new TimeoutException("Database operation timed out", sqlEx);
            }
            catch (SqlException sqlEx)
            {
                // Handle other SQL exceptions
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.DeleteClientDataAsync,
                };
                //_log.Error(irixErrorEntity, sqlEx);
                throw;
            }
            catch (Exception ex)
            {
                // Catch all other exceptions
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.DeleteClientDataAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return rowsAffected;
        }

        public async Task<List<TimeZones>> GetTimeZones()
        {
            var key = "GetTimeZones";

            var timeZones = new List<TimeZones>();

            if (_memoryCache.TryGetValue(key, out timeZones))
            {
                return timeZones;
            }

            timeZones = new List<TimeZones>();

            if (RedisCacheHelper.KeyExists(key))
            {
                timeZones = RedisCacheHelper.Get<List<TimeZones>>(key);
            }
            else
            {
                try
                {
                    using SqlConnection connection = RePricerConnection();
                    await connection.OpenAsync();
                    using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetTimeZones);
                    DateTime utcNow = DateTime.UtcNow;

                    while (reader.Read())
                    {
                        var timeZone = new TimeZones
                        {
                            TimeZoneId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TimeZoneId"),
                            TimeZoneName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "TimeZoneName"),
                            Country = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Country")
                        };
                        try
                        {
                            timeZone.Key = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "TimeZoneKey");
                            var timeZoneInfo = TZConvert.GetTimeZoneInfo(timeZone.Key);
                            TimeSpan utcOffset = timeZoneInfo.GetUtcOffset(utcNow);
                            string utcOffsetFormatted = $"{(utcOffset >= TimeSpan.Zero ? "+" : "-")}{utcOffset:hh\\:mm}";

                            var offset = timeZoneInfo.BaseUtcOffset.ToString().Contains("-") ? timeZoneInfo.BaseUtcOffset.ToString().Substring(0, 6) : "+" + timeZoneInfo.BaseUtcOffset.ToString().Substring(0, 5);
                            var offsetToreplace = timeZone.TimeZoneName.Split(')').FirstOrDefault();
                            timeZone.TimeZoneName = timeZone.TimeZoneName.Replace(offsetToreplace, $"(UTC{utcOffsetFormatted}");
                            timeZone.StandardName = timeZoneInfo.StandardName;
                            timeZone.DisplayName = timeZoneInfo.DisplayName;
                            timeZone.DaylightName = timeZoneInfo.DaylightName;
                            timeZone.BaseUtcOffset = timeZoneInfo.BaseUtcOffset.ToString();
                            timeZone.SupportsDaylightSavingTime = timeZoneInfo.SupportsDaylightSavingTime;
                        }
                        catch (Exception ex)
                        {
                        }
                        timeZones.Add(timeZone);
                    }
                    if (timeZones?.Any() == true)
                    {
                        _memoryCache.Set(key, timeZones, TimeSpan.FromDays(1));
                        RedisCacheHelper.Set(key, timeZones, TimeSpan.FromDays(20), false);
                    }
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ClientPersistance,
                        MethodName = nameof(GetTimeZones),
                    };
                    _log.Error(irixErrorEntity, ex);
                }
            }

            return timeZones;
        }

        public int UpdateRepricerStatus(int RePricerUserId, bool isactive = true)
        {
            var watch = Stopwatch.StartNew();

            int rowsAffected = 0;

            try
            {
                using SqlConnection connection = new SqlConnection(_RePricerconnectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.UpdateRepricerStatus, connection);
                command.CommandType = CommandType.StoredProcedure;

                // Add parameters
                command.Parameters.AddWithValue(Constant.RePricerUserId, RePricerUserId);
                command.Parameters.AddWithValue("isActive", isactive);

                rowsAffected = command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.DeleteClientDataAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"ClientPersistance|DeleteClientDataAsync|{elapsedTimeInSeconds}|{RePricerUserId} in {watch.Elapsed}");

            return rowsAffected;
        }
    }
}