# Multi-Supplier Optimization - Technical Implementation Guide

## 🎯 Overview

This guide provides technical implementation details for developers working on the multi-supplier optimization system.

## 🏗️ Architecture Components

### Core Services
```csharp
// Primary orchestrator
public class SupplierSearchService
{
    // Main entry point for multi-supplier optimization
    public async Task<PreBookResults> _1_PrebookAndOptimize_MultiSupplier(...)
    
    // Offer filtering and matching
    private async Task<List<OfferInfo>> _2_GetOfferInfoListAsync(...)
    
    // Room matching logic
    private bool UpdateMatchingRoomOffer(...)
}

// Search operations
public class SearchHelper
{
    // Execute searches across multiple suppliers
    public SearchResponseIRIX SearchSync(...)
    
    // Create search criteria for suppliers
    public SearchCriteria CreateCriteria(...)
}

// Room mapping service
public class GiataService
{
    // Map rooms across suppliers
    public async Task<GiataRoomMapping> GiataApiCall(...)
}
```

### Data Access Layer
```csharp
public class ReservationPersistence
{
    // Load eligible reservations
    public async Task<List<ReservationMainModel>> GetReservationsAsync(...)
    
    // Get prebook criteria
    public async Task<PreBookCriteriaResult> GetPreBookCriteriaDBAll(...)
    
    // Get allowed suppliers
    public async Task<List<string>> GetAllowedProviders(...)
    
    // Insert prebook records
    public void InsertPreBookReservation(...)
}
```

## 🔄 Implementation Flow

### Phase 1: Initialization
```csharp
// Validate client configuration
var repricerClientDetail = _clientServices.GetRePricerDetail(repricerId);
var repricerSchedules = _clientServices.GetRePricerSchedules(repricerId);

// Key validations
if ((repricerSchedules?.IsMultiSupplierEnabled ?? false) == false)
    return CreateErrorResult("Multi-supplier not enabled");

if ((repricerSchedules?.IsJobsEnable ?? false) == false)
    return CreateErrorResult("Jobs not enabled");

if (string.IsNullOrWhiteSpace(supplierName) && isOptimizeTriggeredManually)
    return CreateErrorResult("Supplier name required for manual trigger");
```

### Phase 2: Data Loading
```csharp
// Load all required data in parallel
var searchCriterias = await _reservationPersistance.GetReservationsAsync(repricerId, reservationId ?? 0);
var roomReservations = await _reservationPersistance.GetReservationsRoomAsync(repricerId);
var reservationPrebookCounts = await _reservationPersistance.GetPrebookReservationIdsAsync(repricerId);
var preBookCriteriasAll = await _reservationPersistance.GetPreBookCriteriaDBAll(repricerId, isMultiSupplier);
var cancellationPolicies = await _reservationPersistance.GetCancellationPolicyReservationIdsAsync(repricerId);
var allowedProviders = await _reservationPersistance.GetAllowedProviders(repricerId);
```

### Phase 3: Supplier Filtering
```csharp
// Filter to allowed suppliers only
searchCriterias = searchCriterias?
    .Where(searchcriteria => allowedProviders
        .Any(provider => provider.Equals(searchcriteria.supplierName, StringComparison.OrdinalIgnoreCase)))
    .ToList();

// Exclude original supplier from multi-supplier search
var suppliersWithoutReservationSupplier = allowedProviders
    .Where(x => x.ToLower() != reservation.supplierName.ToLower())
    .ToList();

// Process in batches of 5
int batchSize = 5;
var batches = Batch(suppliersWithoutReservationSupplier, batchSize);
```

### Phase 4: Parallel Processing
```csharp
// Configure parallel processing
var parallelOptions = UtilCommonConstants.GetParallelOptions(50);
if (reservationId > 0)
{
    parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 1 };
}

// Process reservations in parallel
await Parallel.ForEachAsync(filteredReservations, parallelOptions, async (reservation, CancellationToken) =>
{
    // Process each reservation
    await ProcessReservation(reservation, cancellationToken);
});
```

### Phase 5: Giata Room Mapping
```csharp
// Map rooms across suppliers
foreach (var prebookcriteriaroom in prebookcriteria)
{
    var giatadatafororiginalreservation = await _giataService.GiataApiCall(
        repricerId, 
        reservation.ReservationId.ToString(), 
        hotelName, 
        prebookcriteriaroom.RoomName, 
        Destinations, 
        reservationsupplier
    );
    
    if (giatadatafororiginalreservation == null)
    {
        // Stop processing - room mapping failed
        itemAttemptResult = UpdateErrorAndReason(
            repricerId, 
            reservationIdInLoop, 
            itemAttemptResult, 
            OptimizationStatusEnum.GiataMappingNotFound, 
            ReasonCode.GiataReservationMappingNotFound
        );
        break;
    }
    
    prebookcriteriaroom.giataroommappingdetail = giatadatafororiginalreservation;
}
```

### Phase 6: Price Validation
```csharp
// Calculate profit and validate thresholds
var originalReservationPrice = prebookcriteriaresult.IssueNet;
var searchsyncprice = _searchserviceHelper.RoundToDecimalPlaces(
    System.Convert.ToDecimal(hotelPrice) * hotellevelfactor
);
var profit = _searchserviceHelper.RoundToDecimalPlaces(
    originalReservationPrice - searchsyncprice
);

// Check price thresholds
var isPriceThreshold = false;
if (extraClientConfig?.IsUsePercentage == true)
{
    var profitperc = (profit / originalReservationPrice) * 100;
    isPriceThreshold = profitperc >= extraClientConfig.PriceDifferencePercentage;
}
else
{
    isPriceThreshold = profit >= pricethresholdInOriginalReservationCurrency;
}
```

### Phase 7: Prebook Creation
```csharp
// Create prebook via IRIX API
var prebook = _irixAdapter.Prebookresponse(
    reservationIdInLoop, 
    prebookrequest, 
    constants.prebookresp, 
    repricerId, 
    prebooktokens, 
    srk, 
    offerIndex, 
    hotelsIndex, 
    stepSearchtoken, 
    isMultiSupplier
);

if (prebook != null)
{
    itemAttemptResult.IsPrebookSucess = true;
    
    // Recalculate profit with actual prebook price
    var prebookPrice = System.Convert.ToDecimal(prebook?.package?.price?.components?.supplier?.value);
    profit = _searchserviceHelper.RoundToDecimalPlaces(originalReservationPrice - prebookPrice);
}
```

### Phase 8: Cancellation Policy Validation
```csharp
// Validate cancellation policies
var cancellationresult = _searchserviceHelper.CancellationCheck(
    repricerId, 
    cancellationpolicybyreservationid, 
    prebook.cancellationPolicy, 
    prebookcriteriaresult, 
    prebookPrice, 
    reservation
);

// Check if policy is acceptable
if (cancellationresult != null && 
    (cancellationresult.cPStatus == CPBucketStatus.loose.ToString() || 
     cancellationresult.cPStatus == CPBucketStatus.tightWithBuffer.ToString() || 
     cancellationresult.cPStatus == CPBucketStatus.CancellationChargesApplicable.ToString()))
{
    isPrebookCreated = true;
}
```

### Phase 9: Dry Run Execution
```csharp
// Execute dry run for validation
if (itemAttemptResult.IsPrebookSucess == true && 
    isUpdateDB == true && 
    cancellationresult?.cPStatus?.ToLower() == CPBucketStatus.loose.ToString())
{
    var dryRunResponse = _dryRunOptimizationService._2_DryRunOptimizationApiIRIX(
        optimizeBookingReq, 
        reservationIdInLoop, 
        isUpdateDB: isOptimizeTriggeredManually, 
        isCacheRefresh: isOptimizeTriggeredManually, 
        isMultiSupplier: true
    );
    
    if (dryRunResponse?.ExpectedGain?.Value > 0)
    {
        isPriceThreshold = _searchserviceHelper.IsPriceThreshold(
            originalReservationPrice, 
            dryRunResponse.ExpectedGain.Value, 
            repricerClientDetail, 
            prebookcriteriaresult, 
            reservationIdInLoop, 
            itemAttemptResult
        );
        
        if (isPriceThreshold)
        {
            itemAttemptResult.IsExpectedGainAboveThreshold = true;
            // Send email notification for manual approval
        }
    }
}
```

### Phase 10: Final Optimization
```csharp
// Execute final optimization (manual approval required)
if (extraClientConfig?.OptimizationType != null && 
    cancellationresult?.cPStatus?.ToLower() == CPBucketStatus.loose.ToString() && 
    isPrebookPriceValid && 
    optimizationStatus?.IsOptimizable == true && 
    isUpdateDB == true && 
    itemAttemptResult.IsPrebookSucess == true && 
    isOptimized == false && 
    isOptimizeTriggeredManually == true) // MANUAL TRIGGER REQUIRED
{
    var optimizationBookingResponse = _optimizationService._3_OptimizeBookingApiIRIX(
        optimizeBookingReq, 
        reservationIdInLoop, 
        isOptimizeTriggeredManually, 
        isMultiSupplier
    );
}
```

## 🔧 Key Implementation Patterns

### Error Handling
```csharp
// Consistent error handling pattern
private PreBookResponseResult UpdateErrorAndReason(
    int repricerId, 
    int reservationId, 
    PreBookResponseResult result, 
    OptimizationStatusEnum status, 
    ReasonCode reason, 
    string message = null)
{
    result.OptimizableStatus = new OptimizationOptimizationBooking
    {
        Status = status,
        ReasonCode = reason,
        Message = message
    };
    
    // Log error for monitoring
    _logger.LogWarning($"Multi-supplier optimization failed: {reason} for reservation {reservationId}");
    
    return result;
}
```

### Timeout Management
```csharp
// Timeout configuration
var timeoutMinutes = isOptimizeTriggeredManually ? 5 : 0.5; // 5 min manual, 30 sec auto
var timeout = TimeSpan.FromMinutes(timeoutMinutes);

using var cts = new CancellationTokenSource(timeout);
```

### Caching Strategy
```csharp
// Cache frequently accessed data
var cacheKey = $"PreBookCriteria_{repricerId}_{isMultiSupplier}";
if (RedisCacheHelper.KeyExists(cacheKey))
{
    var cachedResults = RedisCacheHelper.Get<PreBookCriteriaResult>(cacheKey);
    if (cachedResults != null)
        return cachedResults;
}

// Cache results for future use
RedisCacheHelper.Set(cacheKey, results, TimeSpan.FromHours(1));
```

## 📊 Performance Optimization

### Database Optimization
```csharp
// Use bulk operations for large datasets
public void BulkInsertMultiSupplierRooms(List<MultiSupplierRoom> rooms)
{
    using var connection = new SqlConnection(_connectionString);
    connection.Open();
    
    using var transaction = connection.BeginTransaction();
    try
    {
        foreach (var batch in rooms.Batch(1000))
        {
            // Process in batches of 1000
            BulkInsertBatch(batch, connection, transaction);
        }
        transaction.Commit();
    }
    catch
    {
        transaction.Rollback();
        throw;
    }
}
```

### Memory Management
```csharp
// Dispose resources properly
public async Task ProcessReservationsAsync(List<ReservationMainModel> reservations)
{
    foreach (var reservation in reservations)
    {
        using var scope = _serviceProvider.CreateScope();
        var processor = scope.ServiceProvider.GetRequiredService<IReservationProcessor>();
        
        await processor.ProcessAsync(reservation);
        
        // Explicit garbage collection for large datasets
        if (reservations.Count > 1000)
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }
    }
}
```

## 🧪 Testing Strategies

### Unit Testing
```csharp
[Test]
public async Task MultiSupplierOptimization_ValidReservation_ShouldCreatePrebook()
{
    // Arrange
    var repricerId = 99; // Test repricer
    var reservationId = 12345;
    
    // Act
    var result = await _supplierSearchService._1_PrebookAndOptimize_MultiSupplier(
        repricerId, reservationId, isUpdateDB: false, isOptimizeTriggeredManually: true
    );
    
    // Assert
    Assert.IsTrue(result.PreBookResponseResults.Any(x => x.IsPrebookSucess));
}
```

### Integration Testing
```csharp
[Test]
public async Task EndToEndMultiSupplierFlow_ShouldCompleteSuccessfully()
{
    // Test complete flow from data loading to optimization
    // Use test data with known outcomes
    // Verify all phases complete without errors
}
```

---

*Technical Implementation Guide - Last Updated: December 2024*
