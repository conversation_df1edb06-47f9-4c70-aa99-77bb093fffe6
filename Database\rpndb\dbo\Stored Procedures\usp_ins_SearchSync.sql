﻿CREATE PROCEDURE [dbo].[usp_ins_SearchSync]
    @SearchSyncDataJson NVARCHAR(MAX),
    @RePricerId INT,
    @ReservationId INT
AS
BEGIN
    -- Insert into SearchSync_Hotel
    INSERT INTO SearchSync_Hotel (
        HotelIndex,
        HotelName,
        MinPriceValue,
        MinPriceCurrency,
        Recommended,
        SpecialDeal,
        CreatedDate,
        ReservationId,
        RePricerId
    )
    SELECT
        JSON_VALUE(hotel.value, '$.HotelIndex'),
        JSON_VALUE(hotel.value, '$.HotelName'),
        JSON_VALUE(hotel.value, '$.MinPriceValue'),
        JSON_VALUE(hotel.value, '$.MinPriceCurrency'),
        JSON_VALUE(hotel.value, '$.Recommended'),
        JSO<PERSON>_VALUE(hotel.value, '$.SpecialDeal'),
        GETUTCDATE(),
        @ReservationId,
        @RePricerId
		FROM OPENJSON(@SearchSyncDataJson) AS hotel                
        --CROSS APPLY OPENJSON(j.value, '$.hotelMain') AS hotel      

   
    -- Insert into SearchSync_Offer
    INSERT INTO SearchSync_Offer (
        HotelIndex,
        Id_offer,
        Recommended,
        SpecialDeal,
        MinPriceValue,
        MinPriceCurrency,
        CreatedDate,
		ReservationId,
        RePricerId
    )
    SELECT
        JSON_VALUE(offer.value, '$.HotelIndex'),
        JSON_VALUE(offer.value, '$.Id_offer'),
        JSON_VALUE(offer.value, '$.Recommended'),
        JSON_VALUE(offer.value, '$.SpecialDeal'),
        JSON_VALUE(offer.value, '$.MinPriceValue'),
        JSON_VALUE(offer.value, '$.MinPriceCurrency'),
        GETUTCDATE(),
		@ReservationId,
        @RePricerId
		FROM OPENJSON(@SearchSyncDataJson) AS j                  
        CROSS APPLY OPENJSON(j.value, '$.HotelOffers') AS offer;

    -- Insert into SearchSync_Room
    INSERT INTO SearchSync_Room (
        Id_offer,
        RoomIndex,
        RoomName,
        RoomStatus,
        Board,
        BoardBasis,
        NonRefundable,
        Info,
        SellingValue,
        SellingCurrency,
        CreatedDate,
		ReservationId,
        RePricerId,
		CancellationDate,
		CancellationChargeValue,
		CancellationChargeCurrency
    )
    SELECT
        JSON_VALUE(room.value, '$.Id_offer'),
        JSON_VALUE(room.value, '$.RoomIndex'),
        JSON_VALUE(room.value, '$.RoomName'),
        JSON_VALUE(room.value, '$.RoomStatus'),
        JSON_VALUE(room.value, '$.Board'),
        JSON_VALUE(room.value, '$.BoardBasis'),
        JSON_VALUE(room.value, '$.NonRefundable'),
        JSON_VALUE(room.value, '$.Info'),
        JSON_VALUE(room.value, '$.SellingValue'),
        JSON_VALUE(room.value, '$.SellingCurrency'),
        GETUTCDATE(),
		@ReservationId,
        @RePricerId,
		 JSON_VALUE(room.value, '$.CancellationDate'),
		  JSON_VALUE(room.value, '$.CancellationChargeValue'),
		    TRY_CAST(JSON_VALUE(room.value, '$.CancellationChargeCurrency') as date)
     FROM OPENJSON(@SearchSyncDataJson) AS j                  
    CROSS APPLY OPENJSON(j.value, '$.HotelOffers') AS offer
    CROSS APPLY OPENJSON(offer.value, '$.SearchRooms') AS room;

INSERT INTO SearchSync_Package (
        Id_offer,
        PackageCode,
        PackageToken,
        SellingValue,
        SellingCurrency,
        Complete,
        CreatedDate,
		ReservationId,
        RePricerId
    )
    SELECT
        JSON_VALUE(package.value, '$.Id_offer'),
        JSON_VALUE(package.value, '$.PackageCode'),
        JSON_VALUE(package.value, '$.PackageToken'),
        JSON_VALUE(package.value, '$.SellingValue'),
        JSON_VALUE(package.value, '$.SellingCurrency'),
        JSON_VALUE(package.value, '$.Complete'),
        GETUTCDATE(),
		@ReservationId,
        @RePricerId
     FROM OPENJSON(@SearchSyncDataJson) AS j                  
    CROSS APPLY OPENJSON(j.value, '$.HotelOffers') AS offer
    CROSS APPLY OPENJSON(offer.value, '$.packages') AS package;
	-- Insert into SearchSync_PackageRoom for every index of packages


    -- Insert into SearchSync_PackageRoom
    INSERT INTO SearchSync_PackageRoom (
        PackageCode,
        PackageRoomCode,
        OccupancyAdults,
        OccupancyChildrenAges,
        CreatedDate,
		ReservationId,
        RePricerId
    )
    SELECT
        JSON_VALUE(pr.value, '$.PackageCode'),
        JSON_VALUE(pr.value, '$.PackageRoomCode'),
        JSON_VALUE(pr.value, '$.Adults'),
        JSON_QUERY(pr.value, '$.ChildrenAges'),
        GETUTCDATE(),
		@ReservationId,
        @RePricerId   
     FROM OPENJSON(@SearchSyncDataJson) AS j                  
    CROSS APPLY OPENJSON(j.value, '$.HotelOffers') AS offer
    CROSS APPLY OPENJSON(offer.value, '$.packages') AS package
    CROSS APPLY OPENJSON(package.value, '$.PackageRooms') AS pr;

    -- Insert into SearchSync_RoomReference
    INSERT INTO SearchSync_RoomReference (
        PackageRoomCode,
        RoomCode,
        RoomToken,
        Selected,
        CreatedDate,
		ReservationId,
        RePricerId
    )
    SELECT
        JSON_VALUE(rr.value, '$.PackageRoomCode'),
        JSON_VALUE(rr.value, '$.RoomCode'),
        JSON_VALUE(rr.value, '$.RoomToken'),
        JSON_VALUE(rr.value, '$.Selected'),
        GETUTCDATE(),
		@ReservationId,
        @RePricerId
     FROM OPENJSON(@SearchSyncDataJson) AS j                  
    CROSS APPLY OPENJSON(j.value, '$.HotelOffers') AS offer
    CROSS APPLY OPENJSON(offer.value, '$.packages') AS package
    CROSS APPLY OPENJSON(package.value, '$.PackageRooms') AS pr
    CROSS APPLY OPENJSON(pr.value, '$.RoomReferences') AS rr;

END;