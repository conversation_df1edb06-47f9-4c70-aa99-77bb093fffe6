﻿using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace Irix.Entities
{
    public class ClientDetail
    {
        public string ClientName
        {
            get; set;
        }

        public string ClientAdminUserID
        {
            get; set;
        }

        public string ClientAdminPassword
        {
            get; set;
        }

        public string ResellerUserID
        {
            get; set;
        }

        public string ResellerPassword
        {
            get; set;
        }

        public string ApiScope
        {
            get; set;
        }

        public string ResellerApiScope
        {
            get; set;
        }
    }

    public class RepricerResponse
    {
        public int RepricerUserID
        {
            get; set;
        }

        public string UserName
        {
            get; set;
        }
    }

    /// <summary>
    /// Represents a Complete RePricer client configuration with all settings and credentials
    /// </summary>
    /// <remarks>
    /// This class contains all the configuration details needed for a RePricer client,
    /// including authentication credentials, API endpoints, scheduling settings, and optimization parameters.
    /// </remarks>
    public class RePricerDetail
    {
        /// <summary>
        /// Indicates whether automated jobs are enabled for this RePricer configuration
        /// </summary>
        /// <remarks>
        /// When set to true, scheduled jobs like reservation fetching and optimization will run automatically
        /// according to the configured schedule. When false, jobs must be triggered manually.
        /// </remarks>
        [SwaggerSchema(Description = "Indicates whether automated jobs are enabled for this RePricer configuration")]
        [SwaggerSchemaExample("true")]
        public bool IsJobsEnable
        {
            get; set;
        }

        /// <summary>
        /// Unique identifier for the RePricer configuration
        /// </summary>
        /// <remarks>
        /// This ID is automatically assigned when creating a new configuration.
        /// For updates, this ID must be specified to identify which configuration to modify.
        /// </remarks>
        [Required]
        [SwaggerSchema(Description = "Unique identifier for the RePricer configuration")]
        [SwaggerSchemaExample("1")]
        public int RepricerUserID
        {
            get; set;
        }

        /// <summary>
        /// Name of the RePricer client
        /// </summary>
        /// <remarks>
        /// This name should match the username used during signup.
        /// It is used for identification and display purposes throughout the system.
        /// </remarks>
        [Required]
        [SwaggerSchema(Description = "Name of the RePricer client (should match the username used during signup)")]
        [SwaggerSchemaExample("Accent")]
        public string RepricerUserName
        {
            get; set;
        }

        /// <summary>
        /// URL endpoint for the admin API used to fetch reservations
        /// </summary>
        /// <remarks>
        /// This URL is used by the system to connect to the client's reservation system
        /// and retrieve booking information for optimization.
        /// </remarks>
        [Required]
        [SwaggerSchema(Description = "URL endpoint for the admin API used to fetch reservations")]
        [SwaggerSchemaExample("https://admin.example.com/api")]
        public string AdminUrl
        {
            get; set;
        }

        /// <summary>
        /// User ID for authenticating with the admin API
        /// </summary>
        /// <remarks>
        /// This credential is used to authenticate with the admin API endpoint
        /// when fetching reservation data.
        /// </remarks>
        [Required]
        [SwaggerSchema(Description = "User ID for authenticating with the admin API")]
        [SwaggerSchemaExample("admin_user")]
        public string AdminUserId
        {
            get; set;
        }

        /// <summary>
        /// Password for authenticating with the admin API
        /// </summary>
        /// <remarks>
        /// This credential is used to authenticate with the admin API endpoint
        /// when fetching reservation data.
        /// </remarks>
        [Required]
        [SwaggerSchema(Description = "Password for authenticating with the admin API")]
        [SwaggerSchemaExample("password123")]
        public string AdminPassword
        {
            get; set;
        }

        /// <summary>
        /// ResellerURL
        /// </summary>
        [Required]
        public string ResellerUrl
        {
            get; set;
        }

        /// <summary>
        /// ResellerUserId
        /// </summary>
        public string ResellerUserId
        {
            get; set;
        }

        [Required]
        public string ResellerPassword
        {
            get; set;
        }

        [Required]
        public string AdminApiScope
        {
            get; set;
        }

        [Required]
        public string ResellerApiScope
        {
            get; set;
        }

        /// <summary>
        /// By Default its true IsActive that its Job should or not
        /// </summary>
        public bool IsActive
        {
            get; set;
        }

        /// <summary>
        /// ClientConfig
        /// </summary>
        public ClientConfiguration? ClientConfiguration
        {
            get; set;
        }

        /// <summary>
        /// ClientDetail For Filtering Reservations
        /// </summary>
        public ExtraClientDetail ExtraClientDetail
        {
            get; set;
        }

        /// <summary>
        /// Job Scheduling Cron Timer
        /// </summary>
        public ClientConfigScheduler? ClientConfigScheduler
        {
            get; set;
        }

        /// <summary>
        /// Specifies the optimization mode for this RePricer configuration
        /// </summary>
        /// <remarks>
        /// The optimization type determines how the system handles Price optimization:
        /// - Undefined (0): Default state, no optimization behavior defined
        /// - Demo (1): Simulation mode, shows optimization possibilities without making actual changes
        /// - SemiAutomatic (2): Some optimizations happen automatically, others require approval
        /// - Automatic (3): All optimizations are performed automatically based on configured rules
        /// </remarks>
        [SwaggerSchema(Description = "Specifies the optimization mode (Demo, SemiAutomatic, or Automatic)")]
        [SwaggerSchemaExample("2")]
        public OptimizationType OptimizationType
        {
            get; set;
        }

        public String? ModifiedBy
        {
            get; set;
        }

        public string? EmailTo
        {
            get; set;
        }

        public string? EmailCC
        {
            get; set;
        }

        public string? EmailBcc
        {
            get; set;
        }

        public List<HotelMasterModel>? RestrictedHotel
        {
            get; set;
        }

        public List<CityMaster>? RestrictedCity
        {
            get; set;
        }

        public List<CountryMasterModel>? RestrictedCountry
        {
            get; set;
        }

        public List<ResellerMaster>? RestrictedReseller
        {
            get; set;
        }

        public List<SupplierMaster>? RestrictedSupplier
        {
            get; set;
        }

        /// <summary>
        /// Check If Optimization Allowed or not
        /// </summary>
        public bool IsOptimizationAllowed
        {
            get
            {
                return (this?.OptimizationType == OptimizationType.SemiAutomatic || this?.OptimizationType == OptimizationType.Automatic) ? true : false;
            }
        }

        public int AutoJobServerId
        {
            get;
            set;
        }

        /// <summary>
        /// Delay between optimization job runs in hours
        /// </summary>
        /// <remarks>
        /// This property controls how frequently the optimization job runs.
        /// Default value is 1 hour if not specified.
        /// </remarks>
        [SwaggerSchema(Description = "Delay between optimization job runs in hours")]
        [SwaggerSchemaExample("1")]
        public int DelayBetweenOptimizationJob
        {
            get;
            set;
        }

        /// <summary>
        /// Delay between daily reservation download operations in hours
        /// </summary>
        /// <remarks>
        /// This property controls how frequently the system downloads reservations.
        /// Default value is 3 hours if not specified.
        /// </remarks>
        [SwaggerSchema(Description = "Delay between daily reservation download operations in hours")]
        [SwaggerSchemaExample("3")]
        public int DelayBetweenDailyDownloadReservation
        {
            get;
            set;
        }

        /// <summary>
        /// Delay between requests to the same supplier in seconds
        /// </summary>
        /// <remarks>
        /// This property controls the rate limiting for requests to the same supplier.
        /// Default value is 2 seconds if not specified.
        /// </remarks>
        [SwaggerSchema(Description = "Delay between requests to the same supplier in seconds")]
        [SwaggerSchemaExample("2")]
        public int DelayBetweenRequestsSameSupplier
        {
            get;
            set;
        }

        /// <summary>
        /// Delay between requests to multiple suppliers in seconds
        /// </summary>
        /// <remarks>
        /// This property controls the rate limiting for requests to multiple suppliers.
        /// Default value is 2 seconds if not specified.
        /// </remarks>
        [SwaggerSchema(Description = "Delay between requests to multiple suppliers in seconds")]
        [SwaggerSchemaExample("2")]
        public int DelayBetweenRequestsMultiSupplier
        {
            get;
            set;
        }


        /// <summary>
        /// Indicates whether to enable multi-supplier synchronization
        /// </summary>
        [SwaggerSchema(Description = "Indicates whether to enable multi-supplier synchronization")]
        [SwaggerSchemaExample("false")]
        public bool IsMultiSupplierEnabled
        {
            get;
            set;
        }



        /// <summary>
        /// Last update date
        /// </summary>
        [SwaggerSchema(Description = "Last update date")]
        [SwaggerSchemaExample("2025-02-18 09:35:02.067")]
        public DateTime? UpdateDate
        {
            get;
            set;
        }
    }

    public class RePricerAuthDetail
    {
        public int RepricerUserID
        {
            get; set;
        }

        public string AdminURL
        {
            get; set;
        }

        public string AdminUserID
        {
            get; set;
        }

        public string AdminPassword
        {
            get; set;
        }

        public string ResellerURL
        {
            get; set;
        }

        public string ResellerUserID
        {
            get; set;
        }

        public string ResellerPassword
        {
            get; set;
        }

        public string ApiScope
        {
            get; set;
        }

        public string ResellerApiScope
        {
            get; set;
        }
    }

    public class ClientConfigScheduler
    {
        /// <summary>
        /// By Default its value is 10 */2 * * *
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "By Default its value is 10 */2 * * *")]
        [SwaggerSchemaExample("10 */2 * * *")]
        public string? Reservation_CronTime
        {
            get; set;
        }

        /// <summary>
        /// By Default its value is 5 */5 * * *
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "By Default its value is 5 */5 * * *")]
        [SwaggerSchemaExample("5 */5 * * *")]
        public string? PreBook_CronTime
        {
            get; set;
        }

        /// <summary>
        /// By Default its value to 0 12 * * *
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "By Default its value is 5 */5 * * *")]
        [SwaggerSchemaExample("5 */5 * * *")]
        public string? CurrencyExchange_CronTime
        {
            get; set;
        }

        [Required]
        [SwaggerSchema(Description = "TimeZoneId By Default UTC")]
        public int TimeZoneId
        {
            get; set;
        }

        public string? TimeZoneName
        {
            get; set;
        }
    }

    public class ClientConfigScheduler_Hangfire
    {
        public bool IsActive
        {
            get; set;
        }

        public bool IsJobsEnable
        {
            get; set;
        }

        public int RepricerId
        {
            get; set;
        }

        /// <summary>
        /// By Default its value is 10 */2 * * *
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "By Default its value is 10 */2 * * *")]
        [SwaggerSchemaExample("10 */2 * * *")]
        public string? Reservation_CronTime
        {
            get; set;
        }

        /// <summary>
        /// By Default its value is 5 */5 * * *
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "By Default its value is 5 */5 * * *")]
        [SwaggerSchemaExample("5 */5 * * *")]
        public string? PreBook_CronTime
        {
            get; set;
        }

        /// <summary>
        /// By Default its value to 0 12 * * *
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "By Default its value is 5 */5 * * *")]
        [SwaggerSchemaExample("5 */5 * * *")]
        public string? CurrencyExchange_CronTime
        {
            get; set;
        }

        [Required]
        [SwaggerSchema(Description = "By Default its value is 5 */5 * * *")]
        [SwaggerSchemaExample("*/30 * * * *")]
        public string? RefreshCache_CronTime
        {
            get; set;
        }

        [Required]
        [SwaggerSchema(Description = "By Default its value is UTC")]
        [SwaggerSchemaExample("UTC")]
        public string? TimeZoneName
        {
            get; set;
        }

        [Required]
        [SwaggerSchema(Description = "Repricer Name")]
        public string? RepricerName
        {
            get; set;
        }

        public bool IsMultiSupplierEnabled
        {
            get; set;
        }

        /// <summary>
        /// Optimization Type
        /// </summary>
        public OptimizationType OptimizationType
        {
            get; set;
        }

        /// <summary>
        /// Check If Optimization Allowed or not
        /// </summary>
        public bool IsOptimizationAllowed
        {
            get
            {
                return (this?.OptimizationType == OptimizationType.SemiAutomatic || this?.OptimizationType == OptimizationType.Automatic) ? true : false;
            }
        }

        public int AutoJobServerId
        {
            get;
            set;
        }

        /// <summary>
        /// If this is true than only ResellerCPHourDifference will be checked else default behavior.
        /// </summary>
        public bool IsUseResellerCPHourDifference
        {
            get;
            set;
        }

        /// <summary>
        /// When prebook supplier cancellation policy is less than reservation supplier cancellation policy than
        /// if Difference hour is less than or equal to this value then reservation will still be attempted to optimized.
        /// </summary>
        public int ResellerCPHourDifference
        {
            get;
            set;
        }
    }

    public class ExtraClientDetail
    {
        //public bool isroomboard { get; set; }
        //public bool isroomlevelprice { get; set; }
        //public bool isroominfor { get; set; }

        /// <summary>
        /// No longer needed ##TODO we must deelte it
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "TravelDaysMinSearchInDays")]
        [SwaggerSchemaExample("45")]
        public int TravelDaysMaxSearchInDays
        {
            get; set;
        }

        /// <summary>
        /// Used for Cancellation policy start date buffer now. ##TODO rename to CancellationPolicyStartDateBuffer
        /// DaysBeforeCancellationPolicyStartDate
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "TravelDaysMinSearchInDays")]
        [SwaggerSchemaExample("30")]
        public int TravelDaysMinSearchInDays
        {
            get; set;
        }

        /// <summary>
        /// Not used
        /// </summary>
        [Required]
        [SwaggerSchema(Description = "MaxNumberOfTimesOptimization")]
        [SwaggerSchemaExample("5")]
        public int MaxNumberOfTimesOptimization
        {
            get; set;
        }

        [Required]
        [SwaggerSchema(Description = "ClientConfig_DaysDifferenceInPreBookCreation")]
        [SwaggerSchemaExample("3")]
        public int ClientConfig_DaysDifferenceInPreBookCreation
        {
            get; set;
        }

        /// <summary>
        /// By Default its value is 20.0
        /// </summary>
        [SwaggerSchema(Description = "By Default its value is 20.0")]
        [SwaggerSchemaExample("20.0")]
        public decimal? PriceDifferenceValue
        {
            get; set;
        }

        /// <summary>
        /// By Default its value is 0.0
        /// </summary>
        [SwaggerSchema(Description = "By Default its value is 0.0")]
        [SwaggerSchemaExample("0.0")]
        public decimal? PriceDifferencePercentage
        {
            get; set;
        }

        /// <summary>
        /// By Default its value is false
        /// </summary>
        [SwaggerSchema(Description = "By Default its value is false")]
        [SwaggerSchemaExample("false")]
        public bool IsUsePercentage
        {
            get; set;
        }

        /// <summary>
        /// Email Address of Client to which Mail to send In Case of Null Report Email will only be send to InternalUsers.
        /// </summary>
        [SwaggerSchema(Description = "ReportEmailToSend")]
        [SwaggerSchemaExample("<EMAIL>")]
        public string? ReportEmailToSend
        {
            get; set;
        }

        //public bool IsUseSendgrid { get; set; }

        [SwaggerSchema(Description = "Number of days in minus upto that,  The Repricer will ignored while showing Cancellation Policy Edge Case data.")]
        [SwaggerSchemaExample("10")]
        public int DaysLimitCancellationPolicyEdgeCase
        {
            get; set;
        }

        [SwaggerSchema(Description = "On/Off DaysLimitCancellationPolicyEdgeCase setting.")]
        [SwaggerSchemaExample("true/false")]
        public bool IsUseDaysLimitCancellationPolicyEdgeCase
        {
            get; set;
        }

        [SwaggerSchema(Description = "If this is on/1 then for Price Edge Case, Prebook will be created and then such successful reservations will be shown in  Price Edge Case report")]
        [SwaggerSchemaExample("true/false")]
        public bool IsCreatePrebookForPriceEdgeCase
        {
            get; set;
        }

        [SwaggerSchema(Description = "Currency")]
        public string? Currency
        {
            get; set;
        }





        /// <summary>
        /// Indicates whether to use the Reseller CP Hour Difference feature
        /// </summary>
        [SwaggerSchema(Description = "Indicates whether to use the Reseller CP Hour Difference feature")]
        [SwaggerSchemaExample("false")]
        public bool IsUseResellerCPHourDifference
        {
            get;
            set;
        }

        /// <summary>
        /// When prebook supplier cancellation policy is less than reservation supplier cancellation policy than
        /// if Difference hour is less than or equal to this value then reservation will still be attempted to optimized.
        /// Hour difference for Reseller CP when IsUseResellerCPHourDifference is true
        /// </summary>
        [SwaggerSchema(Description = "Hour difference for Reseller CP when IsUseResellerCPHourDifference is true")]
        [SwaggerSchemaExample("0")]
        public int ResellerCPHourDifference
        {
            get;
            set;
        }

        /// <summary>
        /// Cancellation Policy buffer date time difference percentage. It will be considered for optimizations.
        /// For example optimization that are getting rejected due to being 1 minute stricter , now will be processed based on this.
        /// </summary>
        [SwaggerSchema(Description = "Cancellation Policy buffer amount percentage. It will be considered for optimizations")]
        [SwaggerSchemaExample("false")]
        public decimal CPTimeAllowedBufferPercentage
        {
            get;
            set;
        }

        /// <summary>
        /// Cancellation Policy buffer amount percentage. It will be considered for optimizations
        /// For example optimization that are getting rejected due to being 1 EUR stricter , now will be processed based on this.
        /// </summary>
        [SwaggerSchema(Description = "Cancellation Policy buffer amount percentage. It will be considered for optimizations")]
        [SwaggerSchemaExample("false")]
        public decimal CPAmountAllowedBufferPercentage
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets the original value of the price difference threshold.
        /// Just for display purpose so we can verify what was the original value before buffer.
        /// </summary>
        public decimal? PriceDifferenceValueOriginal
        {
            get;
            set;
        }
        /// <summary>
        /// Price difference value allowed buffer percentage. It will be considered for optimizations if value is &gt; 0.
        /// For example optimization that are getting rejected due to price difference being slightly over the threshold, now will be processed based on this buffer.
        /// (Example of Calculations : PriceDifferenceValue was 20, PriceDifferenceValueAllowedBufferPercentage = 2, Now new PriceDifferenceValue = 18 , and PriceDifferenceValueOriginal = 20)
        /// PriceDifferenceValueOriginal - The amount before this buffer is applied (Original Profit Threshold). 
        /// PriceDifferenceValue - Now This amount is Calculated and reduced by BufferPercentage (New Calculated Profit Threshold). 
        /// </summary>
        [SwaggerSchema(Description = "Price difference value allowed buffer percentage. It will be considered for optimizations. Should not be less than 1")]
        [SwaggerSchemaExample("0")]
        public decimal PriceDifferenceValueAllowedBufferPercentage
        {
            get;
            set;
        }


        /// <summary>
        /// Indicates whether to check cross supplier before optimization
        /// When enabled, the system will verify cross supplier availability before proceeding with optimization.
        /// </summary>
        [SwaggerSchema(Description = "Indicates whether to check cross supplier before optimization")]
        [SwaggerSchemaExample("false")]
        public bool IsCheckCrossSupperBeforeOptimization
        {
            get;
            set;
        }
        public bool IsCheckProfitAfterCancellation
        {
            get;
            set;
        }
         

    }

    /// <summary>
    /// Defines the different optimization modes available for RePricer configurations
    /// </summary>
    public enum OptimizationType
    {
        /// <summary>
        /// Default state, no optimization behavior defined
        /// </summary>
        Undefined = 0,

        /// <summary>
        /// Simulation mode, shows optimization possibilities without making actual changes
        /// </summary>
        Demo = 1,

        /// <summary>
        /// Some optimizations happen automatically, others require approval
        /// </summary>
        SemiAutomatic = 2,

        /// <summary>
        /// All optimizations are performed automatically based on configured rules
        /// </summary>
        Automatic = 3
    }
}