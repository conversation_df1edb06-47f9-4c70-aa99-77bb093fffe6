# AI Context Prompt: Hotel Reservation Re-Optimization Platform
## Optimized AI Interaction Reference

## ⚠️ CRITICAL CODE ANALYSIS PROTOCOL

**🚨 NEVER ASSUME ANYTHING - ALWAYS READ ALL CODE AND PROCEDURES LINE BY LINE**

### **🔍 MANDATORY LINE-BY-LINE ANALYSIS PROTOCOL**

**B<PERSON>OR<PERSON> making ANY changes or references to code:**

1. **READ EVERY SINGLE LINE** of target methods, stored procedures, and related code
2. **VERIFY EVERY COLUMN NAME** against actual table schemas before using them
3. **CHECK EVERY STORED PROCEDURE NAME** in Constants files (never assume names)
4. **VALIDATE EVERY TABLE REFERENCE** against actual database definitions
5. **TRACE COMPLETE DATA FLOW** through actual code execution paths
6. **FOLLOW ALL CONDITIONAL LOGIC** (if/else branches, parameter conditions)
7. **IDENTIFY ALL DATABASE CALLS** and their exact parameters
8. **CROSS-REFERENCE ALL COLUMN MAPPINGS** between source and target
9. **VERIFY CASE SENSITIVITY** for all column and table names
10. **NEVER MAKE ASSUMPTIONS** about what code "probably" does

### **🚨 CRITICAL MISTAKES TO AVOID**

**Column Name Assumptions:**
- ❌ NEVER assume column names without checking actual table schemas
- ❌ NEVER use `PrebookProviders` when the actual column is `PrebookSupplier`
- ❌ NEVER mix case (`PrebookChildAges` vs `Prebookchildages`) without verification
- ✅ ALWAYS verify column names in actual table definitions

**Stored Procedure Assumptions:**
- ❌ NEVER assume procedure names without checking Constants files
- ❌ NEVER assume `usp_Ins_Prebooklog_V1` populates `ReservationTable` (it populates `ReservationTablelog`)
- ✅ ALWAYS read the actual stored procedure to see what table it operates on

**Code Reference Assumptions:**
- ❌ NEVER copy-paste code without adapting ALL column references
- ❌ NEVER assume similar procedures have identical column names
- ✅ ALWAYS validate each column reference against the target context

### **📋 VERIFICATION CHECKLIST**

Before writing any code that references database objects:

- [ ] Read the complete target method/procedure line by line
- [ ] Verify all column names against actual table schemas
- [ ] Check all stored procedure names in Constants files
- [ ] Cross-reference all column mappings between source and target
- [ ] Validate case sensitivity for all database object names
- [ ] Test all assumptions against actual code/schema definitions

**Example of Critical Miss:** Assuming `usp_Ins_Prebooklog_V1` populates `ReservationTable` when it actually populates `ReservationTablelog`. The actual gateway procedure is `usp_Ins_Prebook_V1` which populates `ReservationTable`.

**Recent Critical Mistakes Fixed:**
- Using `PrebookProviders` instead of `PrebookSupplier` in stored procedures
- Mixing case `PrebookChildAges` vs `Prebookchildages` causing SQL errors
- Assuming column names without verifying against actual table schemas

### 🎯 Quick Context Primer (30-Second Understanding)

**System**: Hotel booking arbitrage platform that automatically finds better deals for existing client reservations
**Business Model**: 50% commission on savings achieved (client keeps 50%)
**Core Process**: Download reservations → Search for better rates → Rebook if profitable → Split savings
**Technology**: .NET Core API + SQL Server + MongoDB + IRIX booking API + Giata room mapping
**Revenue**: Only bill after guest checks in and all risks are eliminated (risk-free billing)

---

### 📋 Structured Information Hierarchy

#### **Level 1: Business Foundation**
- **Problem Solved**: Existing hotel reservations often have better rates available after booking
- **Value Proposition**: Automated re-optimization with zero client risk and guaranteed quality
- **Revenue Model**: 50% of realized savings (€70 savings = €35 platform revenue + €35 client savings)
- **Target Clients**: Hotels, travel agencies, booking platforms with reservation portfolios

#### **Level 2: System Architecture**
- **Data Pipeline**: Raw Data → Consolidation → Business Logic → API Access
- **Dual Optimization**: Same Supplier (automatic) vs Multi-Supplier (manual approval)
- **Risk Management**: Conservative billing only after guest check-in and cancellation deadlines pass
- **Configuration-Driven**: Extensive decision gates control optimization eligibility and execution

#### **Level 3: Technical Implementation**
- **Primary Database**: SQL Server with 3-layer stored procedure architecture
- **Secondary Database**: MongoDB for Giata room mapping data
- **Background Processing**: HangFire jobs for automated optimization workflows
- **External APIs**: IRIX (booking), Giata (room mapping), client systems (reservation download)

---

### 🗄️ Complete Database Object Definitions

#### **Core Tables with Relationships**

```sql
-- SOURCE OF TRUTH: All client reservations
ReservationMain (
    ReservationId INT PK,           -- Unique reservation identifier
    RepricerId INT FK,              -- Client identifier (links to RePricerDetail)
    ReservationStatus VARCHAR(50),  -- 'OK', 'Cancelled'
    CheckIn/CheckOut DATE,          -- Stay dates
    supplierName VARCHAR(100),      -- Original booking supplier
    cpJSON NVARCHAR(MAX),          -- Cancellation policy details
    Destinations, PaymentType, CriteriaJson -- Additional booking details
)

-- PROCESSED DATA: Optimization calculations and results
ReservationReportDetails (
    ReservationId INT FK → ReservationMain,
    RepricerId INT FK → RePricerDetail,
    ReservationPrice/PrebookPrice DECIMAL(18,4), -- Original vs optimized prices
    Profit DECIMAL(18,4),                        -- Savings amount
    cpstatus VARCHAR(50),                        -- 'loose', 'tight', 'CancellationChargesApplicable'
    CPDaysGain INT,                              -- Cancellation policy improvement days
    ActionTakenGain DECIMAL(18,4),               -- Realized profit (for invoicing)
    newReservationID INT,                        -- New booking reference
    matchedreservation/prebookcancellationdate  -- CP deadlines for risk management
)

-- BUSINESS INTELLIGENCE: Pre-computed dashboard data
tbl_vw_ResevationReports (
    ReportType VARCHAR(50),         -- 'Prebook', 'CancellationEdgeCase', 'Optimized', etc.
    ReservationPrice/PrebookPrice,  -- Financial data
    Profit/ProfitPercentage,        -- Savings calculations
    HotelName, Destinations,        -- Property details
    ReservationSupplier/PrebookSupplier, -- Supplier information
    OptimizationStatus              -- Current processing status
)

-- CLIENT CONFIGURATION: Core operational settings
RePricerDetail (
    RepricerUserID INT PK,
    IsActive/IsOptimization/IsJobsEnable BIT,    -- Master switches
    OptimizationType INT,                        -- 1=Manual, 2=Automatic
    IsMultiSupplierRoomSync BIT,                -- Cross-supplier optimization
    DelayBetween* INT,                          -- Rate limiting settings
    CPTimeAllowedBufferPercentage DECIMAL,      -- Cancellation policy buffers
    Restricted* NVARCHAR(MAX),                  -- Geographic/supplier restrictions
    AdminURL/ResellerURL,                       -- IRIX API endpoints
    AdminUserID/Password                        -- API credentials
)

-- BUSINESS RULES: Profit thresholds and time windows
clientconfiguration_ExtraCriteria (
    RePricerId INT FK → RePricerDetail,
    priceDifferenceValue/Percentage DECIMAL,    -- Profit thresholds
    isUsePercentage BIT,                       -- Threshold type selector
    traveldaysmin/maxsearchindays INT,         -- Time window restrictions
    MaxNumberOfTimesOptimization INT,          -- Attempt limits
    DaysLimitCancellationPolicyEdgeCase INT,   -- CP flexibility
    PriceDifferenceValueAllowedBufferPercentage, -- Enhanced controls
    IsCheckCrossSupperBeforeOptimization BIT
)

-- API CONTROLS: External service restrictions
IrixConfiguration (
    RepricerId INT FK → RePricerDetail,
    AllowedProvidersForOptimization VARCHAR(MAX), -- Supplier whitelist
    MinimumSupplierReservationPrice DECIMAL,      -- Value thresholds
    AllowNonRefundableReservations BIT,           -- Payment restrictions
    RejectPayNowReservations/OptimizedReservations BIT, -- Booking type filters
    MinimumOfferGainValue DECIMAL                 -- Gain requirements
)

-- ACTION TRACKING: Optimization execution history
BookingActionsTaken (
    RepricerId/ReservationId INT FK,
    ActionId INT,                    -- 1=Optimized, 2=Attempted, etc.
    NewBookingId INT,               -- New reservation reference
    ProfitAmount DECIMAL,           -- Actual savings realized
    OptimizationMethod VARCHAR,     -- 'SameSupplier', 'MultiSupplier'
    Status/ErrorMessage             -- Execution results
)
```

#### **Key Stored Procedures**

```sql
-- DATA PROCESSING PIPELINE (3-Layer Architecture)
usp_upd_reservationreport          -- Layer 1: Data consolidation, currency conversion
usp_ins_upd_tbl_vw_ResevationReports -- Layer 2: Business logic, report classification
usp_get_ResevationReports_V1       -- Layer 3: API access with caching

-- CONFIGURATION MANAGEMENT
usp_ins_RepricerClientConfiguration -- Client setup and updates
usp_GetRePricerDataByUserID        -- Load complete client configuration
usp_ins_configuration              -- IRIX API configuration

-- FINANCIAL PROCESSING
usp_Get_Invoice                    -- Generate 50% commission invoices
```

---

### 🔧 Business Logic Decision Trees

#### **Optimization Eligibility (5-Gate Process)**

```
Gate 1: Basic Eligibility
├── IsActive = 1 AND IsOptimization = 1 AND IsJobsEnable = 1
├── CheckIn within [traveldaysminsearchindays, traveldaysmaxsearchindays]
└── ReservationStatus = 'OK'

Gate 2: Profit Validation
├── IF isUsePercentage = 1: (Profit ÷ OriginalPrice) × 100 >= priceDifferencePercentage
├── IF isUsePercentage = 0: Profit >= priceDifferenceValue (currency converted)
└── Profit >= 5.0 EUR (global minimum)

Gate 3: Cancellation Policy Rules
├── cpstatus = 'loose' (preferred)
├── OR cpstatus = 'tightWithBuffer' AND CPTimeAllowedBufferPercentage > 0
├── CP deadline > current date + traveldaysminsearchindays
└── CP days gain >= -DaysLimitCancellationPolicyEdgeCase

Gate 4: Restrictions & Filters
├── Supplier in AllowedProvidersForOptimization
├── Supplier NOT in RestrictedSupplier
├── Hotel/City/Country NOT in restricted lists
├── Reservation value >= MinimumSupplierReservationPrice
└── Payment type allowed (if RejectPayNowReservations configured)

Gate 5: Execution Controls
├── OptimizationType = 2 (Automatic) OR manual trigger
├── Attempts < MaxNumberOfTimesOptimization
├── Rate limiting delays respected
└── Multi-supplier rules applied if IsMultiSupplierRoomSync enabled
```

#### **Report Type Classification Logic**

```sql
CASE
    WHEN ActionId = 1 THEN 'Optimized'                    -- Already processed
    WHEN cpstatus = 'loose'
         AND profit_meets_threshold
         AND cp_deadline_safe
         AND no_action_taken THEN 'Prebook'               -- Prime candidates
    WHEN cpstatus IN ('CancellationChargesApplicable') THEN 'CancellationChargesApplicable'
    WHEN cpstatus = 'loose'
         AND CPDaysGain >= -DaysLimitCancellationPolicyEdgeCase
         AND other_conditions THEN 'CancellationEdgeCase' -- Relaxed CP rules
    WHEN profit < threshold
         BUT other_favorable_conditions THEN 'PriceEdgeCase' -- Below threshold opportunities
    ELSE 'Other'                                          -- Not eligible
END
```

---

### ⚡ Dual Optimization Workflows

#### **Same Supplier Flow** (`Irix.Service/SearchService.cs`)
```csharp
// Key Method: _1_PrebookAndOptimize_SameSupplier_Automatic
Process:
1. Load reservations via GetReservationsAsync(repricerId)
2. Validate: reservationSupplier == prebookSupplier (exact match)
3. Search IRIX API with original supplier only
4. Direct room/board matching (exact tokens)
5. Price validation → Prebook creation → CP validation
6. Execute if: OptimizationType=Automatic + cpstatus='loose' + thresholds met
7. Record in BookingActionsTaken with OptimizationMethod='SameSupplier'

Characteristics: Lower risk, automatic execution, faster processing
```

#### **Multi-Supplier Flow** (`Irix.Service/SupplierSearchService.cs`)
```csharp
// Key Method: _1_PrebookAndOptimize_MultiSupplier
Process:
1. Check IsMultiSupplierRoomSync = 1
2. Load AllowedProvidersForOptimization, exclude original supplier
3. Process suppliers in batches of 5
4. Giata room mapping: _giataService.GiataApiCall() for cross-supplier equivalency
5. Enhanced validation: room groups, board compatibility
6. Dry run required: _dryRunOptimizationService._2_DryRunOptimizationApiIRIX()
7. Manual approval mandatory (isOptimizeTriggeredManually = true)
8. Record with OptimizationMethod='MultiSupplier'

Characteristics: Maximum savings, complex mapping, manual approval required
```

---

### 💰 Financial Model Implementation

#### **50% Commission Calculation**
```sql
-- Invoice generation (usp_Get_Invoice)
SELECT SUM(Profit) / 2.0 AS InvoiceableAmount  -- 50% commission
WHERE ActionTakenGain > 0                      -- Optimization completed
  AND newReservationID > 0                     -- New booking confirmed
  AND reservationcheckin < @currentDate        -- Guest checked in
  AND matchedreservationcancellationdate < @currentDate -- Cancellation risk passed
```

#### **Risk-Free Billing Logic**
```
Billing Triggers (ALL must be true):
✅ ActionTakenGain > 0 (optimization executed successfully)
✅ newReservationID > 0 (new booking confirmed)
✅ Guest check-in completed (eliminates no-show risk)
✅ Cancellation deadlines passed (eliminates cancellation risk)
✅ Profit realized and confirmed (eliminates execution risk)
```

---

### 🔌 Key API Endpoints & Usage

#### **GetRepricerReport** - Primary Dashboard API
```http
POST /api/Repricer/GetRepricerReport
{
  "RepricerId": 12,
  "ReportType": "Prebook|CancellationEdgeCase|Optimized|Other",
  "PageNumber": 1, "PageSize": 50
}

Response: { requestBody, pagesSummary, action[], overallSummary, data[] }
Data Source: usp_get_ResevationReports_V1 → tbl_vw_ResevationReports
Caching: Memory (5min) → Redis (6hr) → Database
```

#### **Optimization Execution**
```http
POST /api/Optimization/ExecuteOptimization
{
  "RepricerId": 12, "ReservationId": 12345,
  "OptimizationType": "SameSupplier|MultiSupplier",
  "IsManuallyTriggered": true
}

Triggers: SearchService._1_PrebookAndOptimize_SameSupplier_Automatic()
       OR SupplierSearchService._1_PrebookAndOptimize_MultiSupplier()
```

---

### 🚨 Common Use Cases & Troubleshooting

#### **Configuration Issues**
```
Problem: No optimizations appearing
Check: IsActive=1, IsOptimization=1, IsJobsEnable=1 in RePricerDetail
Check: traveldaysminsearchindays/maxsearchindays range includes target dates
Check: priceDifferenceValue/Percentage thresholds not too high

Problem: Multi-supplier not working
Check: IsMultiSupplierRoomSync=1 in RePricerDetail
Check: AllowedProvidersForOptimization contains multiple suppliers
Check: Giata room mapping data available for hotel/room combinations
```

#### **Profit Calculation Issues**
```
Problem: Profit calculations incorrect
Check: CurrencyFactorToEur values in ReservationReportDetails
Check: Exchange rate data current and accurate
Check: pricedifferencecurrency matches client's base currency
Verify: Profit = OriginalPrice - OptimizedPrice (after currency conversion)
```

#### **Testing Guidelines**
```
✅ Use RepricerId = 99 for testing (NEVER use RepricerId = 12 - production)
✅ Test both same supplier and multi-supplier flows
✅ Validate profit thresholds with percentage and fixed amount modes
✅ Test cancellation policy edge cases with buffer settings
✅ Verify invoicing calculations with completed optimization scenarios
```

---

### 🔄 COMPLETE STEP-BY-STEP DATA FLOW WITH ALL CRITICAL PROCEDURES

#### **📊 DETAILED SAME SUPPLIER OPTIMIZATION FLOW**

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 1: LOAD SOURCE DATA
-- ═══════════════════════════════════════════════════════════════════════════════
GetReservationsAsync(repricerId=12)
├── EXEC usp_get_CreateSearch @RePricerId=12
│   ├── READ: ReservationMain (source reservations)
│   ├── READ: Reservation_Supplier (supplier info)
│   ├── READ: SupplierMasterExclusion (excluded suppliers)
│   ├── READ: BookingActionsTaken (already optimized check)
│   └── READ: clientconfiguration_ExtraCriteria (business rules)
└── OUTPUT: List<ReservationMainModel> (eligible reservations)

GetReservationsRoomAsync(repricerId=12, reservationId=12345)
├── EXEC usp_get_RoomInfo @RePricerId=12, @ReservationId=12345
│   ├── READ: ReservationRoom (room details)
│   └── READ: ReservationMain (reservation context)
└── OUTPUT: List<ReservationRoomModel> (room configurations)

GetPreBookCriteria(reservationId=12345, repricerId=12)
├── EXEC usp_get_PreBookCriteria @ReservationId=12345, @RePricerId=12
│   ├── READ: ReservationPrice (financial data)
│   ├── READ: ReservationHotelInformation (hotel info)
│   ├── READ: ReservationRoom (room specs)
│   ├── READ: ReservationMain (reservation details)
│   └── COMPLEX CTE: Aggregate room counts and distinct names
└── OUTPUT: PreBookCriteriaResult (complete criteria for IRIX API)

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 2: IRIX API SEARCH AND PREBOOK
-- ═══════════════════════════════════════════════════════════════════════════════
IRIX SearchForBooking(reservationId=12345, criteria, repricerId=12)
├── External IRIX API call
├── Room/Board matching logic
├── Price comparison validation
└── OUTPUT: SearchResponseFromAPI (available offers)

IRIX PrebookResponse(selectedOffer, reservationId=12345, repricerId=12)
├── External IRIX API call
├── Temporary booking hold creation
├── Availability token generation
└── OUTPUT: PrebookResponseFromAPI (prebook confirmation + token)

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 3: VALIDATION AND QUALIFICATION
-- ═══════════════════════════════════════════════════════════════════════════════
Validation Logic (in SearchService)
├── Price threshold validation (IsPriceThreshold)
├── Cancellation policy comparison (CancellationCheck)
├── Supplier matching validation
├── Business rule compliance check
└── DECISION: Proceed to ReservationTable population OR reject

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 4: POPULATE RESERVATIONTABLE (CRITICAL GATEWAY)
-- ═══════════════════════════════════════════════════════════════════════════════
InsertPreBookReservation() → CRITICAL PROCEDURE (ACTUAL CODE ANALYSIS)
├── CONDITION: IF (isUpdateDB || isCurrentPrebookOptimized) → Lines 1085-1086 in SearchService
├── EXEC usp_Ins_Prebook_V1 ← THE ACTUAL CRITICAL GATEWAY PROCEDURE
│   ├── CALLED WITH PARAMETERS (from SearchService lines 1091-1108):
│   │   ├── prebookAvailabilityToken (from IRIX prebook response)
│   │   ├── reservationIdInLoop, repricerId
│   │   ├── searchsyncprice (PreBookPrice), profit, profitAfterCancellation
│   │   ├── prebookcount + 1 (attempt sequence number)
│   │   ├── cpResult (CancellationPolicyResult), roomResult (RoomResult)
│   │   ├── prebookjson (PreBookResponseJson), searchsyncjson
│   │   └── isCurrentPrebookOptimized (IsOptimized flag)
│   ├── INSERT INTO ReservationTable ← QUALIFIED CANDIDATES FOR REBOOKING
│   │   ├── ReservationId, RePricerId, PreBookPrice, Profit, ProfitAfterCancellation
│   │   ├── AvailabilityToken (critical for optimization execution)
│   │   ├── preBookCount (attempt sequence), cPStatus, createdate
│   │   └── IsOptimized (flag to prevent duplicate processing)
│   └── *** CRITICAL: Only populated AFTER successful IRIX prebook + validation ***
├── SEPARATE LOGGING: InsertPreBookReservationLog() → Lines 1180-1191 in SearchService
│   ├── CONDITION: Called in ELSE branch when (!isOptimized)
│   ├── EXEC usp_Ins_Prebooklog_V1 → INSERT INTO ReservationTablelog
│   └── PURPOSE: Audit trail for failed prebook attempts
└── OUTPUT: ReservationTable contains validated candidates ready for optimization

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 5: DRY RUN OPTIMIZATION (FEASIBILITY TEST)
-- ═══════════════════════════════════════════════════════════════════════════════
_2_DryRunOptimizationApiIRIX(optimizeBookingReq, reservationId=12345)
├── IRIX API dry run call
├── EXEC usp_ins_dryrunoptimization
│   ├── INSERT INTO DryRunOptimization
│   │   ├── RepricerId=12
│   │   ├── ReservationId=12345
│   │   ├── ExpectedGain=50.00
│   │   └── AvailabilityToken='ABC123...'
└── OUTPUT: DryRunResponse (feasibility confirmation)

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 6: OPTIMIZATION EXECUTION
-- ═══════════════════════════════════════════════════════════════════════════════
_3_OptimizeBookingApiIRIX(optimizeBookingReq, reservationId=12345)
├── IRIX API optimization call
├── EXEC usp_ins_optimizedbooking
│   ├── INSERT INTO OptimizationBooking
│   │   ├── RepricerId=12
│   │   ├── OldReservationId=12345
│   │   ├── NewReservationId=67890
│   │   ├── OptimizationGainValue=50.00
│   │   └── OptimizationStatus='SUCCESS'
├── EXEC usp_upd_BookingActionsTaken
│   ├── INSERT INTO BookingActionsTaken
│   │   ├── RepricerId=12
│   │   ├── ReservationId=12345
│   │   ├── ActionId=1 (Optimized)
│   │   ├── NewBookingId=67890
│   │   ├── ProfitAmount=50.00
│   │   └── OptimizationMethod='SameSupplier'
└── UPDATE ReservationMain SET ReservationStatus='Optimized'

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 7: REPORTING PIPELINE (3-LAYER ARCHITECTURE)
-- ═══════════════════════════════════════════════════════════════════════════════
RefreshDbAndCachedReport(repricerId=12, reservationId=12345)
├── LAYER 1: EXEC usp_upd_reservationreport @RepricerId=12
│   ├── READ: ReservationMain, BookingActionsTaken, ExchangeRateData
│   ├── COMPLEX LOGIC: Currency conversion, profit calculation, CP analysis
│   ├── INSERT/UPDATE: ReservationReportDetails
│   │   ├── ReservationId=12345
│   │   ├── ReservationPrice=500.00
│   │   ├── PrebookPrice=450.00
│   │   ├── Profit=50.00
│   │   ├── CPStatus='loose'
│   │   └── ActionTakenGain=50.00
│   └── OUTPUT: Consolidated optimization data
├── LAYER 2: EXEC usp_ins_upd_tbl_vw_ResevationReports @RepricerId=12
│   ├── READ: ReservationReportDetails
│   ├── COMPLEX LOGIC: Business rule application, report classification
│   ├── INSERT INTO tbl_vw_ResevationReports
│   │   ├── RepricerId=12
│   │   ├── ReservationId=12345
│   │   ├── ReportType='Optimized'
│   │   ├── Profit=50.00
│   │   ├── ProfitPercentage=10.0
│   │   └── OptimizationStatus='Completed'
│   └── OUTPUT: Business intelligence data
└── LAYER 3: Cache refresh for usp_get_ResevationReports_V1
    ├── Memory cache invalidation
    ├── Redis cache refresh
    └── Prepared for API access

-- ═══════════════════════════════════════════════════════════════════════════════
-- STEP 8: API ACCESS AND INVOICING
-- ═══════════════════════════════════════════════════════════════════════════════
GetRepricerReport API Call
├── EXEC usp_get_ResevationReports_V1 @RepricerId=12, @ReportType='Optimized'
│   ├── READ: tbl_vw_ResevationReports
│   ├── COMPLEX LOGIC: Filtering, pagination, sorting
│   └── OUTPUT: Dashboard data
└── Display: Optimization results to client

Monthly Invoicing
├── EXEC usp_Get_Invoice @RepricerId=12, @Month='2024-03'
│   ├── READ: BookingActionsTaken (ActionId=1, completed optimizations)
│   ├── READ: ReservationReportDetails (ActionTakenGain > 0)
│   ├── FILTER: Guest checked in AND cancellation deadlines passed
│   ├── CALCULATE: SUM(ActionTakenGain) / 2.0 (50% commission)
│   └── OUTPUT: Invoice amount for risk-free billing
```

#### **🎯 CRITICAL GATEWAY PROCEDURES SUMMARY (ACTUAL CODE ANALYSIS)**

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- CRITICAL PROCEDURE #1: RESERVATIONTABLE POPULATION (GATEWAY TO OPTIMIZATION)
-- ═══════════════════════════════════════════════════════════════════════════════
usp_Ins_Prebook_V1 → THE ACTUAL CRITICAL GATEWAY PROCEDURE
├── CALLED BY: _reservationPersistance.InsertPreBookReservation() in SearchService line 1091
├── CONDITION: IF (isUpdateDB || isCurrentPrebookOptimized) → Only called when updating DB or optimization occurred
├── ACTUAL TABLE POPULATED: ReservationTable (confirmed by reading usp_Ins_Prebook_V1.sql)
├── CRITICAL FIELDS POPULATED (from actual stored procedure):
│   ├── ReservationId, RePricerId, PreBookPrice, Profit, ProfitAfterCancellation
│   ├── AvailabilityToken (critical for optimization execution)
│   ├── preBookCount (attempt sequence number)
│   ├── cPStatus ('loose', 'tight', 'CancellationChargesApplicable')
│   ├── IsOptimized (flag passed as @IsOptimized parameter)
│   └── createdate (GETUTCDATE())
├── CRITICAL ROLE: Acts as the gateway between search/validation and optimization execution
└── WITHOUT THIS: No reservations can proceed to optimization (ReservationTable remains empty)

usp_Ins_Prebooklog_V1 → SEPARATE LOGGING PROCEDURE
├── CALLED BY: _reservationPersistance.InsertPreBookReservationLog() in SearchService line 1180
├── CONDITION: Called in ELSE branch when (!isOptimized) for failed prebook attempts
├── ACTUAL TABLE POPULATED: ReservationTablelog (confirmed by reading usp_Ins_Prebooklog_V1.sql)
├── PURPOSE: Detailed logging and audit trail (NOT the optimization gateway)
└── RELATIONSHIP: Parallel logging for failed attempts, separate from main ReservationTable population

-- ═══════════════════════════════════════════════════════════════════════════════
-- CRITICAL PROCEDURE #2: BOOKINGACTIONSTAKEN POPULATION (FINAL RESULTS)
-- ═══════════════════════════════════════════════════════════════════════════════
usp_upd_BookingActionsTaken → THE ACTUAL FINAL RESULTS PROCEDURE
├── ACTUAL TABLE POPULATED: BookingActionsTaken
├── ONLY CALLED AFTER: Successful IRIX optimization execution
├── KEY LOGIC:
│   ├── IF ActionId = 1 (Optimized) → Records successful optimization
│   ├── IF ActionId = 4 → Records "Cannot See Price/No More Rooms"
│   └── Prevents duplicates: WHERE ISNULL(NewBookingId, 0) > 0
├── CRITICAL FIELDS POPULATED:
│   ├── RepricerId, ReservationId, NewBookingId, ActionId
│   ├── NewBookingPrice, GainAmountInOriginalCurrency
│   ├── createdOn (timestamp), token (operation details)
│   └── ResponseBody (IRIX API response)
├── AUTOMATIC TRIGGERS: When ActionId = 1, automatically executes:
│   ├── EXEC usp_upd_reservationreport (data consolidation)
│   ├── EXEC usp_ins_upd_tbl_vw_ResevationReports (business logic)
│   ├── EXEC usp_Insert_DailyOptimizationReport (daily reports)
│   └── EXEC usp_get_ReservationReportCalc (caching refresh)
├── CRITICAL ROLE: Records final optimization results AND triggers reporting pipeline
└── WITHOUT THIS: No optimization results recorded (no invoicing, no reporting)

usp_upd_ReservationTable → ALTERNATIVE BOOKINGACTIONSTAKEN PROCEDURE
├── SIMPLER VERSION: Basic INSERT into BookingActionsTaken
├── USED BY: Manual operations or specific workflows
└── RELATIONSHIP: Alternative to usp_upd_BookingActionsTaken for simpler cases

-- ═══════════════════════════════════════════════════════════════════════════════
-- MULTI-SUPPLIER SPECIFIC PROCEDURES (ACTUAL CODE ANALYSIS)
-- ═══════════════════════════════════════════════════════════════════════════════
usp_Ins_Prebook_Supplier_V1 → MULTI-SUPPLIER GATEWAY PROCEDURE
├── CALLED BY: _reservationPersistance.InsertPreBookReservation() in SupplierSearchService line 1481
├── CONDITION: IF (isUpdateDB || isCurrentPrebookOptimized) → Lines 1452-1453 in SupplierSearchService
├── ACTUAL TABLE POPULATED: ReservationTableSupplier (multi-supplier candidates table)
├── CRITICAL DIFFERENCE: Uses SAME method name but DIFFERENT stored procedure than same-supplier
├── CONSTANT: PersistanceConstant.InsertPreBookReservationSupplier = "usp_Ins_Prebook_Supplier_V1"
├── CRITICAL ROLE: Gateway for multi-supplier optimization candidates
└── WITHOUT THIS: No multi-supplier reservations can proceed to optimization

usp_Ins_PrebookSupplierlog_V1 → MULTI-SUPPLIER LOGGING PROCEDURE
├── CALLED BY: _reservationPersistance.InsertPreBookReservationLog() in SupplierSearchService line 1564
├── CONDITION: Called in ELSE branch when (!isOptimized || !isPrebookCreated) → Lines 1557-1558
├── ACTUAL TABLE POPULATED: ReservationTableSupplierlog (multi-supplier audit/logging table)
├── CONSTANT: PersistanceConstant.InsertPreBookReservationlogSupplier = "usp_Ins_PrebookSupplierlog_V1"
├── PURPOSE: Detailed logging and audit trail for multi-supplier operations
└── RELATIONSHIP: Parallel logging for failed multi-supplier attempts

-- ═══════════════════════════════════════════════════════════════════════════════
-- CRITICAL SEQUENCE DEPENDENCIES
-- ═══════════════════════════════════════════════════════════════════════════════
MANDATORY SEQUENCE (ACTUAL CODE ANALYSIS):

**SAME-SUPPLIER FLOW (_1_PrebookAndOptimize_SameSupplier_Automatic):**
1. IRIX Search + Prebook → Success
2. Validation (Price, CP, Business Rules) → Pass
3. InsertPreBookReservation() → EXEC usp_Ins_Prebook_V1 → Populates ReservationTable ← GATEWAY #1
   ├── CONDITION: IF (isUpdateDB || isCurrentPrebookOptimized) from SearchService line 1085
   ├── PARAMETERS: All prebook data including isCurrentPrebookOptimized flag
   ├── CONSTANT: PersistanceConstant.InsertPreBookReservation = "usp_Ins_Prebook_V1"
   └── CRITICAL: Only called when DB update needed or optimization occurred
4. Dry Run (optional) → Feasibility check
5. IRIX Optimization → Success
6. usp_upd_BookingActionsTaken → Populates BookingActionsTaken ← GATEWAY #2
7. Reporting Pipeline → Data consolidation and business intelligence (AUTOMATIC)

**MULTI-SUPPLIER FLOW (_1_PrebookAndOptimize_MultiSupplier):**
1. IRIX Search + Prebook → Success (across multiple suppliers)
2. Validation (Price, CP, Business Rules) → Pass
3. InsertPreBookReservation() → EXEC usp_Ins_Prebook_Supplier_V1 → Populates ReservationTableSupplier ← GATEWAY #1
   ├── CONDITION: IF (isUpdateDB || isCurrentPrebookOptimized) from SupplierSearchService line 1452
   ├── PARAMETERS: All prebook data including isCurrentPrebookOptimized flag
   ├── CONSTANT: PersistanceConstant.InsertPreBookReservationSupplier = "usp_Ins_Prebook_Supplier_V1"
   └── CRITICAL: Uses SAME method name but DIFFERENT stored procedure than same-supplier
4. Dry Run (optional) → Feasibility check
5. IRIX Optimization → Success
6. usp_upd_BookingActionsTaken → Populates BookingActionsTaken ← GATEWAY #2 (SHARED)
7. Reporting Pipeline → Data consolidation and business intelligence (AUTOMATIC)

CRITICAL FAILURE POINTS (ACTUAL CODE PATHS):
├── If Step 3 fails: ReservationTable/ReservationTableSupplier empty → No optimization candidates
├── If Step 6 fails: BookingActionsTaken empty → No results recorded
├── If either fails: Complete optimization workflow breaks
├── PARALLEL LOGGING:
│   ├── Same-Supplier: InsertPreBookReservationLog() → usp_Ins_Prebooklog_V1 → ReservationTablelog
│   └── Multi-Supplier: InsertPreBookReservationLog() → usp_Ins_PrebookSupplierlog_V1 → ReservationTableSupplierlog
└── AUTOMATIC TRIGGERS: Step 6 automatically executes reporting pipeline when ActionId=1
```

---

### 📊 Configuration Parameters Impact Matrix

#### **Client Behavior Control**
```
OptimizationType:
├── 1 (Manual): All optimizations require manual approval
├── 2 (Automatic): Same supplier optimizations execute automatically
└── Multi-supplier always requires manual approval regardless

IsMultiSupplierRoomSync:
├── 0: Only same supplier optimization available
└── 1: Both same supplier and multi-supplier optimization enabled

Profit Thresholds:
├── isUsePercentage=1: Use priceDifferencePercentage (e.g., 10% minimum savings)
├── isUsePercentage=0: Use priceDifferenceValue (e.g., €5 minimum savings)
└── Global minimum: 5.0 EUR regardless of configuration

Time Windows:
├── traveldaysminsearchindays: Minimum lead time (default 10 days)
├── traveldaysmaxsearchindays: Maximum advance booking (default 60 days)
└── CheckIn must fall within this window for eligibility

Rate Limiting:
├── DelayBetweenOptimizationJob: Seconds between optimization batches
├── DelayBetweenRequestsSameSupplier: API call throttling (min 10s, default 15s)
└── DelayBetweenRequestsMultiSupplier: Multi-supplier throttling (min 10s, default 20s)
```

#### **Risk Management Controls**
```
Cancellation Policy Buffers:
├── CPTimeAllowedBufferPercentage: Allow slightly worse CP times
├── CPAmountAllowedBufferPercentage: Allow slightly worse CP amounts
├── DaysLimitCancellationPolicyEdgeCase: CP deterioration tolerance (default 5 days)
└── IsUseDaysLimitCancellationPolicyEdgeCase: Enable/disable CP flexibility

Geographic Restrictions:
├── RestrictedHotelId: Comma-separated hotel IDs to exclude
├── RestrictedCityId: Comma-separated city IDs to exclude
├── RestrictedCountryId: Comma-separated country IDs to exclude
└── RestrictedSupplier: Comma-separated supplier names to exclude

Supplier Controls:
├── AllowedProvidersForOptimization: Whitelist of allowed suppliers
├── MinimumSupplierReservationPrice: Minimum booking value threshold
└── RejectPayNowReservations: Block immediate payment bookings
```

---

### 🔍 Advanced Troubleshooting Scenarios

#### **Performance Issues**
```
Slow API Response:
├── Check cache hit rates (Memory → Redis → Database)
├── Verify database indexes on RepricerId, ReportType, CheckIn
├── Monitor usp_get_ResevationReports_V1 execution time
└── Review parallel processing settings (MaxDegreeOfParallelism)

High Memory Usage:
├── Check for memory leaks in SearchService parallel processing
├── Verify proper disposal of IRIX API connections
├── Monitor HangFire job queue sizes
└── Review Redis cache expiration settings

Database Deadlocks:
├── Check concurrent access to ReservationReportDetails
├── Review transaction isolation levels in stored procedures
├── Monitor BookingActionsTaken insert conflicts
└── Verify proper indexing on frequently updated tables
```

#### **Data Consistency Issues**
```
Missing Optimizations:
├── Verify usp_upd_reservationreport completed successfully
├── Check ReservationMain → ReservationReportDetails data flow
├── Validate currency conversion factors (CurrencyFactorToEur)
└── Confirm business logic in usp_ins_upd_tbl_vw_ResevationReports

Incorrect Profit Calculations:
├── Trace profit calculation through 3-layer pipeline
├── Verify exchange rate data freshness and accuracy
├── Check for rounding errors in DECIMAL precision
└── Validate currency conversion at each processing stage

Invoicing Discrepancies:
├── Confirm ActionTakenGain values match actual optimizations
├── Verify guest check-in dates and cancellation deadlines
├── Check for duplicate entries in BookingActionsTaken
└── Validate 50% commission calculation in usp_Get_Invoice
```

---

### 🛠️ Development Patterns & Best Practices

#### **Code References for Common Tasks**

```csharp
// Profit Threshold Validation
SearchServiceHelper.IsPriceThreshold(originalPrice, profitAmount, rePricerDetail, preBookCriteriaResult)

// Cancellation Policy Comparison
SearchServiceHelper.CancellationCheck(repricerId, cancellationPolicyByReservation, prebookCancellationPolicy, prebookCriteriaResult, prebookPrice, reservation)

// Room Matching (Same Supplier)
UpdateMatchingRoomOffer(mainRoomInfoList, matchedRoomInfoList, offerInfo, prebookCriteria, selectedRoomTokens, ref packageInfo, repricerId, reservationId)

// Cross-Supplier Room Mapping (Multi-Supplier)
_giataService.GiataApiCall(repricerId, reservationId, hotelName, roomName, destinations, supplierName)

// Configuration Loading
_clientServices.GetRePricerDetail(repricerId)  // Core configuration
_clientServices.GetClientEmail(repricerId)     // Business rules
_reservationPersistance.GetAllowedProviders(repricerId)  // Supplier whitelist

// Optimization Execution
_optimizationService._3_OptimizeBookingApiIRIX(optimizeBookingReq, reservationId, isCacheRefresh, isMultiSupplier)
_dryRunOptimizationService._2_DryRunOptimizationApiIRIX(optimizeBookingReq, reservationId, isUpdateDB, isCacheRefresh, isMultiSupplier)
```

#### **Database Query Patterns**

```sql
-- Load Eligible Reservations
EXEC usp_get_ResevationReports_V1 @RepricerId = 12, @ReportType = 'Prebook'

-- Check Optimization Status
SELECT ActionId, Status FROM BookingActionsTaken
WHERE RepricerId = @RepricerId AND ReservationId = @ReservationId

-- Validate Configuration
SELECT IsActive, IsOptimization, OptimizationType, IsMultiSupplierRoomSync
FROM RePricerDetail WHERE RepricerUserID = @RepricerId

-- Calculate Profit Thresholds
SELECT
    CASE WHEN isUsePercentage = 1
         THEN (Profit / ReservationPrice) * 100 >= priceDifferencePercentage
         ELSE Profit >= priceDifferenceValue * CurrencyFactor
    END AS MeetsThreshold
FROM ReservationReportDetails rrd
JOIN clientconfiguration_ExtraCriteria cce ON rrd.RepricerId = cce.RePricerId
```

---

### 🎯 Business Rules Quick Reference

#### **Report Type Priorities**
```
1. Optimized (ActionId = 1) - Highest priority, already processed
2. Prebook - Prime candidates meeting all criteria
3. CancellationEdgeCase - Relaxed CP rules, good opportunities
4. CancellationChargesApplicable - CP charges but still profitable
5. PriceEdgeCase - Below threshold but other favorable conditions
6. Other - Not eligible for optimization
```

#### **Cancellation Policy Status Hierarchy**
```
'loose' - Best case, same or better CP (preferred for automatic execution)
'tightWithBuffer' - Slightly worse but within buffer tolerance
'CancellationChargesApplicable' - Charges apply but profit remains positive
'tight' - Worse CP, requires manual review
'blocked' - CP too restrictive, optimization blocked
```

#### **Currency Handling Rules**
```
Base Currency: EUR (all profit calculations normalized to EUR)
Conversion: Applied at each processing layer using CurrencyFactorToEur
Precision: DECIMAL(18,4) for prices, DECIMAL(18,6) for exchange rates
Rounding: Applied consistently using SearchServiceHelper.RoundToDecimalPlaces()
```

---

### 🔄 Integration Points & External Dependencies

#### **IRIX API Integration**
```
Search: Find available rooms and rates for specific criteria
Prebook: Create temporary booking holds with availability tokens
Optimize: Execute final booking optimization with confirmed pricing
Status: Check optimization eligibility and restrictions
Rate Limiting: Configured per client with DelayBetween* settings
```

#### **Giata Service Integration**
```
Room Mapping: Cross-supplier room equivalency for multi-supplier optimization
Board Mapping: Meal plan compatibility across different suppliers
Hotel Mapping: Property identification and standardization
Caching: Results cached to improve performance and reduce API calls
```

#### **Client System Integration**
```
Reservation Download: Scheduled jobs pull new/updated reservations
Data Format: JSON/XML feeds processed into ReservationMain table
Validation: Data integrity checks and error handling
Frequency: Configurable per client (DelayBetweenDailyDownloadReservation)
```

---

### 📈 Monitoring & Alerting Key Metrics

#### **Business Metrics**
```
Optimization Success Rate: % of eligible reservations successfully optimized
Average Savings Per Optimization: Mean profit generated per successful optimization
Commission Revenue: Monthly invoiceable amounts per client (50% of savings)
Processing Time: End-to-end time from reservation to optimization completion
Client Satisfaction: Net savings delivered vs expectations
```

#### **Technical Metrics**
```
API Response Times: GetRepricerReport endpoint performance (<2s target)
Cache Hit Rates: Memory (>80%) and Redis (>60%) cache effectiveness
Database Performance: Stored procedure execution times (<5s for reports)
Error Rates: Failed optimizations and system errors (<5% target)
Background Job Health: HangFire job success rates and queue depths
```

#### **Alert Conditions**
```
Critical: API response time >5s, Database deadlocks, Failed optimizations >10%
Warning: Cache hit rate <70%, Background job delays >1hr, Memory usage >80%
Info: New client onboarding, Configuration changes, Monthly invoice generation
```

---

### 🔄 COMPLETE DATABASE OBJECT INVENTORY: Both Optimization Methods

#### **📊 COMPREHENSIVE TABLE ANALYSIS**

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- CORE DATA TABLES (Source of Truth)
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationMain                    -- Primary reservation data from client systems
├── Used by: usp_get_CreateSearch, usp_get_PreBookCriteria, usp_upd_reservationreport
├── Columns: ReservationId, RepricerId, ReservationStatus, CheckIn, CheckOut, supplierName, cpJSON
└── Purpose: Source of truth for all client reservations

ReservationRoom                    -- Room configuration details
├── Used by: usp_get_RoomInfo, usp_get_PreBookCriteria
├── Columns: ReservationId, RoomName, RoomBoard, PassengerCount, ChildAges, RoomInfo
└── Purpose: Room specifications for matching and validation

ReservationHotelInformation        -- Hotel property details
├── Used by: usp_get_PreBookCriteria, usp_upd_reservationreport
├── Columns: ReservationId, HotelId, HotelName, Destinations
└── Purpose: Property identification and location data

ReservationPrice                   -- Financial data for reservations
├── Used by: usp_get_PreBookCriteria, usp_upd_reservationreport
├── Columns: ReservationId, Currency, Total_Selling, Issue_Net
└── Purpose: Original pricing and currency information

Reservation_Supplier               -- Supplier relationship data
├── Used by: usp_get_CreateSearch, usp_upd_reservationreport
├── Columns: ReservationId, Supplier_System, SupplierName
└── Purpose: Original booking supplier identification

ReservationCancellationPolicy     -- Cancellation terms and conditions
├── Used by: usp_get_CancellationPolicy, usp_get_PreBookCriteria
├── Columns: ReservationId, CancellationDate, CancellationCharge, CancellationType
└── Purpose: Original booking cancellation policy details

-- ═══════════════════════════════════════════════════════════════════════════════
-- PROCESSING TABLES (Optimization Workflow)
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationTable                   -- QUALIFIED CANDIDATES FOR REBOOKING
├── Used by: usp_Ins_Prebooklog_V1, usp_get_prebookreservation
├── Columns: ReservationId, PreBookPrice, Profit, CPStatus, AvailabilityToken, PreBookCount
├── Purpose: Stores validated prebook attempts ready for optimization
└── CRITICAL: Only populated AFTER successful IRIX prebook + validation

PreBook_CancellationPolicy         -- Prebook cancellation policy analysis
├── Used by: usp_Ins_Prebooklog_V1
├── Columns: ReservationId, PrebookCancellationDate, PrebookCancellationCharge
└── Purpose: Cancellation policy comparison results

PreBook_PackageRooms               -- Prebook room package details
├── Used by: usp_Ins_Prebooklog_V1
├── Columns: ReservationId, RoomTokens, PackageInfo, RoomDetails
└── Purpose: Room matching and package information

PreBook_ClientConfiguration        -- Applied client configuration
├── Used by: usp_Ins_Prebooklog_V1
├── Columns: ReservationId, ConfigurationUsed, ThresholdApplied
└── Purpose: Track which business rules were applied

BookingActionsTaken                -- FINAL OPTIMIZATION RESULTS
├── Used by: usp_upd_BookingActionsTaken, usp_upd_reservationreport
├── Columns: RepricerId, ReservationId, ActionId, NewBookingId, ProfitAmount, OptimizationMethod
├── Purpose: Record of all optimization attempts and results
└── CRITICAL: Source of truth for completed optimizations

-- ═══════════════════════════════════════════════════════════════════════════════
-- MULTI-SUPPLIER SPECIFIC TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

MultiSupplierSearchRoom            -- Cross-supplier search results
├── Used by: usp_ins_multiSupplierRooms
├── Columns: RepricerId, ReservationId, OldSupplier, NewSupplier, RoomName, RoomBoard
└── Purpose: Store search results across multiple suppliers

MultiSupplierReservationRoom       -- Room mapping relationships
├── Used by: usp_ins_multiSupplierRooms
├── Columns: ReservationId, OriginalRoomId, MappedRoomId, MappingConfidence
└── Purpose: Track room equivalency mappings

MultiSupplierRoomMapping           -- Giata mapping cache
├── Used by: usp_ins_roommapping
├── Columns: HotelId, OriginalRoomName, MappedRoomName, SupplierFrom, SupplierTo
└── Purpose: Cache Giata room mapping results

PreBookSupplierLog                 -- Enhanced multi-supplier logging
├── Used by: usp_Ins_PrebookSupplierlog_V1
├── Columns: ReservationId, SupplierName, SearchResults, MappingResults
└── Purpose: Detailed logging for multi-supplier operations

-- ═══════════════════════════════════════════════════════════════════════════════
-- OPTIMIZATION TRACKING TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

DryRunOptimization                 -- Dry run test results
├── Used by: usp_ins_dryrunoptimization
├── Columns: RepricerId, ReservationId, ExpectedGain, AvailabilityToken
└── Purpose: Store dry run optimization test results

OptimizationBooking                -- Optimization execution details
├── Used by: usp_ins_optimizedbooking
├── Columns: RepricerId, OldReservationId, NewReservationId, OptimizationGainValue
└── Purpose: Track optimization execution and results

OptimizationReservationStatus      -- Optimization eligibility status
├── Used by: usp_ins_optimizationstatus, usp_get_Optimization
├── Columns: RepricerId, ReservationId, IsOptimizable
└── Purpose: Track which reservations are eligible for optimization

PrevOptimization                   -- Previous optimization attempts
├── Used by: usp_ins_prevoptimization
├── Columns: RepricerId, ReservationId, AttemptCount, LastAttemptDate
└── Purpose: Track optimization attempt history

-- ═══════════════════════════════════════════════════════════════════════════════
-- REPORTING AND BUSINESS INTELLIGENCE TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationReportDetails           -- Consolidated optimization data
├── Used by: usp_upd_reservationreport
├── Columns: ReservationId, ReservationPrice, PrebookPrice, Profit, CPStatus, ActionTakenGain
├── Purpose: Layer 1 - Data consolidation with currency conversion
└── CRITICAL: Bridge between raw data and business intelligence

tbl_vw_ResevationReports           -- Materialized business intelligence
├── Used by: usp_ins_upd_tbl_vw_ResevationReports, usp_get_ResevationReports_V1
├── Columns: RepricerId, ReservationId, ReportType, Profit, ProfitPercentage, OptimizationStatus
├── Purpose: Layer 2 - Business logic application and report classification
└── CRITICAL: Primary data source for GetRepricerReport API

-- ═══════════════════════════════════════════════════════════════════════════════
-- CONFIGURATION TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

RePricerDetail                     -- Core client configuration
├── Used by: LoadRePricerDetail, usp_GetRePricerDataByUserID
├── Columns: RepricerUserID, IsActive, OptimizationType, IsMultiSupplierRoomSync
└── Purpose: Master client settings and operational controls

clientconfiguration_ExtraCriteria  -- Business rules and thresholds
├── Used by: usp_get_CreateSearch, usp_get_prebookreservation
├── Columns: RePricerId, priceDifferenceValue, priceDifferencePercentage, traveldaysmin/maxsearchindays
└── Purpose: Profit thresholds and time window restrictions

IrixConfiguration                  -- API-level controls
├── Used by: GetAllowedProviders
├── Columns: RepricerId, AllowedProvidersForOptimization, MinimumSupplierReservationPrice
└── Purpose: Supplier whitelists and API restrictions

ExchangeRateData                   -- Currency conversion rates
├── Used by: usp_upd_reservationreport
├── Columns: FromCurrency, ToCurrency, ExchangeRate, EffectiveDate
└── Purpose: Currency conversion for profit calculations

-- ═══════════════════════════════════════════════════════════════════════════════
-- SEARCH SYNCHRONIZATION TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

SearchSync_Hotel                   -- Search result caching - hotel level
├── Used by: InsertSerachSync
├── Columns: RepricerId, ReservationId, HotelData, SearchTimestamp
└── Purpose: Cache IRIX search results for performance

SearchSync_Offer                   -- Search result caching - offer level
├── Used by: InsertSerachSync
├── Columns: HotelId, OfferData, PriceData, AvailabilityData
└── Purpose: Cache individual offer details

SearchSync_Room                    -- Search result caching - room level
├── Used by: InsertSerachSync
├── Columns: OfferId, RoomData, RoomTokens, BoardData
└── Purpose: Cache room-specific search results

SupplierSearchSync_Hotel           -- Multi-supplier search caching - hotel level
├── Used by: SupplierInsertSerachSync
├── Columns: RepricerId, ReservationId, SupplierName, HotelData
└── Purpose: Cache multi-supplier search results

SupplierSearchSync_Offer           -- Multi-supplier search caching - offer level
├── Used by: SupplierInsertSerachSync
├── Columns: HotelId, SupplierName, OfferData, PriceData
└── Purpose: Cache multi-supplier offer details

-- ═══════════════════════════════════════════════════════════════════════════════
-- LOGGING AND AUDIT TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

ReservationTableLog                -- Audit trail for ReservationTable changes
├── Used by: Audit triggers
├── Columns: ReservationId, ChangeType, OldValues, NewValues, ChangeDate
└── Purpose: Track all changes to qualified candidates

PreBookLog                         -- Detailed prebook operation logging
├── Used by: usp_Ins_Prebooklog_V1
├── Columns: ReservationId, PrebookAttempt, Results, Timestamps
└── Purpose: Comprehensive prebook operation audit trail

RepricerJobLogging                 -- Background job execution logging
├── Used by: usp_insert_repricerjoblogging
├── Columns: RepricerId, JobType, StartTime, EndTime, Status, ErrorDetails
└── Purpose: Track HangFire job execution and performance

-- ═══════════════════════════════════════════════════════════════════════════════
-- MASTER DATA TABLES
-- ═══════════════════════════════════════════════════════════════════════════════

SupplierMaster                     -- Master supplier configuration
├── Used by: usp_get_SupplierMaster
├── Columns: SupplierId, SupplierName, SupplierCode, IsActive
└── Purpose: Central supplier registry and configuration

SupplierMasterExclusion            -- Supplier exclusion rules
├── Used by: usp_get_CreateSearch
├── Columns: RepricerId, SupplierName, ExclusionReason, IsActive
└── Purpose: Client-specific supplier exclusions

ResellerMaster                     -- Master reseller configuration
├── Used by: usp_get_ResellerMaster
├── Columns: ResellerId, ResellerName, ResellerCode, IsActive
└── Purpose: Central reseller registry and configuration
```

#### **📋 COMPREHENSIVE STORED PROCEDURE ANALYSIS**

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- CORE DATA RETRIEVAL PROCEDURES (READ OPERATIONS)
-- ═══════════════════════════════════════════════════════════════════════════════

usp_get_CreateSearch               -- Load eligible reservations for optimization
├── Tables: ReservationMain, Reservation_Supplier, SupplierMasterExclusion, BookingActionsTaken
├── Joins: Complex multi-table joins with business rule filtering
├── Purpose: Get reservations that meet client criteria for optimization
└── Used by: GetReservationsAsync() in SearchService

usp_get_CreateSearch_V0            -- Mock/disconnected version of CreateSearch
├── Tables: Same as usp_get_CreateSearch but with mock data handling
├── Purpose: Testing and development environment support
└── Used by: GetReservationsAsync() when _isMock = true

usp_get_RoomInfo                   -- Load room configuration details
├── Tables: ReservationRoom, ReservationMain
├── Purpose: Get room specifications for matching and validation
└── Used by: GetReservationsRoomAsync() in SearchService

usp_get_PreBookCriteria            -- Load comprehensive prebook criteria
├── Tables: ReservationPrice, ReservationHotelInformation, ReservationRoom, ReservationMain
├── Complex CTE: Aggregates room counts and distinct room names
├── Purpose: Build complete criteria for IRIX API prebook calls
└── Used by: GetPreBookCriteria() in both optimization flows

usp_get_PreBookCriteria_V0         -- Alternative version of PreBookCriteria
├── Tables: Same as usp_get_PreBookCriteria with different logic
├── Purpose: Alternative criteria building logic
└── Used by: GetPreBookCriteria() with different parameters

usp_get_prebookreservation         -- Load existing prebook attempts
├── Tables: ReservationTable, ReservationMain, clientconfiguration_ExtraCriteria
├── Filters: Active reservations with future check-in dates
├── Purpose: Get reservations that already have prebook attempts
└── Used by: GetPrebookReservationIdsAsync() in SearchService

usp_get_CancellationPolicy         -- Load cancellation policy details
├── Tables: ReservationCancellationPolicy, ReservationMain
├── Purpose: Get original booking cancellation terms for comparison
└── Used by: GetCancellationPolicyReservationIdsAsync() in SearchService

usp_get_CancellationPolicyV0       -- Alternative cancellation policy procedure
├── Tables: Same as usp_get_CancellationPolicy with different logic
├── Purpose: Alternative CP retrieval logic
└── Used by: GetCancellationPolicyReservationIdsAsync() with different parameters

-- ═══════════════════════════════════════════════════════════════════════════════
-- PREBOOK AND OPTIMIZATION PROCEDURES (WRITE OPERATIONS)
-- ═══════════════════════════════════════════════════════════════════════════════

usp_Ins_Prebooklog_V1              -- Insert qualified prebook candidates
├── Tables: ReservationTable, PreBook_CancellationPolicy, PreBook_PackageRooms, PreBook_ClientConfiguration
├── Complex Logic: Multi-table insert with validation and business rules
├── Purpose: Store validated prebook attempts ready for optimization
└── Used by: InsertPreBookReservation() - CRITICAL GATEWAY TO OPTIMIZATION

usp_Ins_PrebookSupplierlog_V1      -- Insert multi-supplier prebook logs
├── Tables: PreBookSupplierLog, related multi-supplier tables
├── Purpose: Enhanced logging for multi-supplier prebook attempts
└── Used by: InsertIntoPrebookSupplierlog() in multi-supplier flow

usp_ins_dryrunoptimization         -- Insert dry run test results
├── Tables: DryRunOptimization
├── Purpose: Store dry run optimization test results
└── Used by: InsertDryRunOptimization() in DryRunOptimizationService

usp_ins_optimizedbooking           -- Insert optimization execution results
├── Tables: OptimizationBooking
├── Purpose: Record optimization execution details and results
└── Used by: InsertOptimizedBooking() in OptimizationService

usp_ins_optimizationstatus         -- Upsert optimization eligibility status
├── Tables: OptimizationReservationStatus
├── MERGE Logic: Upsert operation for optimization status tracking
├── Purpose: Track which reservations are eligible for optimization
└── Used by: InsertOptimizationStatus() in OptimizationService

usp_ins_prevoptimization           -- Insert previous optimization attempts
├── Tables: PrevOptimization
├── Purpose: Track optimization attempt history and failure counts
└── Used by: InsertPrevOptimization() in OptimizationService

usp_upd_BookingActionsTaken        -- Update booking action results
├── Tables: BookingActionsTaken
├── Purpose: Record final optimization results and status
└── Used by: Optimization completion workflows

-- ═══════════════════════════════════════════════════════════════════════════════
-- MULTI-SUPPLIER SPECIFIC PROCEDURES
-- ═══════════════════════════════════════════════════════════════════════════════

usp_ins_multiSupplierRooms         -- Insert multi-supplier room data
├── Tables: MultiSupplierSearchRoom, MultiSupplierReservationRoom
├── Purpose: Store cross-supplier search results and room mappings
└── Used by: InsertMultiSupplierRoom() in SupplierSearchService

usp_ins_roommapping                -- Insert/update room mapping data
├── Tables: MultiSupplierRoomMapping
├── Purpose: Cache Giata room mapping results for performance
└── Used by: InsertRoomMapping() in SupplierSearchService

usp_get_multiSupplierRooms         -- Retrieve multi-supplier room data
├── Tables: MultiSupplierSearchRoom, ReservationMain, ReservationHotelInformation
├── Complex Joins: Multi-table joins with pagination support
├── Purpose: Get multi-supplier search results for display
└── Used by: GetMultiSupplierRoomDetailsResp() in reporting

-- ═══════════════════════════════════════════════════════════════════════════════
-- REPORTING PIPELINE PROCEDURES (3-LAYER ARCHITECTURE)
-- ═══════════════════════════════════════════════════════════════════════════════

usp_upd_reservationreport          -- LAYER 1: Data consolidation and currency conversion
├── Tables: ReservationMain, BookingActionsTaken, ExchangeRateData, ALL optimization tables
├── Complex Logic: Massive procedure with currency conversion, profit calculation, CP analysis
├── Output: ReservationReportDetails (consolidated optimization data)
├── Purpose: Transform raw data into standardized reporting format
└── Used by: InsertReportinTable() - CRITICAL DATA CONSOLIDATION

usp_ins_upd_tbl_vw_ResevationReports -- LAYER 2: Business logic and report classification
├── Input: ReservationReportDetails
├── Output: tbl_vw_ResevationReports
├── Complex Logic: Business rule application, report type classification
├── Purpose: Apply business intelligence and categorization
└── Used by: RefreshDbAndCachedReport() - CRITICAL BUSINESS LOGIC

usp_get_ResevationReports_V1       -- LAYER 3: API access with caching and filtering
├── Input: tbl_vw_ResevationReports
├── Complex Logic: Filtering, pagination, sorting, caching
├── Purpose: Serve GetRepricerReport API with optimized performance
└── Used by: GetReservationReport() - CRITICAL API ENDPOINT

usp_get_resevationreports          -- Alternative reporting procedure
├── Tables: Same as usp_get_ResevationReports_V1 with different logic
├── Purpose: Alternative reporting logic and filtering
└── Used by: Alternative reporting workflows

-- ═══════════════════════════════════════════════════════════════════════════════
-- CONFIGURATION AND MASTER DATA PROCEDURES
-- ═══════════════════════════════════════════════════════════════════════════════

usp_GetRePricerDataByUserID        -- Load complete client configuration
├── Tables: RePricerDetail, clientconfiguration_ExtraCriteria, IrixConfiguration
├── Purpose: Get all client settings and business rules
└── Used by: LoadRePricerDetail() - CRITICAL CONFIGURATION LOADING

usp_ins_RepricerClientConfiguration -- Insert/update client configuration
├── Tables: RePricerDetail, clientconfiguration_ExtraCriteria
├── Purpose: Manage client configuration changes
└── Used by: Client configuration management workflows

usp_ins_configuration              -- Insert/update IRIX API configuration
├── Tables: IrixConfiguration
├── Purpose: Manage API-level configuration and restrictions
└── Used by: IRIX configuration management workflows

usp_get_SupplierMaster             -- Load supplier master data
├── Tables: SupplierMaster
├── Purpose: Get supplier registry and configuration
└── Used by: Supplier management workflows

usp_get_ResellerMaster             -- Load reseller master data
├── Tables: ResellerMaster
├── Purpose: Get reseller registry and configuration
└── Used by: Reseller management workflows
```

#### **📊 VIEW ANALYSIS**

```sql
-- ═══════════════════════════════════════════════════════════════════════════════
-- CRITICAL VIEWS
-- ═══════════════════════════════════════════════════════════════════════════════

vw_ResevationReports               -- Core business intelligence view
├── Tables: ReservationMain, ReservationTable, BookingActionsTaken, clientconfiguration_ExtraCriteria
├── Complex Logic: Multi-table joins with business rule application
├── Purpose: Real-time view of optimization data with business logic
├── Performance: Indexed view with schema binding
└── Used by: usp_get_SearchByResevationIds, debugging, and analysis

-- Note: This view is the foundation for the materialized table tbl_vw_ResevationReports
-- The stored procedure usp_ins_upd_tbl_vw_ResevationReports essentially materializes this view
```

#### **Tables & Procedures Used in _1_PrebookAndOptimize_MultiSupplier**

```sql
-- ADDITIONAL INPUT SOURCES (Multi-Supplier Specific)
1. GetAllowedProviders() → IrixConfiguration table
   └── AllowedProvidersForOptimization (supplier whitelist)

2. GetPreBookCriteriaDBAll() → Enhanced version with multi-supplier data
   ├── All same-supplier sources PLUS
   ├── MultiSupplierSearchRoom (cross-supplier room data)
   ├── MultiSupplierReservationRoom (room mappings)
   └── ReservationPrice (pricing data)

3. GiataApiCall() → MongoDB collections
   ├── GiataRoomMapping (room equivalency)
   ├── GiataBoardMapping (meal plan mapping)
   └── GiataHotelMapping (property mapping)

-- MULTI-SUPPLIER PROCESSING (WRITE OPERATIONS)
4. InsertMultiSupplierRoom() → usp_ins_multiSupplierRooms
   ├── INSERT INTO MultiSupplierSearchRoom (search results)
   └── INSERT INTO MultiSupplierReservationRoom (room mappings)

5. InsertRoomMapping() → usp_ins_roommapping
   ├── INSERT INTO MultiSupplierRoomMapping (Giata mappings)
   └── UPDATE existing mappings with new data

-- ENHANCED LOGGING (Multi-Supplier Specific)
6. InsertPreBookReservationlogSupplier() → usp_Ins_PrebookSupplierlog_V1
   ├── INSERT INTO PreBookSupplierLog (multi-supplier attempts)
   └── Enhanced tracking vs same-supplier logs
```

#### **Real Data Flow Example: Same Supplier Optimization**

```
STEP 1: Data Loading (READ Phase)
┌─────────────────────────────────────────────────────────────────┐
│ GetReservationsAsync(repricerId=12)                             │
│ ├── usp_get_CreateSearch executes                               │
│ ├── Returns: ReservationId=12345, supplierName="Booking.com"   │
│ ├── CheckIn=2024-03-15, Price=€450, Currency=EUR               │
│ └── Status=OK, CancellationDate=2024-03-10                     │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│ GetReservationsRoomAsync(repricerId=12)                         │
│ ├── usp_get_RoomInfo executes                                   │
│ ├── Returns: RoomName="Superior Double", RoomBoard="BB"        │
│ ├── PassengerCount=2, ChildAges=null                           │
│ └── RoomInfo="Sea view, balcony"                               │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│ GetCancellationPolicyReservationIdsAsync(repricerId=12)        │
│ ├── usp_get_CancellationPolicy executes                        │
│ ├── Returns: CancellationDate=2024-03-10                       │
│ ├── CancellationCharge=€50, Currency=EUR                       │
│ └── CancellationType="Flexible"                                │
└─────────────────────────────────────────────────────────────────┘

STEP 2: IRIX API Search (EXTERNAL)
┌─────────────────────────────────────────────────────────────────┐
│ SearchForBooking() → IRIX API                                   │
│ ├── Criteria: Hotel, Dates, Rooms, Supplier="Booking.com"      │
│ ├── Returns: OfferPrice=€420, AvailabilityToken="ABC123"       │
│ ├── CancellationPolicy: Date=2024-03-10, Charge=€50           │
│ └── RoomMatch: "Superior Double BB" ✓                          │
└─────────────────────────────────────────────────────────────────┘

STEP 3: Initial Validation (PRE-PREBOOK)
┌─────────────────────────────────────────────────────────────────┐
│ Price Validation: €450 - €420 = €30 profit ✓                  │
│ Threshold Check: €30 >= €5 (global minimum) ✓                 │
│ CP Validation: Same dates, same charges = "loose" ✓            │
│ Supplier Match: "Booking.com" == "Booking.com" ✓               │
└─────────────────────────────────────────────────────────────────┘
                                ↓
STEP 4: IRIX API Prebook Creation
┌─────────────────────────────────────────────────────────────────┐
│ Prebookresponse() → IRIX API                                    │
│ ├── Creates temporary booking hold with IRIX                    │
│ ├── Returns: PrebookPrice=€420, AvailabilityToken="XYZ789"     │
│ ├── Confirms: Room available, price locked for optimization    │
│ └── Status: Prebook successful, ready for validation           │
└─────────────────────────────────────────────────────────────────┘
                                ↓
STEP 5: Post-Prebook Validation & Candidate Selection
┌─────────────────────────────────────────────────────────────────┐
│ Final Validation Against Configuration:                         │
│ ├── Re-validate profit after actual prebook price              │
│ ├── Confirm cancellation policy compatibility                  │
│ ├── Check client-specific business rules                       │
│ └── Determine if candidate qualifies for optimization          │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│ InsertPreBookReservation() → usp_Ins_Prebooklog_V1             │
│ ├── INSERT ReservationTable: SELECTED CANDIDATES FOR REBOOKING │
│ │   ├── ReservationId=12345, PreBookPrice=€420, Profit=€30     │
│ │   ├── CPStatus="loose", AvailabilityToken="XYZ789"           │
│ │   ├── CreateDate=NOW(), PreBookCount=1                       │
│ │   └── Status: QUALIFIED FOR OPTIMIZATION                     │
│ ├── INSERT PreBook_CancellationPolicy: CP analysis             │
│ ├── INSERT PreBook_PackageRooms: Room details                  │
│ └── INSERT PreBook_ClientConfiguration: Applied rules          │
└─────────────────────────────────────────────────────────────────┘

STEP 6: Dry Run Phase (OPTIMIZATION VALIDATION)
┌─────────────────────────────────────────────────────────────────┐
│ _2_DryRunOptimizationApiIRIX() → IRIX API                      │
│ ├── Tests optimization feasibility without execution           │
│ ├── Returns: ExpectedGain=€28, Status="feasible"               │
│ ├── Validates: Final pricing, availability, restrictions       │
│ └── Decision: Proceed to optimization if gain > threshold      │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│ Optimization Conditions Check:                                  │
│ ├── OptimizationType=Automatic ✓                               │
│ ├── CPStatus="loose" ✓                                         │
│ ├── DryRun ExpectedGain=€28 >= threshold ✓                     │
│ └── IsOptimizable=true (IRIX API) ✓                            │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│ _3_OptimizeBookingApiIRIX() → IRIX API                         │
│ ├── Executes final booking optimization from ReservationTable  │
│ ├── Uses AvailabilityToken from selected candidate             │
│ ├── Returns: NewReservationId=67890, ActualGain=€28            │
│ └── Status: "optimized", OptimizedBy=system                    │
└─────────────────────────────────────────────────────────────────┘
                                ↓
STEP 8: Booking Action Recording
┌─────────────────────────────────────────────────────────────────┐
│ INSERT BookingActionsTaken (FINAL OPTIMIZATION RESULT)         │
│ ├── RepricerId=12, ReservationId=12345 (original)              │
│ ├── ActionId=1 (Optimized), NewBookingId=67890 (new booking)   │
│ ├── NewBookingPrice=€28 (actual profit), Status="Success"      │
│ ├── OptimizationMethod="SameSupplier", CreatedOn=NOW()         │
│ └── Links original reservation to new optimized booking        │
└─────────────────────────────────────────────────────────────────┘

STEP 9: Data Pipeline Refresh (REPORTING)
┌─────────────────────────────────────────────────────────────────┐
│ RefreshDbAndCachedReport(repricerId=12, reservationId=12345)   │
│ ├── EXEC usp_upd_reservationreport                             │
│ │   ├── Consolidates ReservationMain + BookingActionsTaken     │
│ │   ├── Applies currency conversion to EUR                     │
│ │   └── Updates ReservationReportDetails                       │
│ ├── EXEC usp_ins_upd_tbl_vw_ResevationReports                  │
│ │   ├── Applies business logic classification                  │
│ │   ├── Sets ReportType="Optimized"                           │
│ │   └── Updates tbl_vw_ResevationReports                       │
│ └── Cache refresh for API endpoints                            │
└─────────────────────────────────────────────────────────────────┘
```

#### **Key Differences: Multi-Supplier vs Same Supplier**

```sql
-- SAME SUPPLIER FLOW
Supplier Validation: reservationSupplier == prebookSupplier (exact match)
Room Matching: Direct token matching (faster)
Giata Usage: None (same supplier = same room codes)
Execution: Automatic if conditions met
Tables: Standard optimization tables only

-- MULTI-SUPPLIER FLOW
Supplier Validation: prebookSupplier IN AllowedProvidersForOptimization
Room Matching: Giata API mapping (complex)
Giata Usage: Extensive (room/board/hotel mapping)
Execution: Always requires manual approval
Tables: Additional MultiSupplier* tables + MongoDB collections

-- ADDITIONAL MULTI-SUPPLIER TABLES
MultiSupplierSearchRoom: Cross-supplier search results
MultiSupplierReservationRoom: Room mapping relationships
MultiSupplierRoomMapping: Giata mapping cache
PreBookSupplierLog: Enhanced logging for multi-supplier attempts
```

#### **🎯 Critical Understanding: ReservationTable Role**

```sql
-- IMPORTANT: ReservationTable is NOT a source table
-- It contains SELECTED CANDIDATES FOR REBOOKING after validation

SEQUENCE:
1. ReservationMain → Source reservations from client systems
2. IRIX API Search → Find potential better offers
3. IRIX API Prebook → Create temporary booking holds
4. Validation → Check profit thresholds, CP compatibility, business rules
5. ReservationTable ← INSERT qualified candidates for optimization
6. Dry Run → Test optimization feasibility
7. Optimization → Execute final rebooking
8. BookingActionsTaken ← Record final results

-- ReservationTable Purpose:
├── Stores prebook attempts that passed all validations
├── Contains AvailabilityTokens for optimization execution
├── Tracks PreBookCount (attempt numbers)
├── Holds calculated Profit and CPStatus
└── Serves as input for dry run and optimization phases

-- Key Fields in ReservationTable:
├── ReservationId (links to original ReservationMain)
├── PreBookPrice (validated price from IRIX prebook)
├── Profit (calculated savings amount)
├── CPStatus ("loose", "tight", "CancellationChargesApplicable")
├── AvailabilityToken (IRIX token for optimization)
├── PreBookCount (attempt sequence number)
└── CreateDate (when candidate was qualified)
```

---

### 🚨 CRITICAL DEVELOPMENT GUIDELINES

#### **⚠️ MANDATORY REQUIREMENTS FOR ANY CODE CHANGES**

```
🔴 STOP: This application implements a dual-architecture approach with business
logic distributed between database stored procedures and application code.
Both layers contain critical decision-making logic that must be understood
in detail before making any modifications.
```

#### **1. Code Analysis Protocol (REQUIRED BEFORE ANY CHANGES)**

```sql
-- STEP 1: Complete Method Analysis
├── Read EVERY line of code in the target method/procedure
├── Trace complete data flow: input tables → transformations → output tables
├── Identify ALL conditional logic branches and business rule implications
└── Map ALL database interactions (reads, writes, updates) and sequence dependencies

-- STEP 2: Database Procedure Understanding
├── Analyze complete stored procedure logic, not just the signature
├── Understand 3-layer architecture:
│   ├── Layer 1: usp_upd_reservationreport (data consolidation)
│   ├── Layer 2: usp_ins_upd_tbl_vw_ResevationReports (business logic)
│   └── Layer 3: usp_get_ResevationReports_V1 (API access)
├── Identify ALL table dependencies and join conditions
└── Understand currency conversion logic and business rule applications

-- STEP 3: Flow Integrity Verification
├── Maintain exact sequence: Search → Prebook → Validation → ReservationTable → Dry Run → Optimization
├── Preserve ALL validation checkpoints and their order
├── Ensure new code follows existing error handling patterns
└── Maintain consistency with parallel processing and locking mechanisms
```

#### **2. Risk Mitigation Requirements**

```
🔥 HIGH-RISK AREAS (Extreme Caution Required):
├── ReservationTable population logic (only after successful IRIX prebook + validation)
├── Same Supplier vs Multi-Supplier flow differences
├── 3-layer reporting pipeline triggers
├── Currency conversion and profit calculations
├── IRIX API integration points
└── Parallel processing and Redis locking mechanisms

⚡ TESTING REQUIREMENTS:
├── Test against COMPLETE optimization workflow, not isolated components
├── Verify Same Supplier AND Multi-Supplier flows
├── Validate reporting pipeline triggers correctly
├── Confirm data consistency across all tables
└── Test with actual IRIX API integration (not mocks)
```

#### **3. Code Placement Rules**

```csharp
// CRITICAL: Maintain exact operation sequence
// ❌ NEVER insert code that breaks this flow:

1. GetReservationsAsync() → Load source data
2. IRIX SearchForBooking() → Find offers
3. IRIX Prebookresponse() → Create holds
4. Validation Logic → Check all rules
5. InsertPreBookReservation() → Populate ReservationTable
6. _2_DryRunOptimizationApiIRIX() → Test feasibility
7. _3_OptimizeBookingApiIRIX() → Execute optimization
8. BookingActionsTaken → Record results
9. RefreshDbAndCachedReport() → Update reporting

// ✅ ALWAYS preserve validation checkpoints:
├── Price threshold validation
├── Cancellation policy compatibility
├── Supplier matching rules
├── Business rule compliance
└── IRIX API response validation
```

#### **4. Failure Consequences**

```
🚫 FAILURE TO FOLLOW THESE GUIDELINES WILL RESULT IN:
├── Broken optimization workflows
├── Incorrect profit calculations
├── Data inconsistencies between tables
├── Failed integrations with IRIX API
├── Corrupted reporting pipeline
├── Revenue loss due to failed optimizations
└── Client billing discrepancies
```

#### **5. Pre-Development Checklist**

```
✅ BEFORE WRITING ANY CODE, PROVIDE:
├── Detailed analysis of existing logic in target methods
├── Complete list of all affected components and tables
├── Explanation of how changes preserve workflow integrity
├── Risk assessment of potential breaking changes
├── Testing strategy for both Same Supplier and Multi-Supplier flows
└── Rollback plan if changes cause issues
```

#### **6. Architecture-Specific Warnings**

```sql
-- DATABASE LAYER WARNINGS
⚠️  usp_upd_reservationreport: Contains complex currency conversion logic
⚠️  usp_ins_upd_tbl_vw_ResevationReports: Business rule classification engine
⚠️  ReservationTable: Only populated after successful validation
⚠️  BookingActionsTaken: Final source of truth for optimizations

-- APPLICATION LAYER WARNINGS
⚠️  SearchService._1_PrebookAndOptimize_SameSupplier_Automatic: Core same-supplier logic
⚠️  SupplierSearchService._1_PrebookAndOptimize_MultiSupplier: Complex multi-supplier flow
⚠️  Parallel.ForEach processing: Redis locking and race condition management
⚠️  IRIX API calls: Rate limiting and error handling critical
```

---

### ✅ COMPREHENSIVE DATABASE COVERAGE VERIFICATION

#### **📊 Complete Object Inventory Summary**

```
TABLES COVERED: 35+ core tables including:
├── 6 Core Data Tables (ReservationMain, ReservationRoom, etc.)
├── 8 Processing Tables (ReservationTable, BookingActionsTaken, etc.)
├── 4 Multi-Supplier Tables (MultiSupplierSearchRoom, etc.)
├── 4 Optimization Tracking Tables (DryRunOptimization, etc.)
├── 2 Reporting Tables (ReservationReportDetails, tbl_vw_ResevationReports)
├── 3 Configuration Tables (RePricerDetail, clientconfiguration_ExtraCriteria, etc.)
├── 5 Search Sync Tables (SearchSync_Hotel, SupplierSearchSync_Hotel, etc.)
├── 3 Logging Tables (ReservationTableLog, PreBookLog, etc.)
└── 3 Master Data Tables (SupplierMaster, ResellerMaster, etc.)

STORED PROCEDURES COVERED: 25+ procedures including:
├── 8 Core Data Retrieval Procedures (usp_get_CreateSearch, etc.)
├── 6 Prebook/Optimization Procedures (usp_Ins_Prebooklog_V1, etc.)
├── 3 Multi-Supplier Procedures (usp_ins_multiSupplierRooms, etc.)
├── 3 Reporting Pipeline Procedures (3-layer architecture)
├── 5 Configuration Procedures (usp_GetRePricerDataByUserID, etc.)

VIEWS COVERED: 1 critical view:
└── vw_ResevationReports (foundation for materialized reporting)

MONGODB COLLECTIONS: 3 Giata collections:
├── GiataRoomMapping (room equivalency)
├── GiataBoardMapping (meal plan mapping)
└── GiataHotelMapping (property mapping)
```

#### **🎯 Data Flow Coverage Verification**

```
✅ SAME SUPPLIER FLOW (_1_PrebookAndOptimize_SameSupplier_Automatic):
├── ✅ All input tables and procedures identified
├── ✅ All processing operations mapped
├── ✅ All output tables and reporting pipeline covered
└── ✅ Complete sequence from source data to final reporting

✅ MULTI-SUPPLIER FLOW (_1_PrebookAndOptimize_MultiSupplier):
├── ✅ All additional multi-supplier tables identified
├── ✅ All Giata integration points covered
├── ✅ All enhanced logging mechanisms mapped
└── ✅ Complete cross-supplier workflow documented

✅ REPORTING PIPELINE (3-Layer Architecture):
├── ✅ Layer 1: usp_upd_reservationreport (data consolidation)
├── ✅ Layer 2: usp_ins_upd_tbl_vw_ResevationReports (business logic)
└── ✅ Layer 3: usp_get_ResevationReports_V1 (API access)

✅ OPTIMIZATION TRACKING:
├── ✅ All dry run and optimization execution tables covered
├── ✅ All status tracking and history tables identified
└── ✅ Complete audit trail from attempt to completion

✅ CONFIGURATION SYSTEM:
├── ✅ All client configuration tables and procedures covered
├── ✅ All business rule and threshold mechanisms identified
└── ✅ Complete decision-making infrastructure documented
```

**This comprehensive database object inventory ensures complete understanding of ALL data flows, table relationships, and stored procedure interactions in both optimization methods without any gaps or missing components.**
